import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, inject, input, computed, type OnInit } from '@angular/core';
import { ISearchStudentGroupsDto, IStudentGroupDto, IStudentGroupStatusEnum, GeneralService, EnumDropdownOptionsService, GroupDialogState, IStudentLevelEnum } from 'SharedModules.Library';
import { AvatarModule } from 'primeng/avatar';
import { AvatarGroup, AvatarGroupModule } from 'primeng/avatargroup';
import { ButtonModule } from 'primeng/button';
import { TagModule } from 'primeng/tag';
import { StudentGroupSelectionDialogComponent } from '@platform.src/app/shared/components/dialogs/student-group-selection-dialog/student-group-selection-dialog.component';

@Component({
    selector: 'app-group-mini-info-card',
    imports: [
      CommonModule,
      AvatarGroupModule,
      AvatarModule,
      ButtonModule,
      TagModule
    ],
    templateUrl: './group-mini-info-card.component.html',
    styleUrl: './group-mini-info-card.component.scss',
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class GroupMiniInfoCardComponent implements OnInit {
  generalService = inject(GeneralService);
  enumDropdownService = inject(EnumDropdownOptionsService);

  // Accept ISearchStudentGroupsDto instead of IStudentGroupDto
  item = input.required<ISearchStudentGroupsDto>();

  // Computed properties for better UX
  groupDisplayName = computed(() => {
    const group = this.item();

    // If group name exists and is not empty, use it with language level
    if (group.groupName && group.groupName.trim() !== '') {
      const levelText = this.enumDropdownService.getLanguageLevelDisplayLabel(group.groupLevel, true);
      return levelText ? `${group.groupName} (${levelText})` : group.groupName;
    }

    // Fallback: use teaching language name + language level
    const languageName = group.teachingLanguageName || 'Language Group';
    const levelText = this.enumDropdownService.getLanguageLevelDisplayLabel(group.groupLevel, true);

    // Create a clean display name
    if (levelText && levelText !== 'Mixed Levels') {
      return `${languageName} ${levelText}`;
    } else if (levelText === 'Mixed Levels') {
      return `${languageName} Group`;
    } else {
      return `${languageName} Group`;
    }
  });

  languageName = computed(() => this.item().teachingLanguageName);

  studentsCount = computed(() => this.item().studentsCount);

  groupStatusText = computed(() => {
    const status = this.item().groupStatus;
    return this.generalService.getEnumDisplayText(status, IStudentGroupStatusEnum);
  });

  groupStatusSeverity = computed(() => {
    const status = this.item().groupStatus;
    // Map status to PrimeNG tag severity based on IStudentGroupStatusEnum
    switch (status) {
      case IStudentGroupStatusEnum.Active: return 'success'; // Active
      case IStudentGroupStatusEnum.Deleted: return 'danger'; // Deleted
      default: return 'info';
    }
  });

  activePackagesCount = computed(() => this.item().numberOfActivePackages);
  inactivePackagesCount = computed(() => this.item().numberOfInActivePackages);

  ngOnInit(): void { }

  onSelectListGroupItem() {
    const group = this.item();
    if (group) {
      // Convert ISearchStudentGroupsDto to IStudentGroupDto for the dialog
      // const studentGroupDto: ISearchStudentGroupsDto = group as IStudentGroupDto;

      const params = {
        'editMode': false,
        groupId: group.id, 
        state: GroupDialogState.ViewGroup
      };
      this.generalService.openComponent(StudentGroupSelectionDialogComponent, params);
    }
  }
}
