Stack trace:
Frame         Function      Args
0007FFFFBBB0  00021005FEBA (000210285F48, 00021026AB6E, 0007FFFFBBB0, 0007FFFFAAB0) msys-2.0.dll+0x1FEBA
0007FFFFBBB0  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFBE88) msys-2.0.dll+0x67F9
0007FFFFBBB0  000210046832 (000210285FF9, 0007FFFFBA68, 0007FFFFBBB0, 000000000000) msys-2.0.dll+0x6832
0007FFFFBBB0  000210068F86 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28F86
0007FFFFBBB0  0002100690B4 (0007FFFFBBC0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x290B4
0007FFFFBE90  00021006A49D (0007FFFFBBC0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A49D
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFAAA670000 ntdll.dll
7FFAAA560000 KERNEL32.DLL
7FFAA8300000 KERNELBASE.dll
7FFAAA280000 USER32.dll
7FFAA8100000 win32u.dll
000210040000 msys-2.0.dll
7FFAAA530000 GDI32.dll
7FFAA8130000 gdi32full.dll
7FFAA8600000 msvcp_win.dll
7FFAA7EF0000 ucrtbase.dll
7FFAAA480000 advapi32.dll
7FFAA8DB0000 msvcrt.dll
7FFAA9CC0000 sechost.dll
7FFAA8BC0000 RPCRT4.dll
7FFAA7FF0000 bcrypt.dll
7FFAA7500000 CRYPTBASE.DLL
7FFAA8020000 bcryptPrimitives.dll
7FFAA9D60000 IMM32.DLL
