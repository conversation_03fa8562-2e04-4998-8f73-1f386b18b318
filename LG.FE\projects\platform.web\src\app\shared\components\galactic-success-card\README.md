# Galactic Success Card Component

A reusable, customizable success card component with galactic/space-themed styling and animations.

## Features

- 🎨 **Multiple Themes**: Success, Primary, Warning, Info
- 📏 **Size Variants**: Small, Medium, Large
- ✨ **Animations**: Optional cosmic animations and effects
- 🎯 **Content Projection**: Flexible action button placement
- 📱 **Responsive**: Mobile-friendly design with PrimeFlex
- 🎭 **Customizable**: Extensive configuration options

## Basic Usage

```typescript
import { GalacticSuccessCardComponent, type GalacticSuccessConfig } from './galactic-success-card.component';

@Component({
  imports: [GalacticSuccessCardComponent]
})
export class MyComponent {
  config: GalacticSuccessConfig = {
    title: 'Success!',
    subtitle: 'Your action was completed successfully',
    badgeText: 'COMPLETED',
    badgeIcon: 'pi pi-check',
    equipmentItems: ['Feature 1', 'Feature 2'],
    theme: 'success'
  };
}
```

```html
<app-galactic-success-card [config]="config">
  <div slot="primary-actions">
    <p-button label="Continue" icon="pi pi-arrow-right"></p-button>
  </div>
</app-galactic-success-card>
```

## Configuration Interface

```typescript
interface GalacticSuccessConfig {
  title: string;                    // Main title text
  subtitle: string;                 // Subtitle text
  badgeText: string;               // Badge text (e.g., "FREE", "PREMIUM")
  badgeIcon?: string;              // Badge icon class (default: pi pi-star-fill)
  equipmentItems: string[];        // Array of feature/equipment items
  showDestinationName?: boolean;   // Show destination name section
  destinationName?: string;        // Destination name text
  customIconClass?: string;        // Custom icon class for main icon
  theme?: 'success' | 'primary' | 'warning' | 'info'; // Color theme
}
```

## Input Properties

| Property | Type | Default | Description |
|----------|------|---------|-------------|
| `config` | `GalacticSuccessConfig` | Required | Main configuration object |
| `showOrbitalRing` | `boolean` | `true` | Show animated orbital ring |
| `showCosmicGlow` | `boolean` | `true` | Enable cosmic glow effects |
| `customClasses` | `string` | `''` | Additional CSS classes |
| `enableAnimations` | `boolean` | `true` | Enable/disable animations |
| `size` | `'small' \| 'medium' \| 'large'` | `'medium'` | Component size |

## Content Projection Slots

### Primary Actions
```html
<div slot="primary-actions">
  <!-- Main action buttons -->
</div>
```

### Secondary Actions
```html
<div slot="secondary-actions">
  <!-- Secondary/text buttons -->
</div>
```

### Additional Content
```html
<div slot="additional-content">
  <!-- Any additional content -->
</div>
```

## Theme Examples

### Success Theme
```typescript
config = {
  title: 'Trial Confirmed!',
  subtitle: 'Your learning journey begins',
  badgeText: 'FREE',
  equipmentItems: ['Lessons', 'Progress', 'Support'],
  theme: 'success'
};
```

### Primary Theme
```typescript
config = {
  title: 'Welcome!',
  subtitle: 'Ready to get started?',
  badgeText: 'PREMIUM',
  equipmentItems: ['Advanced Features'],
  theme: 'primary'
};
```

### Warning Theme
```typescript
config = {
  title: 'Action Required',
  subtitle: 'Please complete your profile',
  badgeText: 'PENDING',
  equipmentItems: ['Profile Setup'],
  theme: 'warning'
};
```

## Size Variants

- **Small**: Compact version for sidebars or cards
- **Medium**: Default size for most use cases
- **Large**: Prominent display for main success pages

## Animations

The component includes several cosmic-themed animations:
- Success bounce animation for the main icon
- Orbital ring rotation
- Stellar pulse for badge icon
- Slide-up animations for content
- Cosmic shimmer effects

Disable animations by setting `enableAnimations="false"`.

## Responsive Design

The component uses mixins from the shared-modules library for responsive breakpoints:
- Mobile-optimized sizing and spacing
- Flexible button layouts
- Responsive typography

## Styling Customization

Override CSS custom properties for theme colors:
```scss
app-galactic-success-card {
  --theme-primary: #your-color;
  --theme-primary-50: #your-light-color;
  // ... other theme variables
}
```

## Dependencies

- PrimeNG (for icons)
- PrimeFlex (for responsive utilities)
- SharedModules.Library mixins (for breakpoints)
