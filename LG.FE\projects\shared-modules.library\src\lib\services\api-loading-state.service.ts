import { Injectable, signal, computed, OnDestroy } from '@angular/core';

@Injectable({
  providedIn: 'root'
})
export class ApiLoadingStateService implements OnDestroy {
  private activeRequestCount = signal(0);
  private requestIds = new Set<string>(); // Track unique request IDs
  public isLoading = computed(() => this.activeRequestCount() > 0);

  // Store the bound function reference for proper cleanup
  private boundResetHandler = this.reset.bind(this);

  constructor() {
    // Reset count on initialization
    this.reset();
    // Listen for page reloads or navigation events if needed
    window.addEventListener('beforeunload', this.boundResetHandler);
    window.addEventListener('load', this.boundResetHandler);

    // Add global debug method for development
    if (typeof window !== 'undefined') {
      (window as any).debugApiLoadingState = () => this.debugState();
      (window as any).fixApiLoadingState = () => this.validateAndFix();
      (window as any).resetApiLoadingState = () => this.reset();
    }
  }

  /**
   * Begin a new request with optional unique ID for tracking
   */
  beginRequest(requestId?: string): void {
    if (requestId) {
      // Prevent duplicate tracking of the same request
      if (this.requestIds.has(requestId)) {
        console.warn(`Request ${requestId} is already being tracked`);
        return;
      }
      this.requestIds.add(requestId);
    }

    this.activeRequestCount.update(count => {
      const newCount = count + 1;
      console.log(`🚀 API Request started. Active requests: ${newCount}${requestId ? ` (ID: ${requestId})` : ''}`);
      return newCount;
    });
  }

  /**
   * End a request with optional unique ID for tracking
   */
  endRequest(requestId?: string): void {
    if (requestId) {
      // Only decrement if this request was actually tracked
      if (!this.requestIds.has(requestId)) {
        console.warn(`Request ${requestId} was not being tracked or already completed`);
        return;
      }
      this.requestIds.delete(requestId);
    }

    this.activeRequestCount.update(count => {
      const newCount = Math.max(0, count - 1); // Ensure count never goes below 0
      console.log(`✅ API Request completed. Active requests: ${newCount}${requestId ? ` (ID: ${requestId})` : ''}`);

      // Safety check: if count reaches 0 but we still have tracked requests, log warning
      if (newCount === 0 && this.requestIds.size > 0) {
        console.warn('Request count is 0 but tracked requests still exist:', Array.from(this.requestIds));
        this.requestIds.clear(); // Clear to prevent memory leaks
      }

      return newCount;
    });
  }

  /**
   * Get current loading state
   */
  getIsLoading(): boolean {
    return this.isLoading();
  }

  /**
   * Get current active request count (for debugging)
   */
  getActiveRequestCount(): number {
    return this.activeRequestCount();
  }

  /**
   * Get tracked request IDs (for debugging)
   */
  getTrackedRequestIds(): string[] {
    return Array.from(this.requestIds);
  }

  /**
   * Reset all request tracking
   */
  reset(): void {
    const previousCount = this.activeRequestCount();
    this.activeRequestCount.set(0);
    this.requestIds.clear();

    if (previousCount > 0) {
      console.log(`🔄 API Loading state reset. Previous count: ${previousCount}`);
    }
  }

  /**
   * Force end a specific request by ID (for error recovery)
   */
  forceEndRequest(requestId: string): void {
    if (this.requestIds.has(requestId)) {
      console.log(`🔧 Force ending request: ${requestId}`);
      this.endRequest(requestId);
    }
  }

  /**
   * Debug method to log current state
   */
  debugState(): void {
    console.log('🔍 API Loading State Debug:', {
      activeRequestCount: this.activeRequestCount(),
      isLoading: this.isLoading(),
      trackedRequestIds: Array.from(this.requestIds),
      trackedRequestCount: this.requestIds.size
    });
  }

  /**
   * Check for inconsistencies and auto-fix them
   */
  validateAndFix(): void {
    const count = this.activeRequestCount();
    const trackedCount = this.requestIds.size;

    if (count !== trackedCount) {
      console.warn(`⚠️ Inconsistency detected: count=${count}, tracked=${trackedCount}`);

      // Auto-fix: sync the count with tracked requests
      this.activeRequestCount.set(trackedCount);
      console.log(`🔧 Auto-fixed: set count to ${trackedCount}`);
    }

    // If count is 0 but we have tracked requests, clear them
    if (count === 0 && trackedCount > 0) {
      console.warn('⚠️ Count is 0 but tracked requests exist, clearing tracked requests');
      this.requestIds.clear();
    }

    // If count > 0 but no tracked requests, reset count
    if (count > 0 && trackedCount === 0) {
      console.warn('⚠️ Count > 0 but no tracked requests, resetting count');
      this.activeRequestCount.set(0);
    }
  }

  ngOnDestroy(): void {
    // Clean up event listeners with proper function references
    window.removeEventListener('beforeunload', this.boundResetHandler);
    window.removeEventListener('load', this.boundResetHandler);

    // Clear all tracking
    this.reset();
  }
}