<!-- teachers-list.component.html -->
<div class="teachers-page-container">
  <!-- Main Content (Table & Applied Filters) -->

  <div class="main-content">
    <!-- Applied Filters Section - New Reusable Component -->
    <app-applied-filters-tags
      [filters]="appliedFilters()"
      (filterRemoved)="onAppliedFilterRemove($event)"
      (clearAllClicked)="onAppliedFiltersClearAll($event)">
           <p-button extraButton
                icon="pi pi-filter"
                label="Filters"
                severity="secondary"
                (click)="openFiltersDrawer()"
                class=""
                styleClass="p-button-sm"
                [badge]="appliedFiltersCount() > 0 ? appliedFiltersCount().toString() : undefined"
                badgeClass="p-badge-info" />
    </app-applied-filters-tags>

    <!-- Filters Drawer Sidebar -->
    <app-filters-drawer-sidebar
      #filtersDrawer
      [(visible)]="isFiltersDrawerVisible"
      [config]="filtersDrawerConfig"
      [filterContentTemplate]="filterContentTemplate"
      (actionClicked)="onFiltersDrawerAction($event)">
    </app-filters-drawer-sidebar>

    <!-- Filter Content Template -->
    <ng-template #filterContentTemplate>
      <app-teachers-list-filters
        #teachersFilters
        [filterState]="filterState()"
        (filterChanged)="onFilterChange($event)"
        (filterAction)="onFilterAction($event)">
      </app-teachers-list-filters>
    </ng-template>

    <!-- Table Container -->
    <div class="table-container surface-card p-3 border-round">
      <p-table #dt [value]="dataResponse()?.pageData || []" [lazy]="true" [paginator]="true"
        [rows]="queryParams().pageSize" [totalRecords]="totalRecords()" [loading]="isLoading()"
        [rowsPerPageOptions]="rowsPerPageOptions()" [showCurrentPageReport]="true"
        [sortField]="queryParams().sortColumn" [sortOrder]="queryParams().sortDirection === 'asc' ? 1 : -1"
        [reorderableColumns]="true" currentPageReportTemplate="Showing {first} to {last} of {totalRecords} teachers"
        (onPage)="onPageChange($event)" (onSort)="onSortChange($event)" (onColReorder)="onColumnReorder($event)"
        [scrollable]="true" scrollHeight="flex"
        [resizableColumns]="true" showGridlines stripedRows
        [tableStyle]="{'min-width': '1000px; min-height: calc(100% + 300px)'}" [columns]="selectedColumns()">

        <ng-template pTemplate="caption">
          <div class="flex flex-column md:flex-row justify-content-between align-items-center p-0 gap-4">
            <div class="flex-1">
              <div class="field" style="width: 300px">
                <div class="search-input-container">
                  <input type="text" pInputText
                    id="searchTerm"
                    type="text"
                    styleClass="w-full"
                    [value]="queryParams().searchTerm || ''"
                    (input)="updateSearchTerm($event)"
                    placeholder="Search by name or email" />
                  <button *ngIf="queryParams().searchTerm" class="search-clear-button" type="button"
                    (click)="clearSearchTerm()">
                    <i class="pi pi-times"></i>
                  </button>
                </div>
              </div>
            </div>
            <div class="flex align-items-center gap-3">

              <p-multiSelect [options]="visibleColumns()" [ngModel]="selectedColumns()" (ngModelChange)="onColumnsChange($event)"
                [filter]="true" optionLabel="header" placeholder="Choose Columns"
                [selectedItemsLabel]="'{0}/' + visibleColumns().length + ' columns shown'" scrollHeight="400px" />
              <p-button icon="pi pi-download" label="Export" severity="secondary" (click)="exportTable()"
                class="p-button-sm" />
            </div>
          </div>


        </ng-template>

        <ng-template pTemplate="header">

        <!-- Table Header -->
        <app-data-grid-header-footer
          [columns]="selectedColumns()"
          [sortable]="true"
          [reorderable]="true"
          [showHeader]="true"
          [showFooter]="false"
          [showActionsColumn]="true"
          actionsColumnHeader="Actions"
          actionsColumnWidth="100px">
        </app-data-grid-header-footer>
        </ng-template>

        <!-- Table Body -->
        <ng-template pTemplate="body" let-teacher let-index="rowIndex">
          <tr [pReorderableRow]="index">

            <td alignFrozen="left" pFrozenColumn>
              <div class="flex justify-content-center">
                <button (click)="goToTeacherOverview(teacher.id)" pButton icon="pi pi-eye" class="p-button-text p-button-sm"></button>
              </div>
            </td>

            <td *ngFor="let col of selectedColumns()">
              <ng-container [ngSwitch]="col.field">
                <!-- Formatted dates -->
                <ng-container *ngSwitchCase="searchTeacherDtoFieldNames.approvedDate">
                  <div class="multiline"
                    [attr.style]="col.maxWidth !== undefined ? 'max-width:' + col.maxWidth + ' !important; width:max-content !important; ' : 'width:max-content !important;'">
                    {{ teacher.approvedDate | date: 'mediumDate' }}
                  </div>
                </ng-container>

                <!-- Countries -->
                <ng-container *ngSwitchCase="searchTeacherDtoFieldNames.country">
                  <div class="multiline"
                    [attr.style]="col.maxWidth !== undefined ? 'max-width:' + col.maxWidth + ' !important; width:max-content !important; ' : 'width:max-content !important;'">
                    {{ teacher[col.field] }}
                  </div>
                </ng-container>

                <!-- Gender -->
                <ng-container *ngSwitchCase="searchTeacherDtoFieldNames.gender">
                  <div class="multiline"
                    [attr.style]="col.maxWidth !== undefined ? 'max-width:' + col.maxWidth + ' !important; width:max-content !important; ' : 'width:max-content !important;'">
                    {{ teacher.gender }}
                  </div>
                </ng-container>

                <!-- Languages lists -->
                <ng-container *ngSwitchCase="searchTeacherDtoFieldNames.speakingLanguages">
                  <div class="multiline"
                    [attr.style]="col.maxWidth !== undefined ? 'max-width:' + col.maxWidth + ' !important; width:max-content !important; ' : 'width:max-content !important;'">
                    <div class="language-list">
                      <span *ngFor="let language of teacher.speakingLanguages" class="language-tag">
                        {{ language.language }}
                      </span>
                    </div>
                  </div>
                </ng-container>

                <ng-container *ngSwitchCase="searchTeacherDtoFieldNames.teacherTeachingLanguages">
                  <div class="multiline"
                    [attr.style]="col.maxWidth !== undefined ? 'max-width:' + col.maxWidth + ' !important; width:max-content !important; ' : 'width:max-content !important;'">
                    <div class="language-list">
                      <span *ngFor="let language of teacher.teacherTeachingLanguages" class="language-tag">
                        {{ language.teachingLanguageName }}
                      </span>
                    </div>
                  </div>
                </ng-container>

                <!-- Vacation days -->
                <ng-container *ngSwitchCase="searchTeacherDtoFieldNames.vacationDates">
                  <div class="multiline overflow-x-auto"
                    [attr.style]="col.maxWidth !== undefined ? 'max-width:' + col.maxWidth + ' !important; width:max-content !important; ' : 'width:max-content !important;'">
                    <div class="vacation-days flex align-items-center gap-1">
                      <span *ngFor="let day of teacher.vacationDates" class="vacation-day">
                        {{ day | date:'mediumDate' }}
                      </span>
                      <i class="pi pi-info-circle text-400 cursor-pointer ml-1"
                         pTooltip="Vacation days are displayed in teacher's timezone"
                         tooltipPosition="top"
                         *ngIf="teacher.vacationDates && teacher.vacationDates.length > 0">
                      </i>
                    </div>
                  </div>
                </ng-container>

                <!-- Account status -->
                <ng-container *ngSwitchCase="searchTeacherDtoFieldNames.isBlocked">
                  <div class="multiline"
                    [attr.style]="col.maxWidth !== undefined ? 'max-width:' + col.maxWidth + ' !important; width:max-content !important; ' : 'width:max-content !important;'">
                    <span [ngClass]="teacher.isBlocked ? 'active-status' : 'inactive-status'">
                      {{ teacher.isBlocked ? 'Active' : 'Blocked' }}
                    </span>
                  </div>
                </ng-container>

                <ng-container *ngSwitchCase="searchTeacherDtoFieldNames.availabilityStatus">
                  <div class="multiline"
                    [attr.style]="col.maxWidth !== undefined ? 'max-width:' + col.maxWidth + ' !important; width:max-content !important; ' : 'width:max-content !important;'">
                    {{
                    this.enumDropdownOptionsService.getLabelFromValue(this.enumDropdownOptionsService.teacherAvailabilityStatusOptions,
                    teacher.availabilityStatus )}}
                  </div>
                </ng-container>


                <ng-container *ngSwitchCase="searchTeacherDtoFieldNames.accountStatus">
                  <div class="multiline"
                    [attr.style]="col.maxWidth !== undefined ? 'max-width:' + col.maxWidth + ' !important; width:max-content !important; ' : 'width:max-content !important;'">
                    {{
                    this.enumDropdownOptionsService.getLabelFromValue(this.enumDropdownOptionsService.userAccountStatusOptions,
                    teacher.accountStatus )}}
                  </div>
                </ng-container>


                <ng-container *ngSwitchCase="searchTeacherDtoFieldNames.lastAvailStatusUpdate">
                  <div class="multiline"
                    [attr.style]="col.maxWidth !== undefined ? 'max-width:' + col.maxWidth + ' !important; width:max-content !important; ' : 'width:max-content !important;'">
                    {{ formatUtcDateToAdminLocalized(teacher.lastAvailStatusUpdate) }}
                  </div>
                </ng-container>

                <ng-container *ngSwitchDefault>
                  <div class="multiline"
                    [attr.style]="col.maxWidth !== undefined ? 'max-width:' + col.maxWidth + ' !important; width:max-content !important; ' : 'width:max-content !important;'">
                    {{ teacher[col.field] }}
                  </div>
                </ng-container>
              </ng-container>
            </td>

          </tr>
        </ng-template>

        <!-- Table Footer -->
        <ng-template pTemplate="footer">

        <app-data-grid-header-footer
          [columns]="selectedColumns()"
          [sortable]="true"
          [reorderable]="true"
          [showHeader]="false"
          [showFooter]="true"
          [showActionsColumn]="true"
          actionsColumnHeader="Actions"
          actionsColumnWidth="100px">
        </app-data-grid-header-footer>
        </ng-template>

        <!-- Empty Message -->
        <ng-template pTemplate="emptymessage">
          <tr>
            <td colspan="15" class="text-center p-4">No teachers found.</td>
          </tr>
        </ng-template>
      </p-table>
    </div>
  </div>
</div>