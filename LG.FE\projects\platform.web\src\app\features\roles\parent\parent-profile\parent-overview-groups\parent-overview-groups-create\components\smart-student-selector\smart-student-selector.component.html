<!-- Professional Smart Student Selector -->
<div class="professional-student-selector">

  <!-- Compact Professional Selection Summary -->
  @if (selectedLanguageName && selectedStudents.length > 0) {
    <div class="compact-selection-summary">
      <!-- Main Info Row -->
      <div class="selection-info-row">
        <div class="language-section">
          <i class="pi pi-globe"></i>
          <span class="language-name">{{ selectedLanguageName }}</span>
            <!-- Group Level Row (Only when 2+ students) -->
      @if (selectedStudents.length >= 2) {
        <div class="group-level-row">
          <div class="group-level-info">
            <div class="level-badges">
              @if (computedGroupLanguageLevels.length > 0) {
                @for (level of computedGroupLanguageLevels; track level) {
                  <span class="level-badge" [ngClass]="getGroupLevelBadgeClass(level)">
                    {{ getGroupLevelDisplayText(level) }}
                  </span>
                }
              } @else {
                <span class="level-badge level-none">N/A</span>
              }
            </div>
          </div>
        </div>
      }
        </div>

        <div class="students-section">
          <span class="student-count">{{ selectedStudents.length }}</span>
          <span class="student-label">student{{ selectedStudents.length !== 1 ? 's' : '' }}</span>
        </div>
      </div>

    
    </div>
  }

  <!-- Loading State -->
  @if (loading) {
    <div class="loading-container">
      <div class="loading-content">
        <i class="pi pi-spin pi-spinner text-primary text-2xl"></i>
        <span class="text-600 text-sm mt-2">Loading students...</span>
      </div>
    </div>
  } @else {
    
    <!-- Professional Search & Controls -->
    @if (showSearch() && studentCards().length > 0) {
      <div class="search-controls-section">
        <div class="search-toolbar hidden">
          <div class="search-input-wrapper">
            <span class="p-input-icon-left search-input-container">
              <input
                type="text"
                pInputText
                class="search-input"
                placeholder="Search students by name..."
                [value]="searchQuery()"
                (input)="onSearchChange($event)">
            </span>
            @if (searchQuery()) {
              <button
                type="button"
                pButton
                pRipple
                class="p-button-text p-button-sm clear-search-btn"
                icon="pi pi-times"
                pTooltip="Clear search"
                tooltipPosition="top"
                (click)="clearSearch()">
              </button>
            }
          </div>
          <!-- <div class="view-controls">
            <button
              type="button"
              pButton
              pRipple
              class="p-button-outlined p-button-sm view-toggle-btn"
              [icon]="compactView() ? 'pi pi-th-large' : 'pi pi-list'"
              [pTooltip]="compactView() ? 'Switch to Grid View' : 'Switch to List View'"
              tooltipPosition="top"
              (click)="toggleView()">
            </button>
          </div> -->
        </div>
        @if (searchQuery() && filteredStudentCards().length === 0) {
          <div class="no-search-results">
            <div class="no-results-content">
              <i class="pi pi-search"></i>
              <span class="no-results-text">No students found matching "{{ searchQuery() }}"</span>
            </div>
          </div>
        }
      </div>
    }

    <!-- Students Container -->
    <div class="students-container" [class.empty]="studentCards().length === 0">

      <!-- Empty State -->
      @if (studentCards().length === 0) {
        <div class="empty-state">
          <i class="pi pi-users text-400 text-4xl mb-3"></i>
          <h4 class="text-600 m-0 mb-2">No Students Available</h4>
          <p class="text-500 text-sm m-0">Please select a language first to see available students.</p>
        </div>
      } @else {
        <!-- {{filteredStudentCards() | json}} -->
        <!-- Professional Student List -->
        <div class="professional-student-list" [class.compact-view]="compactView()">
          @for (student of filteredStudentCards(); track student.userId) {
            <div class="professional-student-card"
                 [class.selected]="student.isSelected"
                 [class.disabled]="!student.isSelectable"
                 [class.selectable]="student.isSelectable"
                 pRipple
                 (click)="onStudentClick(student)">

              <!-- Student Profile Section -->
              <div class="student-profile-section">
                <!-- PrimeNG Avatar Container -->
                <div class="avatar-container">
                  @if (student.profilePhotoUrl) {
                    <p-avatar
                      [image]="student.profilePhotoUrl"
                      size="normal"
                      shape="circle"
                      class="student-avatar">
                    </p-avatar>
                  } @else {
                    <p-avatar
                      [label]="getStudentInitials(student)"
                      size="normal"
                      shape="circle"
                      [style]="{'background-color': '#6366f1', 'color': 'white', 'font-weight': '600'}"
                      class="student-avatar">
                    </p-avatar>
                  }

                  <p class="text-overflow word-break">
</p>
                  <!-- Selection Indicator -->
                  <div class="selection-indicator" [class.visible]="student.isSelected">
                    <i class="pi pi-check"></i>
                  </div>
                </div>

                <!-- Student Information -->
                <div class="student-information">
                  <div class="student-name-section">
                    <p class="student-name">{{ getStudentFullName(student) }}</p>
                    <div class="student-meta">
             <div class="language-proficiency-section">
                @if (student.languageLevels.length === 0) {
                  <div class="professional-language-pill no-languages">
                    <span class="language-name">No languages</span>
                    <span class="level-indicator">N/A</span>
                  </div>
                } @else if (student.languageLevels.length <= 3) {
                  <!-- Show up to 3 languages as individual pills -->
                  @for (languageLevel of student.languageLevels; track languageLevel.teachingLanguageId + languageLevel.level) {
                    <div class="professional-language-pill"
                         [ngClass]="getLanguageBadgeClass(languageLevel)"
                         [class.compact]="student.languageLevels.length > 1">
                      <span class="language-name">{{ languageLevel.languageName }}</span>
                      <span class="level-indicator">{{ languageLevel.levelDisplay }}</span>
                    </div>
                  }
                } @else {
                  <!-- 4+ languages - show first 3 pills + compact summary for the rest -->
                  <div class="mixed-language-display">
                    <!-- First 3 languages as compact pills -->
                    @for (languageLevel of student.languageLevels.slice(0, 3); track languageLevel.teachingLanguageId + languageLevel.level) {
                      <div class="professional-language-pill compact" [ngClass]="getLanguageBadgeClass(languageLevel)">
                        <span class="language-name">{{ languageLevel.languageName }}</span>
                        <span class="level-indicator">{{ languageLevel.levelDisplay }}</span>
                      </div>
                    }

                    <!-- Compact indicator for remaining languages -->
                    <div class="additional-languages-indicator"
                         [pTooltip]="getRemainingLanguagesTooltip(student)"
                         tooltipPosition="top"
                         [tooltipStyleClass]="'language-tooltip'">
                      <span class="plus-indicator">+{{ student.languageLevels.length - 3 }}</span>
                      <i class="pi pi-info-circle info-icon"></i>
                    </div>
                  </div>
                }
              </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Language Proficiency Section -->
             

              <!-- Disabled Overlay -->
              @if (!student.isSelectable && !student.isSelected) {
                <div class="disabled-overlay">
                  <div class="disabled-content">
                    <i class="pi pi-ban"></i>
                    <span class="disabled-text">Not compatible</span>
                  </div>
                </div>
              }
            </div>
          }
        </div>
        
        <!-- Selection Summary -->
      }
    </div>
  }
</div>
