<app-custom-dialog-popup [header]="dialogHeader()" [maximizable]="false" [styleClass]="dialogStyleClass()"
  [style]="dialogStyle$()" [visible]="dialogVisible()">
  <ng-container dialogHeader>
    <div class="dialog-header-content">
      <div class="header-icon">
        <i class="pi pi-graduation-cap"></i>
      </div>
      <div class="header-text">
        <span class="header-title">{{mockLesson.lesson!.teachingLanguageName!}} Lesson</span>
        <span class="header-subtitle">Lesson Details & Actions</span>
      </div>
    </div>
  </ng-container>

  @defer() {


  <section class="lesson-dialog">
    <!-- Hero Language Card -->
    <div class="hero-language-card">
      <div class="language-info">
        <div class="language-badge">
          <img [src]="generalService.getImageUrlForLanguage(mockLesson.lesson!.teachingLanguageName!)"
               [alt]="mockLesson.lesson!.teachingLanguageName!" class="language-flag">
          <div class="language-details">
            <span class="language-name">{{mockLesson.lesson!.teachingLanguageName!}}</span>
            <span class="language-level">
              {{generalService.getEnumDisplayText(mockLesson.lesson!.languageLevel, ILanguageLevelsEnum)}} Level
            </span>
          </div>
        </div>
        <div class="status-badge">
          <p-chip [label]="statusText()" [styleClass]="statusClass() + ' modern-status-chip'" />
        </div>
      </div>
    </div>

    <!-- Professional Calendar Card -->
    <div class="calendar-event-card">
      <div class="calendar-content">
        <div class="calendar-date-block">
          <div class="date-header">
            <span class="month-name">{{this.timezoneService.formatInTimezone(mockLesson.startDateTime, 'MMM', this.timezoneService.getTimezone())}}</span>
            <span class="year">{{this.timezoneService.formatInTimezone(mockLesson.startDateTime, 'YYYY', this.timezoneService.getTimezone())}}</span>
          </div>
          <div class="date-number">{{this.timezoneService.formatInTimezone(mockLesson.startDateTime, 'D', this.timezoneService.getTimezone())}}</div>
          <div class="day-name">{{this.timezoneService.formatInTimezone(mockLesson.startDateTime, 'dddd', this.timezoneService.getTimezone())}}</div>
        </div>

        <div class="event-info">
          <div class="time-section">
            <div class="time-label">Lesson Time</div>
            <div class="time-value">
              {{this.timezoneService.formatInTimezone(mockLesson.startDateTime, 'HH:mm', this.timezoneService.getTimezone())}}
              —
              {{this.timezoneService.formatInTimezone(mockLesson.endDateTime, 'HH:mm', this.timezoneService.getTimezone())}}
            </div>
          </div>

          <div class="duration-section">
            <div class="duration-label">Duration</div>
            <div class="duration-value">{{this.timezoneService.formatTimeRangeDurationInTimezone(mockLesson.startDateTime, mockLesson.endDateTime)}}</div>
          </div>
        </div>
      </div>
    </div>

    <!-- Enhanced Participants Section -->
    <div class="participants-section">
      <div class="participants-header">
        <div class="participants-icon">
          <i class="pi pi-users"></i>
        </div>
        <span class="participants-title">Lesson Participants</span>
      </div>

      <!-- Parent Card -->
      <div class="participant-group">
        <div class="participant-role-header">
          <div class="role-icon parent-icon">
            <i class="pi pi-user"></i>
          </div>
          <span class="role-title">Parent</span>
        </div>
        <div class="modern-participant-card parent-card">
          <div class="participant-avatar-container">
            <img [src]="mockLesson.lesson!.parent.profilePhotoUrl || '/assets/images/auth/parent-2.webp'"
                 [alt]="mockLesson.lesson!.parent.firstName" class="participant-avatar">
            <div class="avatar-ring parent-ring"></div>
          </div>
          <div class="participant-details">
            <div class="participant-name">{{mockLesson.lesson!.parent.firstName}} {{mockLesson.lesson!.parent.lastName}}</div>
            <div class="participant-role">Parent & Guardian</div>
          </div>
        </div>
      </div>

      <!-- Teacher Card -->
      <div class="participant-group">
        <div class="participant-role-header">
          <div class="role-icon teacher-icon">
            <i class="pi pi-graduation-cap"></i>
          </div>
          <span class="role-title">Teacher</span>
        </div>
        <div class="modern-participant-card teacher-card">
          <div class="participant-avatar-container">
            <img [src]="mockLesson.lesson!.teacher!.profilePhotoUrl || '/assets/images/auth/parent-2.webp'"
                 [alt]="mockLesson.lesson!.teacher!.firstName" class="participant-avatar">
            <div class="avatar-ring teacher-ring"></div>
            <div class="online-status"></div>
          </div>
          <div class="participant-details">
            <div class="participant-name">{{mockLesson.lesson!.teacher!.firstName}} {{mockLesson.lesson!.teacher!.lastName}}</div>
            <div class="participant-timezone">
              <i class="pi pi-globe"></i>
              <span>{{mockLesson.lesson!.teacher!.timeZoneDisplayName!}}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Students Card -->
      <div class="participant-group">
        <div class="participant-role-header">
          <div class="role-icon students-icon">
            <i class="pi pi-users"></i>
          </div>
          <span class="role-title">Students</span>
          <div class="student-count-badge">{{mockLesson.lesson!.students.length}}</div>
        </div>
        <div class="students-grid">
          <div class="modern-participant-card student-card" *ngFor="let student of mockLesson.lesson!.students">
            <div class="participant-avatar-container">
              <img [src]="student.profilePhotoUrl || '/assets/images/auth/parent-2.webp'"
                   [alt]="student.firstName" class="participant-avatar">
              <div class="avatar-ring student-ring"></div>
            </div>
            <div class="participant-details">
              <div class="participant-name">{{student.firstName}} {{student.lastName}}</div>
              <div class="participant-timezone">
                <i class="pi pi-globe"></i>
                <span>{{student.timeZoneDisplayName}}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>

}
  <!-- Professional Action Buttons -->
  <ng-container dialogFooter>
    <div class="actions-footer">
      <div class="actions-grid">
        <p-button *ngIf="canJoinLesson()"
          label="Join Lesson"
          icon="pi pi-video"
          styleClass="action-btn action-btn-primary">
        </p-button>
        <p-button *ngIf="canCancelLesson()"
          label="Cancel Lesson"
          icon="pi pi-times"
          styleClass="action-btn action-btn-secondary">
        </p-button>
      </div>
    </div>
  </ng-container>
</app-custom-dialog-popup>