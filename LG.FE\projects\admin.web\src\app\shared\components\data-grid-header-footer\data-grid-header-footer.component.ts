import { CommonModule } from '@angular/common';
import { Component, Input, ChangeDetectionStrategy, ViewEncapsulation } from '@angular/core';
import { TableModule } from 'primeng/table';
import { IDataGridFields } from 'SharedModules.Library';

@Component({
  selector: 'app-data-grid-header-footer',
  standalone: true,
  imports: [
    CommonModule,
    TableModule
  ],
  templateUrl: './data-grid-header-footer.component.html',
  styleUrls: ['./data-grid-header-footer.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class DataGridHeaderFooterComponent {
  /**
   * Array of column definitions for the data grid
   */
  @Input() columns: IDataGridFields[] = [];

  /**
   * Toggle header visibility
   */
  @Input() showHeader: boolean = true;

  /**
   * Toggle footer visibility
   */
  @Input() showFooter: boolean = true;

  /**
   * Enable/disable sorting functionality
   */
  @Input() sortable: boolean = true;

  /**
   * Show actions column (frozen left column)
   */
  @Input() showActionsColumn: boolean = true;

  /**
   * Actions column header text
   */
  @Input() actionsColumnHeader: string = 'Actions';

  /**
   * Actions column width
   */
  @Input() actionsColumnWidth: string = '100px';

  /**
   * Enable column reordering
   */
  @Input() reorderable: boolean = true;

  /**
   * Custom CSS classes to apply to header/footer rows
   */
  @Input() customRowClass: string = '';

  /**
   * Array of column field names to hide globally
   */
  @Input() hiddenColumns: string[] = [];

  /**
   * Array of column field names to hide in header only
   */
  @Input() hiddenColumnsInHeader: string[] = [];

  /**
   * Array of column field names to hide in footer only
   */
  @Input() hiddenColumnsInFooter: string[] = [];

  /**
   * Hide actions column in header only
   */
  @Input() hideActionsInHeader: boolean = false;

  /**
   * Hide actions column in footer only
   */
  @Input() hideActionsInFooter: boolean = false;

  /**
   * Get the style string for a column based on its maxWidth property
   */
  getColumnStyle(column: IDataGridFields): string {
    if (column.maxWidth !== undefined) {
      return `max-width:${column.maxWidth} !important; width:max-content !important;`;
    }
    return 'width:max-content !important;';
  }

  /**
   * Check if a column is sortable
   */
  isColumnSortable(column: IDataGridFields): boolean {
    return this.sortable && column.sortable === true;
  }

  /**
   * Check if column reordering is enabled
   */
  isColumnReorderable(): boolean {
    return this.reorderable;
  }

  /**
   * Check if a column should be visible in header
   */
  isColumnVisibleInHeader(column: IDataGridFields): boolean {
    // Check column's own hide property
    if (column.hide === true) return false;

    // Check global hidden columns
    if (this.hiddenColumns.includes(column.field)) return false;

    // Check header-specific hidden columns
    if (this.hiddenColumnsInHeader.includes(column.field)) return false;

    return true;
  }

  /**
   * Check if a column should be visible in footer
   */
  isColumnVisibleInFooter(column: IDataGridFields): boolean {
    // Check column's own hide property
    if (column.hide === true) return false;

    // Check global hidden columns
    if (this.hiddenColumns.includes(column.field)) return false;

    // Check footer-specific hidden columns
    if (this.hiddenColumnsInFooter.includes(column.field)) return false;

    return true;
  }

  /**
   * Check if actions column should be visible in header
   */
  shouldShowActionsInHeader(): boolean {
    return this.showActionsColumn && !this.hideActionsInHeader;
  }

  /**
   * Check if actions column should be visible in footer
   */
  shouldShowActionsInFooter(): boolean {
    return this.showActionsColumn && !this.hideActionsInFooter;
  }

  /**
   * Get filtered columns for header
   */
  getHeaderColumns(): IDataGridFields[] {
    return this.columns.filter(col => this.isColumnVisibleInHeader(col));
  }

  /**
   * Get filtered columns for footer
   */
  getFooterColumns(): IDataGridFields[] {
    return this.columns.filter(col => this.isColumnVisibleInFooter(col));
  }
}
