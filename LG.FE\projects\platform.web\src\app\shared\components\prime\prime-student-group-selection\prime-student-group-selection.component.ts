import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, EventEmitter, inject, Injector, Input, input, Output, ViewChild, type OnInit, signal, computed, DestroyRef, AfterViewInit, OnDestroy } from '@angular/core';
import { toObservable, takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { FormsModule } from '@angular/forms';
import { debounceTime, distinctUntilChanged, skip } from 'rxjs/operators';
import {
  IStudentGroupDto,
  IGetStudentGroupsResponse,
  DataApiStateService,
  EventBusService,
  Events,
  EmitEvent,
  AuthStateService,
  State,
  DefaultGetStudentGroupsRequest,
  IBasicProfileInfoDto,
  ISearchStudentGroupsDto,
} from 'SharedModules.Library';
import { ButtonModule } from 'primeng/button';
import { Select, SelectModule } from 'primeng/select';
import { InputTextModule } from 'primeng/inputtext';
import { TooltipModule } from 'primeng/tooltip';
import { AvatarModule } from 'primeng/avatar';
import { AvatarGroupModule } from 'primeng/avatargroup';
import { untilDestroyed } from 'SharedModules.Library';
import { GeneralService } from 'SharedModules.Library';
import { StudentGroupRowDisplayComponent } from '../../student-group-row-display/student-group-row-display.component';
import { IconFieldModule } from 'primeng/iconfield';
import { InputIconModule } from 'primeng/inputicon';

@Component({
  selector: 'app-prime-student-group-selection',
  imports: [
    CommonModule,
    FormsModule,
    SelectModule,
    ButtonModule,
    InputTextModule,
    TooltipModule,
    AvatarModule,
    AvatarGroupModule,
    IconFieldModule,
    InputIconModule,
    StudentGroupRowDisplayComponent
  ],
  templateUrl: './prime-student-group-selection.component.html',
  styleUrl: './prime-student-group-selection.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class PrimeStudentGroupSelectionComponent implements OnInit, AfterViewInit, OnDestroy {
  @ViewChild('selectStudentsList') select: Select | undefined;

  // Injected services
  generalService = inject(GeneralService);
  private dataStateService = inject(DataApiStateService);
  private eventBusService = inject(EventBusService);
  private authService = inject(AuthStateService);
  private destroyRef = inject(DestroyRef);
  private injector = inject(Injector);
  private untilDestroyed = untilDestroyed();

  // Existing inputs (maintained for backward compatibility)
  @Input() selectionMode: 'single' | 'multiple' = 'single';
  @Input() nameProperty: string = 'name';
  @Input() imageProperty: string = 'image';
  @Input() groupImageProperty: string = 'image';
  @Input() imageProperties: string[] = [];
  @Input() itemImage: string = '';
  @Input() languagesMode = false;
  @Input() studentGroupMode = false;
  @Input() studentGroup = [] as unknown[];
  @Input() items = [] as any[]; // Legacy items input - will be enhanced with pagination
  @Input() createNewText!: string;
  @Input() createNewImage!: string;
  @Input() selectable = true;
  @Input() numVisible = 4;
  @Input() isItemSelected = false;
  @Input() selectedItem = ({} as any);
  @Input() selectedStudent: IStudentGroupDto | undefined;

  // New pagination inputs
  @Input() enablePagination = false; // Enable pagination functionality
  @Input() pageSize = 5; // Items per page (default from StudentGroupsListHelperService)
  @Input() autoLoadInitialData = false; // Auto-load data on init when pagination is enabled

  // New search inputs
  @Input() enableSearch = true; // Enable search functionality (enabled by default)
  @Input() searchDebounceTime = 400; // Debounce time for search in milliseconds
  @Input() searchPlaceholder = 'Search student groups...'; // Search placeholder text

  // Input signals
  styleClass = input('w-full full-width mb-2');
  selectedItemProperty = input('');
  baseProperty = input('');
  textForNameProperty = input('');
  resetSelectionSignal = input(false);

  // Output events
  @Output() groupItemClicked: EventEmitter<IStudentGroupDto> = new EventEmitter<IStudentGroupDto>();
  @Output() newItemClicked: EventEmitter<IStudentGroupDto> = new EventEmitter<IStudentGroupDto>();
  @Output() pageChanged: EventEmitter<any> = new EventEmitter<any>();
  @Output() loadMoreClicked: EventEmitter<void> = new EventEmitter<void>();
  @Output() searchTermChanged: EventEmitter<string> = new EventEmitter<string>();
  @Output() searchCleared: EventEmitter<void> = new EventEmitter<void>();

  // Pagination state signals
  currentPage = signal(1);
  isLoadingMore = signal(false);
  hasMoreData = signal(true);
  totalRecords = signal(0);

  // Search state signals
  searchTerm = signal<string>('');
  isSearching = signal(false);
  isSearchMode = signal(false);
  searchResults = signal<IStudentGroupDto[]>([]);
  isTyping = signal(false); // Track when user is actively typing

  // Custom search input management
  searchInputValue = '';
  private searchInputDebounceTimer: any;
  private typingIndicatorTimer: any;

  // Initialization state tracking
  private isInitialized = signal(false);



  // Data management signals
  allItems = signal<IStudentGroupDto[]>([]); // Accumulated items from all pages

  // Clean computed properties without side effects
  displayItems = computed(() => {
    if (this.enablePagination) {
      // In search mode, show search results; otherwise show all items
      const items = this.isSearchMode() ? this.searchResults() : this.allItems();
      const filteredItems = Array.isArray(items) ? items.filter(item => item != null) : [];
      console.log('📊 displayItems computed:', {
        searchMode: this.isSearchMode(),
        itemCount: filteredItems.length,
        isLoading: this.isLoading()
      });
      return filteredItems;
    }
    return Array.isArray(this.items) ? this.items.filter(item => item != null) : [];
  });

  // Computed property for loading state (pagination, search, or typing)
  isLoading = computed(() => this.isLoadingMore() || this.isSearching() || this.isTyping());

  // Computed property for placeholder text
  effectivePlaceholder = computed(() => {
    if (this.isSearchMode() && this.searchTerm()) {
      return `Searching for "${this.searchTerm()}"...`;
    }
    return this.enableSearch ? this.searchPlaceholder : 'Select a Student from the list';
  });

  /**
   * Get dynamic search placeholder based on current state
   */
  getSearchPlaceholder(): string {
    if (this.isSearching()) {
      return 'Searching...';
    } else if (this.isTyping()) {
      return 'Type to search...';
    }
    return this.searchPlaceholder;
  }

  studentGroups$ = computed(() => this.dataStateService.parentStudentsGroups.state() as State<IGetStudentGroupsResponse>);

  ngOnInit(): void {
    this.initializeComponent();
    this.setupSubscriptions();
  }

  ngAfterViewInit(): void {
    // Setup cleaner search handling
    this.setupCleanSearchHandling();
  }

  ngOnDestroy(): void {
    // Clean up timers
    this.clearAllTimers();
  }

  private clearAllTimers(): void {
    if (this.searchInputDebounceTimer) {
      clearTimeout(this.searchInputDebounceTimer);
      this.searchInputDebounceTimer = null;
    }
    if (this.typingIndicatorTimer) {
      clearTimeout(this.typingIndicatorTimer);
      this.typingIndicatorTimer = null;
    }
  }

  private initializeComponent(): void {
    // Auto-load initial data if pagination is enabled and autoLoadInitialData is true
    if (this.enablePagination && this.autoLoadInitialData) {
      console.log('Auto-loading initial data...');
      this.loadInitialData();
    } else {
      console.log('Skipping auto-load:', { enablePagination: this.enablePagination, autoLoadInitialData: this.autoLoadInitialData });
    }

    // Mark component as initialized
    this.isInitialized.set(true);
  }

  private setupSubscriptions(): void {
    // Subscribe to reset selection signal
    toObservable(this.resetSelectionSignal, {
      injector: this.injector
    }).pipe(this.untilDestroyed()).subscribe({
      next: (reset) => {
        if (reset) {
          console.log('resetSelectionSignal', reset);
          this.select?.clear();
          this.selectedStudent = {} as IStudentGroupDto;
          this.groupItemClicked.emit(this.selectedStudent);
        }
      }
    });

    // Subscribe to student groups state changes when pagination is enabled
    if (this.enablePagination) {
      console.log('Setting up state subscription for pagination...');
      toObservable(this.studentGroups$, { injector: this.injector })
        .pipe(takeUntilDestroyed(this.destroyRef))
        .subscribe({
          next: (state) => {
            console.log('📡 State subscription triggered');
            this.handleStudentGroupsStateChange(state);
          }
        });
    }

    // Setup search functionality when enabled
    if (this.enableSearch && this.enablePagination) {
      this.setupSearchSubscription();
    }
  }

  private setupSearchSubscription(): void {
    // Subscribe to search term changes with debouncing
    // Only trigger after component is initialized to prevent duplicate API calls
    toObservable(this.searchTerm, { injector: this.injector })
      .pipe(
        debounceTime(this.searchDebounceTime),
        distinctUntilChanged(),
        // Skip the initial empty value to prevent duplicate API calls
        skip(1),
        takeUntilDestroyed(this.destroyRef)
      )
      .subscribe({
        next: (searchTerm: string) => {
          // Only handle search changes after component is fully initialized
          if (this.isInitialized()) {
            console.log('Search term changed via subscription:', searchTerm);
            this.handleSearchTermChange(searchTerm);
          }
        }
      });
  }

  private setupCleanSearchHandling(): void {
    // Minimal setup - let the existing search logic handle everything
    console.log('Search handling setup complete');
  }

  private handleStudentGroupsStateChange(state: State<IGetStudentGroupsResponse>): void {
    console.log('State change handler called:', {
      loading: state.loading,
      hasData: !!state.data,
      hasError: state.hasError,
      searchMode: this.isSearchMode(),
      currentPage: this.currentPage()
    });

    if (state.loading) {
      if (this.isSearchMode()) {
        this.isSearching.set(true);
      } else {
        this.isLoadingMore.set(true);
      }
      return;
    }

    // Clear loading states
    this.isLoadingMore.set(false);
    this.isSearching.set(false);

    if (state.data) {
      const response = state.data;
      // Ensure we have valid data and filter out any null/undefined items
      const newItems = (response.pageData as IStudentGroupDto[] || [])
        .filter(item => item != null && typeof item === 'object');

      if (this.isSearchMode()) {
        // Handle search results
        if (this.currentPage() === 1) {
          // First page of search results - replace search results
          this.searchResults.set(newItems);
        } else {
          // Subsequent pages of search results - append to search results
          this.searchResults.update(current => [...current, ...newItems]);
        }
      } else {
        // Handle normal pagination
        if (this.currentPage() === 1) {
          // First page - replace all items
          this.allItems.set(newItems);
        } else {
          // Subsequent pages - append to existing items
          this.allItems.update(current => [...current, ...newItems]);
        }
      }

      // Update pagination state
      this.totalRecords.set(response.totalRecords || 0);

      // Update hasMoreData based on current mode
      const currentItems = this.isSearchMode() ? this.searchResults() : this.allItems();
      this.hasMoreData.set(currentItems.length < this.totalRecords());



      console.log('Student groups loaded:', {
        page: this.currentPage(),
        searchMode: this.isSearchMode(),
        searchTerm: this.searchTerm(),
        newItems: newItems.length,
        totalItems: currentItems.length,
        totalRecords: this.totalRecords(),
        hasMore: this.hasMoreData(),
        sampleItem: newItems[0]
      });
    }

    if (state.hasError) {
      console.error('Error loading student groups:', state.error);
      this.isLoadingMore.set(false);
      this.isSearching.set(false);



      // Reset to a safe state on error
      if (this.currentPage() === 1) {
        if (this.isSearchMode()) {
          this.searchResults.set([]);
        } else {
          this.allItems.set([]);
        }
      }
    }
  }

  onSelectionChange(event: { value: IStudentGroupDto}) {
    console.log(event);
    this.selectedStudent = event.value;
    this.groupItemClicked.emit(this.selectedStudent);
  }

  /**
   * Handle dropdown show event
   */
  onDropdownShow(): void {
    console.log('🔽 Dropdown opened');
    // Focus on search input when dropdown opens (if search is enabled)
    if (this.enableSearch && this.enablePagination) {
      setTimeout(() => {
        const searchInput = document.querySelector('.p-select-header input[type="text"]') as HTMLInputElement;
        if (searchInput) {
          searchInput.focus();
        }
      }, 150);
    }
  }

  /**
   * Handle dropdown hide event
   */
  onDropdownHide(): void {
    console.log('🔼 Dropdown closed');
  }

  /**
   * Handle custom search input changes with debouncing
   */
  onSearchInputChange(event: any): void {
    // Prevent event bubbling to avoid dropdown closing
    event.stopPropagation();

    const value = event.target.value;
    this.searchInputValue = value;

    console.log('� Search input changed:', value);

    // Show typing indicator immediately
    this.isTyping.set(true);

    // Clear existing timers
    if (this.searchInputDebounceTimer) {
      clearTimeout(this.searchInputDebounceTimer);
    }
    if (this.typingIndicatorTimer) {
      clearTimeout(this.typingIndicatorTimer);
    }

    // Set typing indicator timer (shorter than search debounce)
    this.typingIndicatorTimer = setTimeout(() => {
      this.isTyping.set(false);
    }, this.searchDebounceTime + 100);

    // Set new timer for debounced search
    this.searchInputDebounceTimer = setTimeout(() => {
      this.isTyping.set(false); // Clear typing indicator when search starts
      this.searchTerm.set(value);
      console.log('⏰ Debounced search triggered:', value);
    }, this.searchDebounceTime);
  }

  /**
   * Clear the search input (but keep dropdown open)
   */
  clearSearchInput(event?: Event): void {
    // Prevent event bubbling to avoid dropdown closing
    if (event) {
      event.stopPropagation();
    }

    this.searchInputValue = '';
    this.searchTerm.set('');
    this.isTyping.set(false);

    // Clear any pending timers
    if (this.searchInputDebounceTimer) {
      clearTimeout(this.searchInputDebounceTimer);
      this.searchInputDebounceTimer = null;
    }
    if (this.typingIndicatorTimer) {
      clearTimeout(this.typingIndicatorTimer);
      this.typingIndicatorTimer = null;
    }

    console.log('🧹 Search input cleared');
  }



  /**
   * Load initial data (first page)
   */
  loadInitialData(): void {
    this.currentPage.set(1);
    this.loadStudentGroups();
  }

  /**
   * Load more data (next page)
   */
  onLoadMoreClicked(): void {
    if (this.isLoading() || !this.hasMoreData()) {
      return;
    }

    this.currentPage.update(page => page + 1);
    this.loadStudentGroups();
    this.loadMoreClicked.emit();
  }

  /**
   * Handle search term changes
   */
  private handleSearchTermChange(searchTerm: string): void {
    const trimmedTerm = searchTerm.trim();

    if (trimmedTerm === '') {
      // Clear search and return to normal browsing (but keep dropdown open)
      // Only clear if we're actually in search mode to avoid unnecessary operations
      if (this.isSearchMode()) {
        this.clearSearch(false);
      }
      return;
    }

    console.log('Starting search for:', trimmedTerm);

    // Enter search mode and reset to page 1
    this.isSearchMode.set(true);
    this.currentPage.set(1); // Always reset to page 1 for new search
    this.searchResults.set([]); // Clear previous search results
    this.hasMoreData.set(true);

    // Trigger search
    this.loadStudentGroups();
    this.searchTermChanged.emit(trimmedTerm);
  }



  /**
   * Clear search and return to normal pagination
   */
  clearSearch(closeDropdown: boolean = false): void {
    const wasInSearchMode = this.isSearchMode();

    this.searchTerm.set('');
    this.isSearchMode.set(false);
    this.searchResults.set([]);
    this.currentPage.set(1);
    this.hasMoreData.set(true);

    // Clear custom search input and typing indicators
    this.searchInputValue = '';
    this.isTyping.set(false);

    // Clear any pending timers
    if (this.searchInputDebounceTimer) {
      clearTimeout(this.searchInputDebounceTimer);
      this.searchInputDebounceTimer = null;
    }
    if (this.typingIndicatorTimer) {
      clearTimeout(this.typingIndicatorTimer);
      this.typingIndicatorTimer = null;
    }

    // Only load normal data if we were in search mode, auto-load is enabled,
    // component is initialized, and we don't have normal data loaded
    if (wasInSearchMode && this.autoLoadInitialData && this.isInitialized() && this.allItems().length === 0) {
      console.log('Reloading normal data after clearing search...');
      this.loadStudentGroups();
    }

    // Close the dropdown if requested
    if (closeDropdown) {
      setTimeout(() => {
        if (this.select) {
          this.select.hide();
        }
      }, 100);
    }

    this.searchCleared.emit();
  }

  /**
   * Clear search and close dropdown (for footer button)
   */
  clearSearchAndClose(event?: Event): void {
    if (event) {
      event.stopPropagation();
    }
    this.clearSearch(true);
  }

  /**
   * Manual search trigger (for external use)
   */
  performSearch(searchTerm: string): void {
    this.searchTerm.set(searchTerm);
  }

  /**
   * Load student groups using the existing API method
   */
  private loadStudentGroups(): void {
    const parentId = this.authService.getUserClaims()?.id;
    if (!parentId) {
      console.warn('Parent ID not found. Cannot load student groups.');
      return;
    }

    // Create a proper request object using DefaultGetStudentGroupsRequest
    const baseRequest = {
      pageNumber: this.currentPage(),
      pageSize: this.pageSize,
      parentId: parentId,
      includeDeleted: false
    };

    // Add search term if in search mode
    if (this.isSearchMode() && this.searchTerm().trim()) {
      (baseRequest as DefaultGetStudentGroupsRequest).searchTerm = this.searchTerm().trim();
    }

    const request = new DefaultGetStudentGroupsRequest(baseRequest);

    // Enhanced debugging to track API call sources
    const callStack = new Error().stack;
    console.log('🚀 loadStudentGroups called:', {
      page: this.currentPage(),
      searchMode: this.isSearchMode(),
      searchTerm: this.searchTerm(),
      isInitialized: this.isInitialized(),
      allItemsCount: this.allItems().length,
      request: baseRequest,
      callSource: callStack?.split('\n')[2]?.trim() // Show where this was called from
    });

    // Emit event to trigger API call via state management
    this.eventBusService.emit(new EmitEvent(Events.StateLoadParentStudentsGroups, request));
  }

  /**
   * Reset pagination and search state
   */
  resetPagination(): void {
    this.currentPage.set(1);
    this.allItems.set([]);
    this.hasMoreData.set(true);
    this.totalRecords.set(0);
    this.isLoadingMore.set(false);

    // Reset search state
    this.searchTerm.set('');
    this.isSearchMode.set(false);
    this.isSearching.set(false);
    this.searchResults.set([]);
  }

  /**
   * Safely get group details with null checking
   */
  getSafeGroupDetails(data: any): string {
    if (!data) {
      return 'Unknown Group';
    }

    try {
      // Ensure basicProfileInfoDto exists and is an array before calling getGroupDetails
      if (!data.basicProfileInfoDto) {
        data.basicProfileInfoDto = [];
      }

      return this.generalService.getGroupDetails(data);
    } catch (error) {
      console.warn('Error getting group details:', error, data);

      // Fallback to manual group name construction
      const groupName = data.groupName || data.teachingLanguageName || 'Unknown Group';
      const levelText = data.groupLevel ? this.generalService.getILanguageLevelsEnumText(data.groupLevel, false) : '';

      return levelText ? `${groupName} ${levelText}` : groupName;
    }
  }

  /**
   * Ensure group data has all required properties for child components
   */
  ensureSafeGroupData(data: any): IStudentGroupDto | ISearchStudentGroupsDto {
    if (!data) {
      return {} as IStudentGroupDto;
    }

    return {
      ...data,
      basicProfileInfoDto: Array.isArray(data.basicProfileInfoDto) ? data.basicProfileInfoDto : [],
      groupName: data.groupName || '',
      teachingLanguageName: data.teachingLanguageName || '',
      groupLevel: data.groupLevel || 0,
      id: data.id || '',
      groupStatus: data.groupStatus || 0
    };
  }

  /**
   * Get display name for the group (compact version)
   */
  getGroupDisplayName(groupData: ISearchStudentGroupsDto): string {
    if (!groupData) return 'Unknown Group';

    // Use existing getSafeGroupDetails method but make it more compact
    const fullName = this.getSafeGroupDetails(groupData);

    // If the name is too long, truncate it for dropdown display
    return fullName.length > 25 ? fullName.substring(0, 25) + '...' : fullName;
  }

  /**
   * Get language level text in a compact format
   */
  getLanguageLevelText(level: any): string {
    if (!level) return '';

    try {
      return this.generalService.getILanguageLevelsEnumText(level, false) || '';
    } catch (error) {
      console.warn('Error getting language level text:', error);
      return '';
    }
  }

  /**
   * Get students array for display in avatar group
   */
  getStudentsForDisplay(groupData: ISearchStudentGroupsDto): IBasicProfileInfoDto[] {
    if (!groupData || !groupData.students) {
      return [];
    }

    return Array.isArray(groupData.students) ? groupData.students : [];
  }

  /**
   * Get student initials for avatar display
   */
  getStudentInitials(student: IBasicProfileInfoDto): string {
    if (!student) return '?';

    const firstName = (student.firstName || '').trim();
    const lastName = (student.lastName || '').trim();

    if (!firstName && !lastName) return '?';

    const firstInitial = firstName.charAt(0).toUpperCase();
    const lastInitial = lastName.charAt(0).toUpperCase();

    return firstInitial + lastInitial;
  }

  /**
   * Get avatar style for student with consistent colors
   */
  getStudentAvatarStyle(student: IBasicProfileInfoDto): any {
    if (!student) {
      return { 'background-color': 'var(--surface-300)', 'color': 'var(--text-color)' };
    }

    // Use a simple hash function to generate consistent colors based on student name
    const name = (student.firstName || '') + (student.lastName || '');
    const hash = this.simpleHash(name);

    // Define a set of pleasant colors for avatars
    const colors = [
      '#6366f1', '#8b5cf6', '#a855f7', '#d946ef', '#ec4899',
      '#f43f5e', '#ef4444', '#f97316', '#f59e0b', '#eab308',
      '#84cc16', '#22c55e', '#10b981', '#14b8a6', '#06b6d4',
      '#0ea5e9', '#3b82f6', '#6366f1', '#8b5cf6', '#a855f7'
    ];

    const colorIndex = hash % colors.length;

    return {
      'background-color': colors[colorIndex],
      'color': '#ffffff',
      'font-weight': '600'
    };
  }

  /**
   * Simple hash function for consistent color generation
   */
  private simpleHash(str: string): number {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return Math.abs(hash);
  }

}
