<p-select #selectStudentsList [appendTo]="'body'" [options]="displayItems()" [(ngModel)]="selectedStudent"
    [filter]="false" [placeholder]="effectivePlaceholder()" class="w-full md:w-56" [styleClass]="styleClass()"
    (onChange)="onSelectionChange($event)" (onShow)="onDropdownShow()" (onHide)="onDropdownHide()" [showClear]="false"
    [loading]="isLoading()" [loadingIcon]="'pi pi-spinner pi-spin'" [autoOptionFocus]="false" [selectOnFocus]="false">

    <!-- Custom Header Template with Search -->
    @if (enableSearch && enablePagination) {
    <ng-template #header>
        <div class="p-3 border-bottom-1 surface-border" (click)="$event.stopPropagation()">
            <!-- Search Input with Loading State -->
            <div class="p-inputgroup" [class.search-loading]="isSearching()" [class.typing]="isTyping()">
                <span class="p-inputgroup-addon">
                    <!-- @if (isSearching()) {
                    <i class="pi pi-spin pi-spinner text-primary"></i>
                    } @else if (isTyping()) {
                    <i class="pi pi-clock text-orange-500"></i>
                    } @else {
                    <i class="pi pi-search"></i>
                    } -->
                </span>
                <p-iconfield>
                    <p-inputicon styleClass="pi pi-search" />
                    <input #searchInput pInputText type="text" [placeholder]="getSearchPlaceholder()"
                        [(ngModel)]="searchInputValue" (input)="onSearchInputChange($event)"
                        (keydown.enter)="$event.preventDefault()" (click)="$event.stopPropagation()"
                        (focus)="$event.stopPropagation()" [disabled]="isSearching()" class="w-full" autocomplete="off">
                    @if (searchInputValue.trim() && !isSearching() && !isTyping()) {

                    <p-inputicon styleClass="pi pi-times text-primary cursor-pointer"  (click)="clearSearchInput($event)" [attr.aria-label]="'Clear search'" />

                    }
                    @if (isSearching()) {

                    <p-inputicon styleClass="pi pi-spin pi-spinner text-primary" />
                        }
                </p-iconfield>
            </div>

            <!-- Search Progress Indicator -->
            @if (isSearching()) {
            <div class="mt-2 p-2 bg-primary-50 border-round text-sm">
                <div class="flex align-items-center gap-2">
                    <i class="pi pi-spin pi-spinner text-primary text-xs"></i>
                    <span class="text-primary font-medium">
                        Searching for "{{ searchTerm() }}"...
                    </span>
                </div>
            </div>
            } @else if (isTyping() && searchInputValue.trim()) {
            <div class="mt-2 p-2 bg-orange-50 border-round text-sm">
                <div class="flex align-items-center gap-2">
                    <i class="pi pi-clock text-orange-500 text-xs"></i>
                    <span class="text-orange-600 font-medium">
                        Will search for "{{ searchInputValue }}"...
                    </span>
                </div>
            </div>
            }
        </div>
    </ng-template>
    }

    <ng-template #empty>
        @if (isSearchMode() && searchTerm() && !isLoading()) {
        <div class="text-center py-4">
            <i class="pi pi-search text-400 text-2xl mb-2"></i>
            <div class="text-sm text-500">No groups found for "{{ searchTerm() }}"</div>
        </div>
        } @else {
        <div class="font-medium p-3">Available Students</div>
        }
    </ng-template>

    <!-- Selected item - Compact design for dropdown -->
    <ng-template #selectedItem let-selectedOption>
        @if (selectedOption) {
            <div class="flex align-items-center gap-2 py-1">
                <!-- Group Info Section -->
                <div class="flex flex-column flex-1 min-w-0">
                    <div class="flex align-items-center gap-2 mb-1">
                        <span class="text-sm font-semibold text-900 truncate">
                            {{ getGroupDisplayName(selectedOption) }}
                        </span>
                        <span class="text-xs px-2 py-1 bg-primary-100 text-primary-700 border-round font-medium">
                            {{ getLanguageLevelText(selectedOption.groupLevel) }}
                        </span>
                    </div>
                    <span class="text-xs text-600 truncate">
                        {{ selectedOption.teachingLanguageName }}
                    <!-- {{(selectedOption) | json}} -->
                    </span>
                </div>

                <!-- Students Avatar Group -->
                <div class="flex-shrink-0">
                    <p-avatar-group>
                        @for (student of getStudentsForDisplay(selectedOption); track student.userId; let i = $index) {
                            @if (i < 3) {
                                <p-avatar
                                    [label]="getStudentInitials(student)"
                                    [pTooltip]="student.firstName + ' ' + student.lastName"
                                    tooltipPosition="top"
                                    shape="circle"
                                    size="normal"
                                    [style]="getStudentAvatarStyle(student)"
                                    styleClass="compact-avatar">
                                </p-avatar>
                            }
                        }
                        @if (getStudentsForDisplay(selectedOption).length > 3) {
                            <p-avatar
                                [label]="'+' + (getStudentsForDisplay(selectedOption).length - 3)"
                                shape="circle"
                                size="normal"
                                [style]="{'background-color': 'var(--surface-300)', 'color': 'var(--text-color)'}"
                                styleClass="compact-avatar">
                            </p-avatar>
                        }
                    </p-avatar-group>
                </div>
            </div>
        }
    </ng-template>

    <!-- Dropdown item with loading state -->
    <ng-template let-country #item>
        <div class="dropdown-item-wrapper" [class.loading-overlay]="isLoading() && displayItems().length === 0">
            <ng-container *ngTemplateOutlet="commonTemplate; context: { data: country }"></ng-container>
        </div>
    </ng-template>

    <!-- Loading overlay template for dropdown items -->
    @if (isLoading() && displayItems().length > 0) {
    <ng-template #loader>
        <div class="dropdown-loading-overlay">
            <div class="flex align-items-center justify-content-center gap-2 py-3">
                <i class="pi pi-spin pi-spinner text-primary"></i>
                <span class="text-sm text-600">
                    @if (isSearching()) {
                    Searching for more results...
                    } @else {
                    Loading more groups...
                    }
                </span>
            </div>
        </div>
    </ng-template>
    }

    <!-- Load More Footer (only shown when pagination is enabled) -->
    <ng-template #footer>
        @if (enablePagination) {
        <div class="load-more-container p-2 border-top-1 surface-border" (click)="$event.stopPropagation()">
            <!-- Search mode indicator and clear button -->
            @if (isSearchMode() && searchTerm()) {
            <div
                class="search-indicator flex align-items-center justify-content-between gap-2 mb-2 p-2 bg-primary-50 border-round">
                <div class="flex align-items-center gap-2">
                    @if (isSearching()) {
                    <i class="pi pi-spin pi-spinner text-primary text-sm"></i>
                    <span class="text-sm text-primary font-medium">
                        Searching for "{{ searchTerm() }}"...
                    </span>
                    } @else {
                    <i class="pi pi-search text-primary text-sm"></i>
                    <span class="text-sm text-primary font-medium">
                        Found {{ displayItems().length }} result(s) for "{{ searchTerm() }}"
                    </span>
                    }
                </div>
                <p-button icon="pi pi-times" size="small" styleClass="p-button-text p-button-sm clear-search-btn"
                    [pTooltip]="'Clear search'" (click)="clearSearchAndClose($event)">
                </p-button>
            </div>
            }

            <!-- Load More / Loading / Completion states -->
            @if (hasMoreData() && !isLoading()) {
            <p-button [label]="isSearchMode() ? 'Load More Results' : 'Load More Groups'" icon="pi pi-chevron-down"
                styleClass="w-full p-button-text p-button-sm load-more-btn"
                (click)="onLoadMoreClicked(); $event.stopPropagation()">
            </p-button>
            } @else if (isLoading()) {
            <div class="flex align-items-center justify-content-center gap-2 py-2">
                <i class="pi pi-spin pi-spinner text-primary"></i>
                <span class="text-sm text-600">
                    @if (isSearching()) {
                    Searching for more results...
                    } @else {
                    Loading more groups...
                    }
                </span>
            </div>
            } @else if (!hasMoreData() && displayItems().length > 0) {
            <div class="text-center py-2">
                @if (isSearchMode()) {
                <span class="text-sm text-500">
                    All search results loaded ({{ displayItems().length }} of {{ totalRecords() }})
                </span>
                } @else {
                <span class="text-sm text-500">
                    All groups loaded ({{ displayItems().length }} of {{ totalRecords() }})
                </span>
                }
            </div>
            }
        </div>
        }
    </ng-template>

    <!-- Common reusable template -->
    <ng-template #commonTemplate let-data="data">
        @if (data) {
        @let groupName = getSafeGroupDetails(data) ;
        @let safeData = (data) ;

            <div class="flex align-items-center gap-2 py-1 w-full">
                <!-- Group Info Section -->
                <div class="flex flex-column flex-1 min-w-0">
                    <div class="flex align-items-center gap-2 mb-1">
                        <span class="text-sm font-semibold text-900 truncate">
                            {{ getGroupDisplayName(data) }}
                        </span>
                        <span class="text-xs px-2 py-1 bg-primary-100 text-primary-700 border-round font-medium">
                            {{ getLanguageLevelText(data.groupLevel) }}
                        </span>
                    </div>
                    <span class="text-xs text-600 truncate">
                        {{ safeData.teachingLanguageName }}
                    <!-- {{(selectedOption) | json}} -->
                    </span>
                </div>

                <!-- Students Avatar Group -->
                <div class="flex-shrink-0">
                    <p-avatar-group>
                        @for (student of getStudentsForDisplay(data); track student.userId; let i = $index) {
                            @if (i < 3) {
                                <p-avatar
                                    [label]="getStudentInitials(student)"
                                    [pTooltip]="student.firstName + ' ' + student.lastName"
                                    tooltipPosition="top"
                                    shape="circle"
                                    size="normal"
                                    [style]="getStudentAvatarStyle(student)"
                                    styleClass="compact-avatar">
                                </p-avatar>
                            }
                        }
                        @if (getStudentsForDisplay(safeData).length > 3) {
                            <p-avatar
                                [label]="'+' + (getStudentsForDisplay(safeData).length - 3)"
                                shape="circle"
                                size="normal"
                                [style]="{'background-color': 'var(--surface-300)', 'color': 'var(--text-color)'}"
                                styleClass="compact-avatar">
                            </p-avatar>
                        }
                    </p-avatar-group>
                </div>
            </div>


        }
    </ng-template>

</p-select>