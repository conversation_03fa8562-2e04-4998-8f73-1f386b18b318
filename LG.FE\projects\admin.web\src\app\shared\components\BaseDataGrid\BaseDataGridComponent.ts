﻿import { Directive, ViewChild, ChangeDetectorRef, inject, signal, computed, OnInit, OnDestroy } from '@angular/core';
import { Table, TablePageEvent } from 'primeng/table';
import { ActivatedRoute, Router, Params } from '@angular/router';
import { Observable, Subject, takeUntil } from "rxjs";
import {
  HandleApiResponseService,
  IBasedDataGridRequest,
  IBasedDataGridResponse,
  IDataGridFields,
  IDataGridResponse,
  GeneralService,
  nameOf
} from 'SharedModules.Library';

// ============================================================================
// INTERFACES & TYPES - Clear contracts for component communication
// ============================================================================

// Import the existing IAppliedFilterTag from the applied-filters-tags component
import { IAppliedFilterTag } from '../applied-filters-tags/applied-filters-tags.component';

/**
 * Strongly typed sort direction values
 */
export type SortDirection = 'asc' | 'desc';

/**
 * Strongly typed filter action types
 */
export type FilterActionType = 'search' | 'reset';

/**
 * Strongly typed pagination and sort events from PrimeNG Table
 */
export interface IPaginationEvent {
  first: number; rows: number; page: number; pageCount: number;
}

export interface ISortEvent {
  field: string; order: 1 | -1; multiSortMeta?: Array<{ field: string; order: 1 | -1 }>;
}

/**
 * Filter event interfaces
 */
export interface IDataGridFilterChangeEvent<TRequest> {
  filterName: keyof TRequest; value: any; resetPage?: boolean;
}

export interface IDataGridFilterActionEvent<TRequest> {
  action: FilterActionType; filters: TRequest;
}

/**
 * Configuration for applied filters display and behavior
 */
export interface IAppliedFiltersConfig<TRequest> {
  convertToFilterTags?: (request: TRequest, urlParams: Params) => IAppliedFilterTag[];
  getFiltersCount?: (filters: IAppliedFilterTag[]) => number;
}

/**
 * Filter drawer types and interfaces
 */
export type FilterDrawerActionType = 'apply' | 'reset' | 'close';
export type FilterDrawerPosition = 'left' | 'right' | 'top' | 'bottom';

export interface IFilterDrawerActionEvent {
  action: FilterDrawerActionType; data?: any;
}

export interface IFilterDrawerConfig {
  enabled?: boolean; position?: FilterDrawerPosition; width?: string; headerText?: string; headerIcon?: string;
}

/**
 * Additional type definitions
 */
export interface IApiError {
  status?: number; message?: string; messages?: string[]; error?: any;
}

export type RowsPerPageOption = number;

export interface IColumnSelectionEvent {
  columns: IDataGridFields[];
}

/**
 * Main configuration object for BaseDataGrid
 * Contains all settings needed to set up a data grid
 */
export interface IBaseDataGridConfig<TRequest extends IBasedDataGridRequest> {
  // === CORE SETTINGS ===
  /** Default request object with initial values */
  defaultRequest: TRequest;
  /** API endpoint URL for fetching data */
  apiEndpoint: string;
  /** Prefix for error messages (e.g., 'Failed to load teachers') */
  errorPrefix?: string;

  // === URL & REQUEST MAPPING ===
  /** Converts URL parameters to request object */
  mapUrlParamsToRequest?: (params: Params) => TRequest;
  /** Creates a fresh default request object */
  createDefaultRequest?: () => TRequest;

  // === FILTER CONFIGURATION ===
  /** Settings for applied filters display */
  appliedFiltersConfig?: IAppliedFiltersConfig<TRequest>;
  /** Settings for filter drawer/sidebar */
  filterDrawerConfig?: IFilterDrawerConfig;
  /** Field names object for type safety */
  fieldNames?: any;
}

// ============================================================================
// BASE DATA GRID COMPONENT - Reusable data grid with common functionality
// ============================================================================

/**
 * Abstract base class for data grid components
 *
 * Provides common functionality for:
 * - URL parameter management
 * - Data fetching and pagination
 * - Sorting and filtering
 * - Applied filters display
 * - Search functionality
 *
 * @template TRequest - The request object type for API calls
 * @template TResponse - The response object type from API calls
 */
@Directive()
export abstract class BaseDataGridComponent<
  TRequest extends IBasedDataGridRequest = IBasedDataGridRequest,
  TResponse extends IBasedDataGridResponse = IBasedDataGridResponse
> implements OnInit, OnDestroy {
  // ============================================================================
  // COMPONENT SETUP & DEPENDENCIES
  // ============================================================================

  @ViewChild('dt') table!: Table;

  constructor(
    protected route: ActivatedRoute,
    protected cdr: ChangeDetectorRef,
  ) { }

  // === INJECTED SERVICES ===
  protected handleApiService = inject(HandleApiResponseService);
  protected generalService = inject(GeneralService);
  protected router = inject(Router);

  // === ABSTRACT METHODS (Must be implemented by child classes) ===
  abstract getConfig(): IBaseDataGridConfig<TRequest>;

  // ============================================================================
  // STATE MANAGEMENT - Reactive signals for component state
  // ============================================================================

  // === CORE UI STATE ===
  isLoading = signal<boolean>(false);
  cols: IDataGridFields[] = [];
  rowsPerPageOptions = signal<RowsPerPageOption[]>([10, 25, 50]);

  // === DATA & PARAMETERS ===
  protected currentUrlParams = signal<Params>({});
  protected selectedColumns = signal<IDataGridFields[]>([]);
  protected queryParams = signal<TRequest>({} as TRequest);
  protected dataResponse = signal<TResponse | null>(null);

  // === FILTER STATE ===
  protected appliedFilters = signal<IAppliedFilterTag[]>([]);
  protected isFiltersDrawerVisible = signal<boolean>(false);

  // === COMPUTED PROPERTIES (Derived state) ===
  readonly totalRecords = computed(() => this.dataResponse()?.totalRecords || 0);
  readonly currentPage = computed(() => this.dataResponse()?.currentPage || 1);
  readonly pageSize = computed(() => this.queryParams().pageSize || 10);
  readonly appliedFiltersCount = computed(() => {
    const config = this.getConfig();
    const filters = this.appliedFilters();
    return config.appliedFiltersConfig?.getFiltersCount
      ? config.appliedFiltersConfig.getFiltersCount(filters)
      : filters.length;
  });
  readonly hasAppliedFilters = computed(() => this.appliedFiltersCount() > 0);

  // === LIFECYCLE & UTILITY ===
  protected destroy$ = new Subject<void>();
  protected scrollPosition = 0;
  protected searchTimeout: any = null;

  // ============================================================================
  // LIFECYCLE METHODS - Component initialization and cleanup
  // ============================================================================

  ngOnInit(): void {
    this.route.queryParams.pipe(takeUntil(this.destroy$)).subscribe(params => {
      this.currentUrlParams.set({ ...params });
      const request = this.createRequestFromUrlParams(params);
      this.queryParams.set(request);
      if (Object.keys(params).length === 0) this.updateUrlParams();
      this.fetchData();
      this.updateAppliedFilters();
    });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
    if (this.searchTimeout) clearTimeout(this.searchTimeout);
  }

  // ============================================================================
  // URL PARAMETER MANAGEMENT - Handles URL sync and parameter mapping
  // ============================================================================

  /**
   * Create request object from URL parameters
   */
  private createRequestFromUrlParams(params: Params): TRequest {
    const config = this.getConfig();

    if (Object.keys(params).length > 0) {
      // Use custom mapping if provided, otherwise use generic mapping
      return config.mapUrlParamsToRequest
        ? config.mapUrlParamsToRequest(params)
        : this.mapGenericUrlParamsToRequest(params, config.defaultRequest);
    } else {
      // Return default request when no URL params
      return config.createDefaultRequest
        ? config.createDefaultRequest()
        : config.defaultRequest;
    }
  }

  // ============================================================================
  // APPLIED FILTERS MANAGEMENT - Handles filter display and updates
  // ============================================================================

  /**
   * Update the applied filters display based on current request state
   */
  protected updateAppliedFilters(): void {
    const config = this.getConfig();
    if (config.appliedFiltersConfig?.convertToFilterTags) {
      const filterTags = config.appliedFiltersConfig.convertToFilterTags(
        this.queryParams(),
        this.currentUrlParams()
      );
      this.appliedFilters.set(filterTags);
    }
  }

  /**
   * Generic URL params to request mapping with strong typing
   */
  protected mapGenericUrlParamsToRequest(params: Params, defaultRequest: TRequest): TRequest {
    const fieldNames = nameOf<IBasedDataGridRequest>();
    const request: TRequest = { ...defaultRequest };

    // Map common pagination and sorting params with type safety
    if (params[fieldNames.pageNumber] !== undefined) {
      this.setRequestProperty(request, 'pageNumber', +params[fieldNames.pageNumber]);
    }
    if (params[fieldNames.pageSize] !== undefined) {
      this.setRequestProperty(request, 'pageSize', +params[fieldNames.pageSize]);
    }
    if (params[fieldNames.sortColumn!] !== undefined && params[fieldNames.sortColumn!] !== 'null') {
      this.setRequestProperty(request, 'sortColumn', params[fieldNames.sortColumn!]);
    }
    if (params[fieldNames.sortDirection!] !== undefined && params[fieldNames.sortDirection!] !== 'null') {
      this.setRequestProperty(request, 'sortDirection', params[fieldNames.sortDirection!] as SortDirection);
    }
    if (params[fieldNames.searchTerm!] !== undefined && params[fieldNames.searchTerm!] !== 'null') {
      this.setRequestProperty(request, 'searchTerm', params[fieldNames.searchTerm!]);
    }

    return request;
  }

  /**
   * Type-safe property setter for request objects
   */
  private setRequestProperty<K extends keyof TRequest>(
    request: TRequest,
    key: K,
    value: TRequest[K]
  ): void {
    request[key] = value;
  }

  // ============================================================================
  // DATA FETCHING - Handles API calls and data loading
  // ============================================================================

  /**
   * Update URL parameters with current query params
   */
  protected updateUrlParams(): void {
    this.generalService.updateQueryParams(this.queryParams(), { replaceUrl: true });
  }

  /**
   * Fetch data from the API using current query parameters
   */
  protected fetchData(): void {
    const config = this.getConfig();

    this.getDataTableData<TRequest, TResponse>(
      this.queryParams(),
      config.apiEndpoint,
      config.errorPrefix || 'Failed to load data'
    ).pipe(takeUntil(this.destroy$)).subscribe({
      next: (response: TResponse) => {
        this.dataResponse.set(response);
        this.updateAppliedFilters();
      },
      error: (error: IApiError) => {
        console.error('Error fetching data:', error);
        this.dataResponse.set(null);
      }
    });
  }

  // ============================================================================
  // USER INTERACTION HANDLERS - Pagination, sorting, searching, filtering
  // ============================================================================

  /**
   * Save and restore scroll position for data updates
   */
  protected saveScrollPosition(): void { this.scrollPosition = window.scrollY; }
  protected restoreScrollPosition(): void {
    setTimeout(() => window.scrollTo({ top: this.scrollPosition, behavior: 'auto' }), 10);
  }

  /**
   * Handle page changes from the data table
   */
  onPageChange(event: TablePageEvent): void {
    const newPageNumber = Math.ceil((event.first || 0) / (event.rows || 10)) + 1;
    const newPageSize = event.rows;

    if (this.queryParams().pageNumber === newPageNumber && this.queryParams().pageSize === newPageSize) return;

    this.saveScrollPosition();
    this.queryParams.update(params => ({ ...params, pageNumber: newPageNumber, pageSize: newPageSize }));
    this.updateUrlParams();
    this.restoreScrollPosition();
  }

  /**
   * Handle sort changes from the data table
   */
  onSortChange(event: ISortEvent): void {
    const newSortColumn = event.field;
    const newSortDirection: SortDirection = event.order === 1 ? 'asc' : 'desc';

    if (this.queryParams().sortColumn === newSortColumn && this.queryParams().sortDirection === newSortDirection) return;

    this.saveScrollPosition();
    this.queryParams.update(params => ({ ...params, sortColumn: newSortColumn, sortDirection: newSortDirection }));
    this.updateUrlParams();
    this.restoreScrollPosition();
  }

  /**
   * Handle search input with debouncing
   */
  updateSearchTerm(event: Event, debounceMs: number = 400): void {
    const searchValue = (event.target as HTMLInputElement).value;

    if (this.searchTimeout) clearTimeout(this.searchTimeout);

    this.searchTimeout = setTimeout(() => {
      if (this.queryParams().searchTerm !== searchValue) {
        this.queryParams.update(params => ({ ...params, searchTerm: searchValue || null, pageNumber: 1 }));
        this.updateUrlParams();
      }
    }, debounceMs);
  }

  /**
   * Clear the current search term
   */
  clearSearchTerm(): void {
    this.updateSearchTerm({ target: { value: '' } } as unknown as Event);
  }

  /**
   * Generic filter handlers
   */
  onFilterChange(event: IDataGridFilterChangeEvent<TRequest>): void {
    this.queryParams.update(current => ({ ...current, [event.filterName]: event.value, ...(event.resetPage ? { pageNumber: 1 } : {}) }));
  }

  onFilterAction(event: IDataGridFilterActionEvent<TRequest>): void {
    if (event.action === 'search') {
      this.saveScrollPosition();
      this.queryParams.set(event.filters);
      this.updateUrlParams();
      this.restoreScrollPosition();
    } else if (event.action === 'reset') {
      this.resetFilters();
    }
  }

  /**
   * Applied filters handlers with strong typing
   */
  onAppliedFilterRemove(event: { filter: IAppliedFilterTag; event?: MouseEvent }): void {
    const filterName = event.filter.removeData?.filterName;
    if (filterName) this.removeFilter(filterName, event.event);
  }

  onAppliedFiltersClearAll(_event: MouseEvent): void { this.resetFilters(); }

  /**
   * Filter drawer handlers
   */
  openFiltersDrawer(): void { this.isFiltersDrawerVisible.set(true); }
  closeFiltersDrawer(): void { this.isFiltersDrawerVisible.set(false); }

  /**
   * Generic filter drawer action handler (should be overridden by child classes for 'apply' logic)
   */
  onFiltersDrawerAction(event: IFilterDrawerActionEvent): void {
    switch (event.action) {
      case 'apply':
        // Child classes should override this method to implement specific filter application logic
        console.warn('onFiltersDrawerAction apply case should be overridden by child class for specific filter logic');
        this.closeFiltersDrawer();
        break;
      case 'reset':
        this.resetFilters();
        this.closeFiltersDrawer();
        break;
      case 'close':
        this.closeFiltersDrawer();
        break;
    }
  }

  /**
   * Generic handlers (to be overridden by child classes)
   */
  onColumnsChange(event: IDataGridFields[] | IColumnSelectionEvent): void {
    // Handle both direct array from p-multiSelect and wrapped event object
    const selectedColumns = Array.isArray(event) ? event : event.columns;

    // Filter columns based on selection, preserving order from original cols array
    const filteredColumns = this.cols.filter(col =>
      selectedColumns.some(valCol => valCol.field === col.field)
    );

    console.debug('Column selection changed:', {
      originalCols: this.cols.length,
      selectedFromMultiSelect: selectedColumns.length,
      filteredResult: filteredColumns.length,
      selectedFields: selectedColumns.map(c => c.field),
      filteredFields: filteredColumns.map(c => c.field)
    });

    this.selectedColumns.set(filteredColumns);
  }

  removeFilter(_filterName: string, event?: MouseEvent): void {
    if (event) event.stopPropagation();
    console.warn('removeFilter method should be overridden by child class');
  }

  resetFilters(): void {
    console.warn('resetFilters method should be overridden by child class');
  }

  // Helper function to clean the request object before sending to API
  protected cleanRequestForApi<T extends IBasedDataGridRequest>(request: T): Partial<T> {
    const cleanedRequest: Partial<T> = {};
    for (const [key, value] of Object.entries(request)) {
      if (value !== null && value !== undefined) {
        cleanedRequest[key as keyof T] = value instanceof Date ? value.toISOString() as any : value;
      }
    }
    return cleanedRequest;
  }

  protected createEmptyDataGridResponse<T>(): IDataGridResponse<T> {
    return { currentPage: 0, pageSize: 0, totalRecords: 0, totalPages: 0, sortColumn: '', sortDirection: '', pageData: [] };
  }

  /**
   * Generic method to fetch data table data from API and return an Observable
   */
  protected getDataTableData<TRequest extends IBasedDataGridRequest, TResponse extends IBasedDataGridResponse>(
    request: TRequest, endpoint: string, errorPrefix: string = 'Failed to load data'
  ): Observable<TResponse> {
    const cleanRequest = this.cleanRequestForApi(request);
    const responseSubject = new Subject<TResponse>();

    this.isLoading.set(true);

    this.handleApiService.getApiData<TResponse>({ url: endpoint, method: 'GET' }, cleanRequest).subscribe({
      next: (response: TResponse) => {
        const finalResponse = response || this.createEmptyDataGridResponse() as unknown as TResponse;
        responseSubject.next(finalResponse);
        if (response) this.updateRowsPerPageOptionsSignal(response.totalRecords, request.pageSize);
        else console.warn('Empty response received from server');
        this.isLoading.set(false);
      },
      error: (error: IApiError) => {
        this.isLoading.set(false);
        responseSubject.next(this.createEmptyDataGridResponse() as unknown as TResponse);

        const errorMessage = `${errorPrefix}. ${error.status === 404 ? 'The requested data could not be found.' :
            error.messages?.length ? error.messages.join(', ') :
              error.message || 'Please try again later.'
          }`;

        console.error(errorMessage, error);
        responseSubject.error(error);
      },
      complete: () => responseSubject.complete()
    });

    return responseSubject.asObservable();
  }

  /**
   * Updates rows per page options based on total records in the response
   */
  private updateRowsPerPageOptionsSignal(totalRecords: number, currentPageSize: number, customOptions?: RowsPerPageOption[]): void {
    let options: RowsPerPageOption[] = customOptions || [10, 25, 50];

    if (totalRecords > 50 && !options.includes(totalRecords)) options.push(totalRecords);
    if (options.length === 0) options = [10, 25, 50];
    if (!options.includes(currentPageSize)) {
      options.push(currentPageSize);
      options.sort((a, b) => a - b);
    }

    this.rowsPerPageOptions.set(options);
  }

}
