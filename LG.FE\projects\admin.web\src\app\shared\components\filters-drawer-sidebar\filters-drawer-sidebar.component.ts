import { CommonModule } from '@angular/common';
import {
  ChangeDetectionStrategy,
  Component,
  EventEmitter,
  Input,
  Output,
  TemplateRef,
  computed,
  signal
} from '@angular/core';
import { ButtonModule } from 'primeng/button';
import { DrawerModule } from 'primeng/drawer';
import { TooltipModule } from 'primeng/tooltip';

/**
 * Interface for filter drawer configuration
 */
export interface IFiltersDrawerConfig {
  /** Header text for the drawer */
  headerText?: string;
  /** Header icon for the drawer */
  headerIcon?: string;
  /** Position of the drawer */
  position?: 'left' | 'right' | 'top' | 'bottom';
  /** Width of the drawer */
  width?: string;
  /** Height of the drawer */
  height?: string;
  /** Whether to show the apply button */
  showApplyButton?: boolean;
  /** Whether to show the reset button */
  showResetButton?: boolean;
  /** Whether to show the close button */
  showCloseButton?: boolean;
  /** Apply button label */
  applyButtonLabel?: string;
  /** Reset button label */
  resetButtonLabel?: string;
  /** Close button label */
  closeButtonLabel?: string;
  /** Apply button icon */
  applyButtonIcon?: string;
  /** Reset button icon */
  resetButtonIcon?: string;
  /** Close button icon */
  closeButtonIcon?: string;
  /** Whether to block scroll when drawer is open */
  blockScroll?: boolean;
  /** Whether to show backdrop */
  modal?: boolean;
  /** Whether to dismiss on escape key */
  dismissibleOnEscape?: boolean;
}

/**
 * Interface for filter drawer action events
 */
export interface IFiltersDrawerActionEvent {
  action: 'apply' | 'reset' | 'close';
  event?: Event;
}

/**
 * Reusable filters drawer sidebar component using PrimeNG p-drawer
 * Provides a consistent way to display filters across different data grid lists
 */
@Component({
  selector: 'app-filters-drawer-sidebar',
  standalone: true,
  imports: [
    CommonModule,
    DrawerModule,
    ButtonModule,
    TooltipModule
  ],
  templateUrl: './filters-drawer-sidebar.component.html',
  styleUrl: './filters-drawer-sidebar.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class FiltersDrawerSidebarComponent {
  // Internal signals for reactive state management
  private _visible = signal<boolean>(false);
  private _config = signal<IFiltersDrawerConfig>({
    headerText: 'Filters',
    headerIcon: 'pi pi-filter',
    position: 'right',
    width: '400px',
    height: 'auto',
    showApplyButton: true,
    showResetButton: true,
    showCloseButton: true,
    applyButtonLabel: 'Apply Filters',
    resetButtonLabel: 'Reset',
    closeButtonLabel: 'Close',
    applyButtonIcon: 'pi pi-check',
    resetButtonIcon: 'pi pi-refresh',
    closeButtonIcon: 'pi pi-times',
    blockScroll: true,
    modal: true,
    dismissibleOnEscape: true
  });

  /**
   * Whether the drawer is visible
   */
  @Input()
  set visible(value: boolean) {
    this._visible.set(value);
  }
  get visible(): boolean {
    return this._visible();
  }

  /**
   * Configuration for the drawer
   */
  @Input()
  set config(value: IFiltersDrawerConfig) {
    this._config.update(current => ({ ...current, ...value }));
  }
  get config(): IFiltersDrawerConfig {
    return this._config();
  }

  /**
   * Template for the filter content
   */
  @Input() filterContentTemplate?: TemplateRef<any>;

  /**
   * Emitted when drawer visibility changes
   */
  @Output() visibleChange = new EventEmitter<boolean>();

  /**
   * Emitted when action buttons are clicked
   */
  @Output() actionClicked = new EventEmitter<IFiltersDrawerActionEvent>();

  // Computed properties for reactive access
  readonly isVisible = computed(() => this._visible());
  readonly drawerConfig = computed(() => this._config());

  /**
   * Handles drawer visibility changes
   */
  onVisibleChange(visible: boolean): void {
    this._visible.set(visible);
    this.visibleChange.emit(visible);
  }

  /**
   * Handles apply button click
   */
  onApplyClick(event: Event): void {
    this.actionClicked.emit({
      action: 'apply',
      event
    });
  }

  /**
   * Handles reset button click
   */
  onResetClick(event: Event): void {
    this.actionClicked.emit({
      action: 'reset',
      event
    });
  }

  /**
   * Handles close button click
   */
  onCloseClick(event: Event): void {
    this._visible.set(false);
    this.visibleChange.emit(false);
    this.actionClicked.emit({
      action: 'close',
      event
    });
  }

  /**
   * Opens the drawer
   */
  open(): void {
    this._visible.set(true);
    this.visibleChange.emit(true);
  }

  /**
   * Closes the drawer
   */
  close(): void {
    this._visible.set(false);
    this.visibleChange.emit(false);
  }

  /**
   * Toggles the drawer visibility
   */
  toggle(): void {
    const newVisible = !this._visible();
    this._visible.set(newVisible);
    this.visibleChange.emit(newVisible);
  }
}
