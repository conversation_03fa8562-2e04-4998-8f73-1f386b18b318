import { Injectable, signal } from '@angular/core';
import { Validators, AbstractControl, ValidationErrors, ValidatorFn, FormGroup, FormArray } from '@angular/forms';
import { getExampleNumber, parsePhoneNumberWithError } from "libphonenumber-js";
import examples from 'libphonenumber-js/examples.mobile.json';
import { ILanguageLevelsEnum } from '../GeneratedTsFiles';

@Injectable({
  providedIn: 'root'
})
export class CustomValidators {

  static nameValidator(control: AbstractControl, propertyName: string): ValidationErrors | null {
    const value = control.value;

    // Check if the value is empty
    if (!value) {
      return { required: `${propertyName} is required.` };
    }

    // Check if the value exceeds maximum length
    if (value.length > 50) {
      return { maxlength: `${propertyName} must be at most 50 characters.` };
    }

    // Check if the value contains invalid characters
    if (!/^[\p{L}\p{M}' .-]*$/u.test(value)) {
      return {
        pattern: `${propertyName} can only contain valid characters (letters, hyphens, spaces, and periods).`
      };
    }

    // If all checks pass, return null for no errors
    return null;
  }

  static password(control: AbstractControl): ValidationErrors | null {
    const value = control.value;
    if (!value) {
      return { required: 'Password cannot be empty.' };
    }
    if (value.length < 6) {
      return { minlength: 'Password must be at least 6 characters long.' };
    }
    if (!/[0-9]+/.test(value)) {
      return { pattern: 'Password must contain at least one digit.' };
    }
    if (!/[a-zA-Z]+/.test(value)) {
      return { pattern: 'Password must contain at least one letter.' };
    }
    return null;
  }

  static confirmPassword(control: AbstractControl): ValidationErrors | null {
    const passwordControl = control.root.get('password'); // Assuming 'password' is the form control name for the password field
    const confirmPasswordControl = control.root.get('verifyPassword'); // Assuming 'confirmPassword' is the form control name for the confirm password field

    if (passwordControl && confirmPasswordControl && passwordControl.value !== confirmPasswordControl.value) {
      return { passwordMismatch: 'Passwords do not match.' };
    }

    return null;
  }


  static passwordStrongCheck(control: AbstractControl): ValidationErrors | null {
    const value = control.value;
    if (!value) {
      return { required: 'Password cannot be empty.' };
    }
    if (value.length < 8) {
      return { minlength: 'Password must be at least 8 characters long.' };
    }
    if (!/[A-Z]+/.test(value)) {
      return { pattern: 'Password must contain at least one uppercase letter.' };
    }
    if (!/[a-z]+/.test(value)) {
      return { pattern: 'Password must contain at least one lowercase letter.' };
    }
    if (!/[0-9]+/.test(value)) {
      return { pattern: 'Password must contain at least one digit.' };
    }
    if (!/[\W_]+/.test(value)) {
      return { pattern: 'Password must contain at least one special symbol.' };
    }
    return null;
  }

  static confirmPasswordFirstTime(control: AbstractControl): ValidationErrors | null {
    const passwordControl = control.root.get('password'); // Assuming 'password' is the form control name for the password field
    const confirmPasswordControl = control.root.get('confirmPassword'); // Assuming 'confirmPassword' is the form control name for the confirm password field

    if (passwordControl && confirmPasswordControl && passwordControl.value !== confirmPasswordControl.value) {
      return { passwordMismatch: 'Passwords do not match.' };
    }

    return null;
  }

  // Custom asynchronous validator function to check if password and verifyPassword match
  static passwordMatchValidator(passwordValue: string): ValidatorFn {
    return (control: AbstractControl): Promise<{ [key: string]: any } | null> => {
      return new Promise((resolve) => {
        const password = passwordValue;
        const verifyPassword = control.value;
        if (password === verifyPassword) {
          resolve(null); // Passwords match, return null
        } else {
          resolve({ 'passwordMismatch': true }); // Passwords don't match, return error
        }
      });
    };
  }

  static userName(control: AbstractControl, propertyName: string): ValidationErrors | null {
    const value = control.value;
    if (!value) {
      return { required: `${propertyName} is required.` };
    }
    if (value.length < 1) {
      return { minlength: `${propertyName} must be at least 1 character.` };
    }
    if (value.length > 50) {
      return { maxlength: `${propertyName} must be at most 50 characters.` };
    }
    // Adjusted regular expression to match only lowercase letters and numbers
    if (!/^[a-z0-9]*$/.test(value)) {
      return { pattern: `${propertyName} must contain only lowercase letters and numbers. No special characters or uppercase letters are allowed.` };
    }
    return null;
  }

  static moreDetails(control: AbstractControl): ValidationErrors | null {

    const value = control.value;

    // Check if the value matches the pattern
    if (!/^[^<>=""\/]*$/.test(value)) {
      return { invalidCharacters: 'Invalid characters used. The characters <, >, =, \", / are not allowed.' };
    } else if (value && value.length > 500) {
      return ({ maxlength: 'The More Details field must be at most 500 characters.' });
    } else if (value && !/^[^<>=""\/]*$/.test(value)) {
      return ({ invalidCharacters: 'Invalid characters used. The characters <, >, =, \", / are not allowed.' });
    } else {
      return (null); // Return null if validation passes
    }
  }

  /**
   * Group name validator based on C# FluentValidation rule
   * Validates minimum length (1), maximum length (default 150), and allowed characters
   * Allowed characters: letters, marks, spaces, apostrophes, periods, and hyphens
   * @param maxLength - Maximum allowed length (default: 150)
   * @returns ValidatorFn
   */
  static groupName(maxLength: number = 150): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
      const value = control.value;

      // Check if the value is empty or null
      if (!value || value.trim() === '') {
        return { required: 'Group name is required.' };
      }

      // Check minimum length (at least 1 character after trimming)
      if (value.trim().length < 1) {
        return { minlength: 'Group name must be at least 1 character.' };
      }

      // Check maximum length
      if (value.length > maxLength) {
        return { maxlength: `Group name must be at most ${maxLength} characters.` };
      }

      // Check for valid characters using Unicode property escapes
      // \p{L} = Letters, \p{M} = Marks, ' = apostrophe, space, . = period, - = hyphen
      if (!/^[\p{L}\p{M}' .-]*$/u.test(value)) {
        return {
          pattern: 'Group name contains invalid characters. Only letters, spaces, apostrophes, periods, and hyphens are allowed.'
        };
      }

      // If all checks pass, return null for no errors
      return null;
    };
  }


  static phoneNumberValidator(phoneNumberWithDialcode: string, propertyName: string, regionCode: string): ValidationErrors | null {
    try {
      // Check if the value is empty
      if (!phoneNumberWithDialcode || phoneNumberWithDialcode.trim() === '' || phoneNumberWithDialcode.trim() === 'undefined') {
        return { required: `${propertyName} is required.` };
      }

      const phoneNumber = parsePhoneNumberWithError(phoneNumberWithDialcode, regionCode as any);
      if (!phoneNumber.isValid()) {
        // Get an example number for the region to suggest a valid format
        const exampleNumber = getExampleNumber(regionCode as any, examples);
        let suggestedFormat = 'e.g., ';
        if (exampleNumber) {
          suggestedFormat += phoneNumberWithDialcode.startsWith('+')
            ? exampleNumber.formatInternational()
            : exampleNumber.formatNational();
        } else {
          suggestedFormat += 'check the format for ' + regionCode;
        }

        return {
          invalidCharacters: `Invalid ${propertyName}. Please use a valid format, ${suggestedFormat}.`
        };
      }
    } catch (error) {
      // Handle parsing errors and suggest a format based on regionCode
      const exampleNumber = getExampleNumber(regionCode as any, examples);
      let suggestedFormat = 'e.g., ';
      if (exampleNumber) {
        // phoneNumberWithDialcode.startsWith('+')
        //   ? exampleNumber.formatInternational()
        //   :
        suggestedFormat += exampleNumber.formatNational();
      } else {
        suggestedFormat += 'check the format for ' + regionCode;
      }

      return {
        invalidCharacters: `Invalid ${propertyName}. Please use a valid format, ${suggestedFormat}.`
      };
    }

    // If all checks pass, return null for no errors
    return null;
  }

  // Custom email validator with stricter rules
  static strictEmailValidator(): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
      const value = control.value || '';
      if (!value) return { strictEmail: { value: 'Email cannot be empty' } };
      const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
      return emailRegex.test(value) ? null : { strictEmail: { value } };
    };
  }

  static emailDomainValidator(): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
      const value = control.value?.toLowerCase() || '';
      if (!value) {
        return { required: 'Email is required.' };
      }

      const requiredStrings = ['mlk', 'mylingokids', 'lingogalaxy'];
      const hasRequiredString = requiredStrings.some(str =>
        value.includes(str.toLowerCase())
      );

      return hasRequiredString
        ? null
        : {
          domainRequirement: {
            value,
            message: "The email must contain one of the following: 'mlk', 'mylingokids', or 'lingogalaxy'."
          }
        };
    };
  }

  static validateLanguageLevelRange(
    selectedLevels: ILanguageLevelsEnum,
    isRequired: boolean = false
  ): { isValid: boolean, errorMessage?: string } {
    // If no selection is made
    if (selectedLevels === ILanguageLevelsEnum.None) {
      if (isRequired) {
        return {
          isValid: false,
          errorMessage: 'A language level selection is required.'
        };
      }
      return { isValid: true };
    }

    // Determine required levels based on highest selection
    let requiredLevels: ILanguageLevelsEnum = ILanguageLevelsEnum.None;

    if ((selectedLevels & ILanguageLevelsEnum.C2) !== 0) {
      requiredLevels = ILanguageLevelsEnum.A1 | ILanguageLevelsEnum.A2 | ILanguageLevelsEnum.B1 |
        ILanguageLevelsEnum.B2 | ILanguageLevelsEnum.C1 | ILanguageLevelsEnum.C2;
    } else if ((selectedLevels & ILanguageLevelsEnum.C1) !== 0) {
      requiredLevels = ILanguageLevelsEnum.A1 | ILanguageLevelsEnum.A2 | ILanguageLevelsEnum.B1 |
        ILanguageLevelsEnum.B2 | ILanguageLevelsEnum.C1;
    } else if ((selectedLevels & ILanguageLevelsEnum.B2) !== 0) {
      requiredLevels = ILanguageLevelsEnum.A1 | ILanguageLevelsEnum.A2 | ILanguageLevelsEnum.B1 |
        ILanguageLevelsEnum.B2;
    } else if ((selectedLevels & ILanguageLevelsEnum.B1) !== 0) {
      requiredLevels = ILanguageLevelsEnum.A1 | ILanguageLevelsEnum.A2 | ILanguageLevelsEnum.B1;
    } else if ((selectedLevels & ILanguageLevelsEnum.A2) !== 0) {
      requiredLevels = ILanguageLevelsEnum.A1 | ILanguageLevelsEnum.A2;
    } else if ((selectedLevels & ILanguageLevelsEnum.A1) !== 0) {
      requiredLevels = ILanguageLevelsEnum.A1;
    }

    // Check if all required levels are selected
    if ((selectedLevels & requiredLevels) === requiredLevels) {
      return { isValid: true };
    }

    // Find missing levels
    const missingLevels = requiredLevels & ~selectedLevels;
    const missingNames: string[] = [];

    if (missingLevels & ILanguageLevelsEnum.A1) missingNames.push('A1');
    if (missingLevels & ILanguageLevelsEnum.A2) missingNames.push('A2');
    if (missingLevels & ILanguageLevelsEnum.B1) missingNames.push('B1');
    if (missingLevels & ILanguageLevelsEnum.B2) missingNames.push('B2');
    if (missingLevels & ILanguageLevelsEnum.C1) missingNames.push('C1');
    if (missingLevels & ILanguageLevelsEnum.C2) missingNames.push('C2');

    return {
      isValid: false,
      errorMessage: `Missing required language levels: ${missingNames.join(', ')}. When selecting a language level, all lower levels must also be selected.`
    };
  }

  // New validator to match two fields
  static matchFields(fieldToMatch: string, fieldName: string, comparisonFieldName: string): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
      const value = control.value;
      const fieldToMatchControl = control.root.get(fieldToMatch);

      if (!value) {
        return { required: `${fieldName} is required.` };
      }

      if (!fieldToMatchControl) {
        console.warn(`Control '${fieldToMatch}' not found in form group`);
        return null;
      }

      const fieldToMatchValue = fieldToMatchControl.value;

      return String(value).toLowerCase() === String(fieldToMatchValue).toLowerCase()
        ? null
        : {
          mismatch: {
            value,
            message: `${fieldName} must match ${comparisonFieldName}.`
          }
        };
    };
  }

  static findInvalidControls(userForm: FormGroup | FormArray, prefix?: string, customMessages?: { [key: string]: string }): string[] {
    const invalid: string[] = [];
    const processedPaths = new Set<string>();

    if (userForm instanceof FormGroup) {
      Object.keys(userForm.controls).forEach(controlName => {
        const control = userForm.controls[controlName];
        const fullControlPath = prefix ? `${prefix}.${controlName}` : controlName;

        // For FormArrays, only check array-level errors and individual control errors
        if (control instanceof FormArray) {
          // Check FormArray level errors first
          if (control.errors) {
            const messages = this.extractErrorMessages(fullControlPath, control.errors, customMessages);
            messages.forEach(msg => {
              if (!processedPaths.has(msg)) {
                invalid.push(msg);
                processedPaths.add(msg);
              }
            });
          }

          // Process each control in the array
          control.controls.forEach((arrayControl, index) => {
            const arrayPath = `${fullControlPath}[${index}]`;
            if (arrayControl.errors) {
              const messages = this.extractErrorMessages(arrayPath, arrayControl.errors, customMessages);
              messages.forEach(msg => {
                if (!processedPaths.has(msg)) {
                  invalid.push(msg);
                  processedPaths.add(msg);
                }
              });
            }
          });
        } else if (control instanceof FormGroup) {
          // Recursively check nested FormGroups
          const nestedInvalid = this.findInvalidControls(control, fullControlPath, customMessages);
          nestedInvalid.forEach(msg => {
            if (!processedPaths.has(msg)) {
              invalid.push(msg);
              processedPaths.add(msg);
            }
          });
        } else if (control.invalid && control.errors) {
          // Handle regular form controls
          const messages = this.extractErrorMessages(fullControlPath, control.errors, customMessages);
          messages.forEach(msg => {
            if (!processedPaths.has(msg)) {
              invalid.push(msg);
              processedPaths.add(msg);
            }
          });
        }
      });
    } else if (userForm instanceof FormArray) {
      // Check FormArray level errors first
      if (userForm.errors) {
        const messages = this.extractErrorMessages(prefix || '', userForm.errors, customMessages);
        messages.forEach(msg => {
          if (!processedPaths.has(msg)) {
            invalid.push(msg);
            processedPaths.add(msg);
          }
        });
      }

      // Process each control in the array
      userForm.controls.forEach((arrayControl, index) => {
        const arrayPath = `${prefix}[${index}]`;
        if (arrayControl instanceof FormGroup || arrayControl instanceof FormArray) {
          const nestedInvalid = this.findInvalidControls(arrayControl, arrayPath, customMessages);
          nestedInvalid.forEach(msg => {
            if (!processedPaths.has(msg)) {
              invalid.push(msg);
              processedPaths.add(msg);
            }
          });
        } else if (arrayControl.invalid && arrayControl.errors) {
          const messages = this.extractErrorMessages(arrayPath, arrayControl.errors, customMessages);
          messages.forEach(msg => {
            if (!processedPaths.has(msg)) {
              invalid.push(msg);
              processedPaths.add(msg);
            }
          });
        }
      });
    }

    return Array.from(new Set(invalid)); // Additional deduplication as safety
  }

  /**
   * Extracts error messages from a control's validation errors.
   */

  // ... existing code ...

  private static extractErrorMessages(controlPath: string, errors: ValidationErrors, customMessages?: { [key: string]: string }): string[] {
    const errorMessages: string[] = [];

    // Check for custom message first
    // Check for custom message first
    if (customMessages) {
      // Try exact path first
      if (customMessages[controlPath]) {
        return [customMessages[controlPath]];
      }

      // For form arrays, try the base path without index
      const basePathWithoutIndex = controlPath.replace(/\[\d+\]/g, '');
      if (customMessages[basePathWithoutIndex]) {
        return [customMessages[basePathWithoutIndex]];
      }
    }

    // Format the control path into a readable name
    const formatControlPath = (path: string): string => {
      // Handle array indices
      path = path.replace(/\[\d+\]/g, '');

      // Split the path into parts
      const parts = path.split('.');

      // Format each part
      return parts
        .map(part => {
          // Convert camelCase to Title Case with spaces
          return part
            .replace(/([A-Z])/g, ' $1')
            .replace(/^./, str => str.toUpperCase())
            .trim();
        })
        .join(' › '); // Use a separator between parts
    };

    Object.entries(errors).forEach(([errorKey, errorValue]) => {
      let errorMessage: string = '';
      const fieldName = formatControlPath(controlPath);

      switch (errorKey) {
        case 'required':
          errorMessage = `${fieldName} is required`;
          break;
        case 'minlength':
          errorMessage = `${fieldName} must be at least ${errorValue.requiredLength} characters`;
          break;
        case 'maxlength':
          errorMessage = `${fieldName} must be at most ${errorValue.requiredLength} characters`;
          break;
        case 'pattern':
          if (typeof errorValue === 'string') {
            errorMessage = errorValue;
          } else if (errorValue.message) {
            errorMessage = errorValue.message;
          } else {
            errorMessage = `${fieldName} has an invalid format`;
          }
          break;
        case 'email':
          errorMessage = `${fieldName} must be a valid email address`;
          break;
        case 'min':
          errorMessage = `${fieldName} must be at least ${errorValue.min}`;
          break;
        case 'max':
          errorMessage = `${fieldName} must be at most ${errorValue.max}`;
          break;
        case 'passwordMismatch':
          errorMessage = typeof errorValue === 'string' ? errorValue : 'Passwords do not match';
          break;
        case 'invalidCharacters':
          errorMessage = typeof errorValue === 'string' ? errorValue : `${fieldName} contains invalid characters`;
          break;
        default:
          // Handle custom error messages that might be objects with a message property
          if (errorValue && typeof errorValue === 'object' && 'message' in errorValue) {
            errorMessage = errorValue.message;
          } else if (typeof errorValue === 'string') {
            errorMessage = errorValue;
          } else {
            errorMessage = `${fieldName} is invalid`;
          }
      }

      // Avoid duplicate messages
      if (errorMessage && !errorMessages.includes(errorMessage)) {
        errorMessages.push(errorMessage);
      }
    });

    return errorMessages;
  }

// ... existing code ...

static validateLanguageLevelAsRange(isRequired: boolean = true, controlPath?: string): ValidatorFn {
  return (control: AbstractControl): ValidationErrors | null => {
    const selectedLevels: ILanguageLevelsEnum = control.value;
    const fieldName = controlPath ? controlPath : 'Language Level';

    // If not required and no levels selected, validation passes
    if (!isRequired && selectedLevels === ILanguageLevelsEnum.None) {
      return null;
    }

    // If required and no levels selected, validation fails
    if (isRequired && selectedLevels === ILanguageLevelsEnum.None) {
      return {
        invalidLanguageLevel: {
          message: `${fieldName} selection is required.`
        }
      };
    }

    // If we have any level selected, validate it's a proper range
    if (selectedLevels !== ILanguageLevelsEnum.None) {
      // Find the highest selected level and required levels
      let requiredLevels: ILanguageLevelsEnum = ILanguageLevelsEnum.None;

      if ((selectedLevels & ILanguageLevelsEnum.C2) !== 0) {
        requiredLevels = ILanguageLevelsEnum.A1 | ILanguageLevelsEnum.A2 | ILanguageLevelsEnum.B1 |
        ILanguageLevelsEnum.B2 | ILanguageLevelsEnum.C1 | ILanguageLevelsEnum.C2;
      } else if ((selectedLevels & ILanguageLevelsEnum.C1) !== 0) {
        requiredLevels = ILanguageLevelsEnum.A1 | ILanguageLevelsEnum.A2 | ILanguageLevelsEnum.B1 |
        ILanguageLevelsEnum.B2 | ILanguageLevelsEnum.C1;
      } else if ((selectedLevels & ILanguageLevelsEnum.B2) !== 0) {
        requiredLevels = ILanguageLevelsEnum.A1 | ILanguageLevelsEnum.A2 | ILanguageLevelsEnum.B1 |
        ILanguageLevelsEnum.B2;
      } else if ((selectedLevels & ILanguageLevelsEnum.B1) !== 0) {
        requiredLevels = ILanguageLevelsEnum.A1 | ILanguageLevelsEnum.A2 | ILanguageLevelsEnum.B1;
      } else if ((selectedLevels & ILanguageLevelsEnum.A2) !== 0) {
        requiredLevels = ILanguageLevelsEnum.A1 | ILanguageLevelsEnum.A2;
      } else if ((selectedLevels & ILanguageLevelsEnum.A1) !== 0) {
        requiredLevels = ILanguageLevelsEnum.A1;
      }

      // Check if all required levels are selected
      if ((selectedLevels & requiredLevels) === requiredLevels) {
        return null;
      }

      // Find missing levels
      const missingLevels = requiredLevels & ~selectedLevels;
      const missingNames: string[] = [];

      if (missingLevels & ILanguageLevelsEnum.A1) missingNames.push('A1');
      if (missingLevels & ILanguageLevelsEnum.A2) missingNames.push('A2');
      if (missingLevels & ILanguageLevelsEnum.B1) missingNames.push('B1');
      if (missingLevels & ILanguageLevelsEnum.B2) missingNames.push('B2');
      if (missingLevels & ILanguageLevelsEnum.C1) missingNames.push('C1');
      if (missingLevels & ILanguageLevelsEnum.C2) missingNames.push('C2');

      return {
        invalidLanguageLevel: {
          message: `Missing required language levels: ${missingNames.join(', ')}. When selecting a language level, all lower levels must also be selected.`,
          missingLevels: missingNames
        }
      };
    }

    return null;
  };
}

// ... existing code ...

  static atLeastOneEmailValidator(): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
      if (!(control instanceof FormArray)) {
        return null;
      }

      return control.length > 0
        ? null
        : { atLeastOneEmail: { message: 'At least one email address is required' } };
    };
  }

  static atLeastOneNativeLanguageValidator(): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
      if (!(control instanceof FormArray)) {
        return null; // Return null if not a FormArray
      }

      const hasNative = control.controls.some((ctrl: AbstractControl) =>
        ctrl.get('isNative')?.value === true
      );

      return hasNative ? null :
        {
          atLeastOneNativeRequired: {
            hasNative,
            message: "At least one native language is required."
          }
        }
    };
  }

  // TODO: not needed anymore
  // static atLeastOnePaymentMethodValidator(group: AbstractControl): ValidationErrors | null {
  //   const revolutAccount = group.get('revolutAccount')?.value;
  //   const paypalEmail = group.get('paypalEmail')?.value;

  //   // Return null (valid) if at least one field is filled, otherwise return an error
  //   return revolutAccount || paypalEmail ? null : { atLeastOneRequired: true };
  // }

  static nonEmptyStringValidator(): ValidatorFn {
    return (control: AbstractControl): { [key: string]: any } | null => {
      const value = control.value;
      if (value === '' || (typeof value === 'string' && value.trim() === '')) {
        return { emptyString: { value: control.value } };
      }
      return null;
    };
  }

  static teachingLanguagesValidator(): ValidatorFn {
    return (formGroup: AbstractControl): ValidationErrors | null => {
      // Get speaking languages names
      const speakingLanguagesControls = (formGroup.get('personalInformation.speakingLanguages') as FormArray)?.controls || [];
      const speakingLanguageNames = speakingLanguagesControls
        .map(control => {
          const value = control.get('language')?.value;
          return value ? String(value) : null; // Ensure string type for comparison
        })
        .filter(Boolean);

      // Get teaching languages names
      const teachingLanguagesControls = (formGroup.get('workProfile.teachingLanguages') as FormArray)?.controls || [];
      const teachingLanguageNames = teachingLanguagesControls
        .map(control => {
          const value = control.get('teachingLanguageName')?.value;
          return value ? String(value) : null; // Use teachingLanguageName instead of ID
        })
        .filter(Boolean);

      // Log for debugging
      console.log('Speaking Language Names:', speakingLanguageNames);
      console.log('Teaching Language Names:', teachingLanguageNames);

      // If either array is empty, skip validation
      if (!speakingLanguageNames.length || !teachingLanguageNames.length) {
        console.log('One or both language name arrays are empty, skipping validation');
        return null;
      }

      // Check if every teaching language name is in speaking language names
      const invalidTeachingLanguages = teachingLanguageNames.some(
        (teachingLangName: any) => {
          const isIncluded = speakingLanguageNames.includes(teachingLangName);
          console.log(`Checking ${teachingLangName}: ${isIncluded ? 'Found' : 'Not Found'} in speakingLanguageNames`);
          return !isIncluded;
        }
      );

      // Return validation result
      console.log('Invalid Teaching Languages Detected:', invalidTeachingLanguages);
      return invalidTeachingLanguages
        ? { teachingLanguagesNotInSpeaking: true }
        : null;
    };
  }
  // Add more validators as needed...
}
