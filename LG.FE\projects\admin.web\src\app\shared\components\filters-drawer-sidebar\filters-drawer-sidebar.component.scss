// Filters Drawer Sidebar Component Styles
@use "mixins";

:host {
  display: block;
}

// Global drawer styles
:host ::ng-deep {
  .filters-drawer {
    .p-drawer {
      // Ensure proper z-index for drawer
      z-index: 1100;

      // Custom width for different screen sizes
      @include mixins.breakpoint(mobile) {
        width: 100% !important;
        max-width: 100% !important;
      }

      @include mixins.breakpoint(tablet) {
        width: 450px !important;
        max-width: 450px !important;
      }

      @include mixins.breakpoint(desktop) {
        width: 400px !important;
        max-width: 400px !important;
      }
    }
    
    .p-drawer-content {
      padding: 0 !important;
      height: 100vh !important;
      display: flex !important;
      flex-direction: column !important;
      overflow: hidden !important;
    }

    // Ensure actions are always visible at bottom
    .filters-drawer-actions {
      position: sticky !important;
      bottom: 0 !important;
      margin-top: auto !important;
    }

    // Ensure filter components fit within the scrollable area
    app-teachers-list-filters {
      height: 100% !important;
      overflow: hidden !important;
      display: block !important;
    }
  }
}

// Drawer container styles
.filters-drawer-container {
  background: var(--surface-ground);
  height: 100vh;
  display: flex;
  flex-direction: column;
  
  // Header styles - Fixed at top
  .filters-drawer-header {
    background: var(--surface-card);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    flex-shrink: 0; // Prevent shrinking
    position: relative;
    z-index: 10;
    
    h3 {
      color: var(--text-color);
      font-size: 1.25rem;
      
      @include mixins.breakpoint(mobile) {
        font-size: 1.1rem;
      }
    }
    
    .p-button {
      width: 2.5rem;
      height: 2.5rem;
      
      &:hover {
        transition: transform 0.2s ease;
      }
    }
  }
  
  // Content area styles
  .filters-drawer-content {
    background: var(--surface-ground);
    flex: 1;
    min-height: 0; // Important for flex child to allow shrinking
    max-height: calc(100vh - 160px); // Reserve space for header and actions

    // Ensure proper scrolling
    overflow-y: auto;
    overflow-x: hidden;
    
    // Custom scrollbar styling
    &::-webkit-scrollbar {
      width: 6px;
    }
    
    &::-webkit-scrollbar-track {
      background: var(--surface-border);
      border-radius: 3px;
    }
    
    &::-webkit-scrollbar-thumb {
      background: var(--primary-color);
      border-radius: 3px;
      
      &:hover {
        background: var(--primary-color-text);
      }
    }
    
    // Responsive padding
    @include mixins.breakpoint(mobile) {
      padding: 1rem !important;
    }
    
    @include mixins.breakpoint(tablet) {
      padding: 1.25rem !important;
    }
  }
  
  // Actions area styles - Always visible at bottom
  .filters-drawer-actions {
    background: var(--surface-card);
    box-shadow: 0 -1px 3px rgba(0, 0, 0, 0.1);
    flex-shrink: 0; // Prevent shrinking
    position: relative;
    z-index: 10;
    min-height: 80px; // Ensure minimum height for buttons
    display: flex;
    align-items: center;
    
    .p-button {
      min-height: 2.75rem;
      font-weight: 600;
      border-radius: 8px;
      
      // Responsive button sizing
      @include mixins.breakpoint(mobile) {
        min-height: 3rem;
        font-size: 0.95rem;
      }
      
      &.p-button-outlined {
        border-width: 2px;
      }
      
      &:hover {
        transition: transform 0.2s ease;
      }
      
    }
    
    // Button gap responsive
    .flex {
      @include mixins.breakpoint(mobile) {
        gap: 0.75rem !important;
      }
    }
  }
}

// Animation enhancements
.filters-drawer-container {
  // animation: slideInRight 0.3s ease-out;
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

// Accessibility improvements
.filters-drawer-header,
.filters-drawer-actions {
  .p-button {
    &:focus {
      outline: 2px solid var(--primary-color);
      outline-offset: 2px;
    }
  }
}

// Enterprise-grade styling
.filters-drawer-container {
  .filters-drawer-header {
    border-bottom-color: var(--surface-border);
  }
  
  .filters-drawer-actions {
    border-top-color: var(--surface-border);
  }
}

// Dark mode support
@media (prefers-color-scheme: dark) {
  .filters-drawer-container {
    .filters-drawer-header,
    .filters-drawer-actions {
      box-shadow: 0 1px 3px rgba(255, 255, 255, 0.1);
    }
  }
}

// Debug styles (can be removed in production)
// Uncomment these to debug layout issues
/*
.filters-drawer-container {
  border: 2px solid red !important;

  .filters-drawer-header {
    border: 2px solid blue !important;
  }

  .filters-drawer-content {
    border: 2px solid green !important;
  }

  .filters-drawer-actions {
    border: 2px solid orange !important;
  }
}
*/
