<div class="card surface-card shadow-2 border-round p-3 hover:shadow-3 transition-all transition-duration-200">
    <!-- Header Section -->
    <div class="flex flex-column sm:flex-row justify-content-between align-items-start sm:align-items-center gap-3 pb-3 border-bottom-1 surface-border">
      <div class="flex align-items-center gap-2">
        <img [src]="generalService.getImageUrlForLanguage(languageName())"
             alt="Language Flag"
             class="mini-flag "
             [title]="languageName()" />
        <span class="text-lg font-semibold text-900">{{ groupDisplayName() }}</span>
      </div>
      <p-tag
        [value]="groupStatusText()"
        [severity]="groupStatusSeverity()"
        styleClass="text-xs">
      </p-tag>
    </div>

    <!-- Content Section -->
    <div class="py-3">
      <!-- Students Info -->
      <div class="flex align-items-center gap-2 mb-2">
        <i class="pi pi-users text-600"></i>
        <span class="text-sm text-600">{{ studentsCount() }} student{{ studentsCount() !== 1 ? 's' : '' }}</span>
      </div>

      <!-- Students Names -->
      @if (item().students && item().students.length > 0) {
        <div class="flex flex-wrap gap-1 mb-2">
          @for (student of item().students; track student.userId) {
            <p-tag
              [value]="student.firstName + ' ' + student.lastName"
              severity="info"
              styleClass="text-xs">
            </p-tag>
          }
        </div>
      }

      <!-- Package Info -->
      <div class="flex align-items-center gap-3 text-sm text-600">
        @if (activePackagesCount() > 0) {
          <div class="flex align-items-center gap-1">
            <i class="pi pi-check-circle text-green-500"></i>
            <span>{{ activePackagesCount() }} active package{{ activePackagesCount() !== 1 ? 's' : '' }}</span>
          </div>
        }
        @if (inactivePackagesCount() > 0) {
          <div class="flex align-items-center gap-1">
            <i class="pi pi-clock text-orange-500"></i>
            <span>{{ inactivePackagesCount() }} inactive package{{ inactivePackagesCount() !== 1 ? 's' : '' }}</span>
          </div>
        }
      </div>
    </div>

    <!-- Actions Section -->
    <div class="flex flex-column sm:flex-row gap-2 pt-3 border-top-1 surface-border w-full">
      <p-button
        label="Manage Group"
        icon="pi pi-cog"
        class="w-full"
        styleClass="p-button-primary p-button-sm w-full"
        (click)="onSelectListGroupItem()">
      </p-button>
    </div>
  </div>