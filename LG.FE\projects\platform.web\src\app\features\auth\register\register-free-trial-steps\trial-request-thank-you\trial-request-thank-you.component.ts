import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, inject, signal, type OnInit } from '@angular/core';
import { RouterModule } from '@angular/router';
import { ButtonModule } from 'primeng/button';
import { ActivatedRoute } from '@angular/router';
import { GeneralService } from 'SharedModules.Library';
import { AvailableStudentsForGroupsListComponent } from '@platform.app/shared/components/student-groups/available-students-for-groups-list/available-students-for-groups-list.component';
import { GalacticSuccessCardComponent, type GalacticSuccessConfig } from '../../../../../shared/components/galactic-success-card/galactic-success-card.component';

@Component({
    selector: 'app-trial-request-thank-you',
    imports: [
        CommonModule,
        ButtonModule,
        RouterModule,
        AvailableStudentsForGroupsListComponent,
        GalacticSuccessCardComponent,
    ],
    templateUrl: './trial-request-thank-you.component.html',
    styleUrl: './trial-request-thank-you.component.scss',
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class TrialRequestThankYouComponent implements OnInit {
  private readonly generalService = inject(GeneralService);
  private readonly route = inject(ActivatedRoute);

  // Reactive state using signals
  studentId = signal<string | null>(null);
  teachingLanguageId = signal<string | null>(null);
  teachingLanguageName = signal<string | null>(null);
  isAnimationComplete = signal(false);

  // Galactic success card configuration
  galacticConfig = signal<GalacticSuccessConfig>({
    title: '🚀 Request received successfully!',
    subtitle: 'Your learning adventure begins now',
    badgeText: 'FREE',
    badgeIcon: 'pi pi-star-fill',
    equipmentItems: ['🛡️ No cost, no risk', '👨‍🚀 Expert teachers', '📡 24h support'],
    showDestinationName: true,
    destinationName: '',
    theme: 'success'
  });

  ngOnInit(): void {
    // Subscribe to query parameters and update signals
    this.route.queryParams.subscribe(params => {
      this.studentId.set(params['studentId'] || null);
      this.teachingLanguageId.set(params['teachingLanguageId'] || null);
      this.teachingLanguageName.set(params['teachingLanguageName'] || null);

      // Update galactic config with language name
      this.updateGalacticConfig();

      console.log('Query Params:', {
        studentId: this.studentId(),
        teachingLanguageId: this.teachingLanguageId(),
        teachingLanguageName: this.teachingLanguageName()
      });
    });

    // Trigger animation completion after a delay
    setTimeout(() => {
      this.isAnimationComplete.set(true);
    }, 1500);
  }

  /**
   * Gets the display name for the teaching language
   */
  getLanguageDisplayName(): string {
    return this.teachingLanguageName() || 'your selected language';
  }

  /**
   * Updates the galactic config with current language information
   */
  private updateGalacticConfig(): void {
    const currentConfig = this.galacticConfig();
    this.galacticConfig.set({
      ...currentConfig,
      subtitle: `Your ${this.getLanguageDisplayName()} adventure begins now`,
      badgeText: `FREE ${this.getLanguageDisplayName()} Trial Lesson`
    });
  }

  /**
   * Handles navigation to dashboard
   */
  navigateToDashboard(): void {
    console.log('Navigating to dashboard...');
  }

  /**
   * Handles navigation to students page
   */
  navigateToStudents(): void {
    console.log('Navigating to students page...');
  }

  /**
   * Handles student selection from the available students list
   */
  onStudentSelected(student: any): void {
    console.log('Student selected for grouping:', student);
    // Handle student selection logic here
  }
}