<!-- Applied Filters Container -->

  <div class="applied-filters-container surface-card p-3 border-round" (click)="$event.stopPropagation()"
  *ngIf="isVisible()"
  (click)="$event.stopPropagation()"
  role="region"
  [attr.aria-label]="config.headerText || 'Applied Filters'"
>
  <!-- Header Section - Two Column Layout -->
  <div class="applied-filters-header flex justify-content-between align-items-start gap-3">

    <!-- Left Column: Title and Filter Tags -->
    <div class="flex-1">
      <!-- Title Row -->
      <div class="flex align-items-center gap-2 mb-2">
        <!-- Header Icon -->
        <i
          *ngIf="config.headerIcon"
          [class]="config.headerIcon + ' text-primary font-bold'"
          aria-hidden="true"
        ></i>

        <!-- Header Text -->
        <h5 class="m-0 text-900 font-medium font-bold">
          {{ config.headerText }}
        </h5>
      </div>

      <!-- Filter Tags Row -->
      <div class="applied-filters-tags flex flex-wrap gap-2" role="list">

        <!-- Visible Filter Tags -->
        <ng-container *ngFor="let filter of visibleFilters(); trackBy: trackByFilterId">

          <!-- Custom Template -->
          <ng-container *ngIf="filterTagTemplate; else defaultFilterTag">
            <ng-container
              *ngTemplateOutlet="filterTagTemplate; context: {
                $implicit: filter,
                filter: filter,
                remove: onFilterRemove.bind(this, filter)
              }"
            ></ng-container>
          </ng-container>

          <!-- Default Filter Tag Template -->
          <ng-template #defaultFilterTag>
            <div
              class="filter-chip flex align-items-center gap-2 surface-100 border-round-2xl px-3 py-2"
              role="listitem"
              [attr.aria-label]="'Filter: ' + filter.label + (filter.removable !== false ? '. Press to remove' : '')"
            >
              <!-- Filter Icon -->
              <i
                *ngIf="filter.icon"
                [class]="filter.icon + ' text-600 text-sm'"
                aria-hidden="true"
              ></i>

              <!-- Filter Label -->
              <span class="text-900 font-medium">{{ filter.label }}</span>

              <!-- Remove Button -->
              <button
                *ngIf="filter.removable !== false"
                pButton
                class="p-button-rounded p-button-text p-button-sm"
                icon="pi pi-times"
                [attr.aria-label]="'Remove filter: ' + filter.label"
                (click)="onFilterRemove(filter, $event)"
                tabindex="0"
              ></button>
            </div>
          </ng-template>

        </ng-container>

        <!-- Hidden Filters Indicator -->
        <div
          *ngIf="hiddenFiltersCount() > 0"
          class="filter-chip flex align-items-center gap-2 surface-200 border-round-2xl px-3 py-2"
          role="listitem"
          [attr.aria-label]="'And ' + hiddenFiltersCount() + ' more filters'"
        >
          <i class="pi pi-ellipsis-h text-600 text-sm" aria-hidden="true"></i>
          <span class="text-600 font-medium">+{{ hiddenFiltersCount() }} more</span>
        </div>

      </div>
    </div>

    <!-- Right Column: Action Buttons -->
    <div class="flex flex-column align-items-end gap-2 flex-shrink-0">
      <p-button
        *ngIf="showClearAll()"
        size="small"
        [label]="config.clearAllLabel!"
        [icon]="config.clearAllIcon!"
        styleClass="p-button-outlined p-button-danger"
        [attr.aria-label]="'Clear all applied filters'"
        (click)="onClearAll($event)"
        tabindex="0"
      ></p-button>
      <ng-content select="[extraButton]"></ng-content>
    </div>
  </div>
</div>
