<div class="grid">
    <div class="col-12">
        <!-- {{authService.getUserRole() | json }} -->
        <app-inner-header-card [breadcrumbs]="breadcrumbs" [actionButtonLabel]="
        authService.getUserRole() === IUserRole.PARENT ? 'New Group' : ''" actionButtonIcon="pi pi-users"
            (actionButtonClick)="openNewGroupDialog()">
            <div breadcrumb-label>
                {{ breadcrumbs[0].label }}
            </div>

        </app-inner-header-card>

    </div>


    <div class="col-12">
        <div class=" surface-card shadow-2">
            <div class="surface-section py-3 lg:px-3">
                <div class="flex flex-column md:align-items-center md:justify-content-between md:flex-row">

                    <div class="flex flex-column md:flex-row md:justify-content-between">
                        <div class="flex align-items-center md:mt-0">

                            <p-iconfield>
                                <p-inputicon styleClass="pi pi-search" />
                                <input type="text" pInputText placeholder="Search by group name" [value]="searchQuery()"
                                    (input)="onSearchInputChange($event)" />
                            </p-iconfield>
                        </div>
                    </div>
                    <div class="font-medium text-lg md:text-3xl text-900">&nbsp;</div>
                </div>
            </div>
        </div>
    </div>

</div>

<div class="">
    <div class="grid">

        <!-- {{studentGroups$() | json}} -->
        @defer (on timer(300ms)) {

        @if(isLoading()) {
            <ng-container *ngTemplateOutlet="loading"></ng-container>
        } @else {
            <!-- {{studentGroups$() | json}} -->
            @if (totalRecords() === 0) {

              @let hasCreateGroupPermission = this.permissionService.hasPermission(this.authService.getUserClaims(), 
              ['groups',
                'create']);

                <div class="col-12">
                    <div class="w-full surface-card shadow-2 py-3 flex flex-column align-items-center justify-content-center">
                        <app-empty-data-image-text
                            [emptyDataText]="'No Student Groups found.'"
                            [showAction]="hasCreateGroupPermission ? true : false"
                            [showActionButtonText]="'Create New Group'"
                            (onActionSelected)="this.openNewGroupDialog()">
                        </app-empty-data-image-text>
                    </div>
                </div>
            } @else {
                @for (item of studentGroupsData(); track item.id) {
                    <div class="col-12 md:col-6 xxl:col-3">
                        <app-group-mini-info-card [item]="item" />
                    </div>
                }
            }
        }
        } @placeholder {

            <ng-container *ngTemplateOutlet="loading"></ng-container>



        }


    </div>

    <p-paginator
        (onPageChange)="onPageChange($event)"
        [first]="first()"
        [rows]="rows()"
        [totalRecords]="totalRecords()"
        [rowsPerPageOptions]="[5, 10, 20, 30]"
        [showCurrentPageReport]="true"
        currentPageReportTemplate="Showing {first} to {last} of {totalRecords} groups" />
</div>

<ng-template #loading>
    <div class="flex w-full">
        <div class="col-12 md:col-6 xxl:col-3">
            <lib-skeleton-loader shape="circle" layout="card" width="w-full" height="5rem" [count]="1"
                styleClass="mr-2 w-full sm:w-full" />
        </div>

        <div class="col-12 md:col-6 xxl:col-3">
            <lib-skeleton-loader shape="circle" layout="card" width="w-full" height="5rem" [count]="1"
                styleClass="mr-2 w-full sm:w-full" />
        </div>

        <div class="col-12 md:col-6 xxl:col-3">
            <lib-skeleton-loader shape="circle" layout="card" width="w-full" height="5rem" [count]="1"
                styleClass="mr-2 w-full sm:w-full" />
        </div>

        <div class="col-12 md:col-6 xxl:col-3">
            <lib-skeleton-loader shape="circle" layout="card" width="w-full" height="5rem" [count]="1"
                styleClass="mr-2 w-full sm:w-full" />
        </div>
    </div>
</ng-template>