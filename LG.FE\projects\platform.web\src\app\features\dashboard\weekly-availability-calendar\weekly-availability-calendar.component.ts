import { CommonModule } from '@angular/common';
import {
    AfterViewInit,
    ChangeDetectorRef,
    Component,
    DestroyRef,
    ElementRef,
    inject,
    model,
    OnInit,
    Renderer2,
    signal,
    ViewChild,
    ViewContainerRef,
} from '@angular/core';
import moment from 'moment';
import 'moment-timezone';
import dayGridPlugin from '@fullcalendar/daygrid';
import timeGridPlugin from '@fullcalendar/timegrid';
import interactionPlugin from '@fullcalendar/interaction';
import listPlugin from '@fullcalendar/list';
import momentTimezonePlugin from '@fullcalendar/moment-timezone';
import {
    CalendarOptions,
    DateSelectArg,
    EventApi,
    EventClickArg,
    EventInput,
} from '@fullcalendar/core';
import {
    FullCalendarComponent,
    FullCalendarModule,
} from '@fullcalendar/angular';
import { ScrollPanelModule } from 'primeng/scrollpanel';
import { ButtonModule } from 'primeng/button';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { switchMap, of, map, Subject, debounceTime } from 'rxjs';
import {
    AuthStateService,
    CalendarRoutes,
    HandleApiResponseService,
    IBasicProfileInfoDto,
    ICalendarEventDto,
    ICalendarLessonDto,
    IGetStudentsLessonsCalendarRequest,
    IGetStudentsLessonsCalendarResponse,
    IGetTeacherCalendarRequest,
    IGetTeacherCalendarResponse,
    ILessonStatusEnum,
    IPackageTypeEnum,
    IStudentsLessonsScheduleDto,
    ITeacherScheduleDto,
    IUserRole,
    TimezoneService,
    ToastService,
    GeneralService,
    CalendarService,
    Severity,
    PrimeProfilePhotoSingleComponent,
    CALENDAR_STATUS_CLASSES,
    EVENT_TYPES,
    CalendarFilterOption,
} from 'SharedModules.Library';
import { LessonInfoActionsDialogComponent } from '@platform.src/app/shared/components/dialogs/lesson-info-actions-dialog/lesson-info-actions-dialog.component';
import { TeacherAvailabilitySelectDialogComponent } from '@platform.src/app/shared/components/dialogs/teacher-availability-select-dialog/teacher-availability-select-dialog.component';
import { ToolbarModule } from 'primeng/toolbar';
import { CardModule } from 'primeng/card';
import { mockTeacherLessonsSchedule } from './calendar-mock';
import { ChipModule } from 'primeng/chip';
import { MultiSelectModule } from 'primeng/multiselect';
import { FormsModule } from '@angular/forms';
import { CheckboxModule } from 'primeng/checkbox';
import { TooltipModule } from 'primeng/tooltip';
import { SidebarModule } from 'primeng/sidebar';
import { MessageModule } from 'primeng/message';
import { DrawerModule } from 'primeng/drawer';
import { BadgeModule } from 'primeng/badge';
import { TagModule } from 'primeng/tag';
import { CalendarUtilsService } from './calendar-utils.service';

// Strongly typed constants
const CALENDAR_CONSTANTS = {
    DAY_OF_WEEK_MAP: {
        mon: 1,
        tue: 2,
        wed: 3,
        thu: 4,
        fri: 5,
        sat: 6,
        sun: 0,
    } as const,
    SLOT_DURATION: '00:15:00' as const,
    LONG_PRESS_DELAY: 110 as const,
    CALENDAR_HEIGHT_OFFSET: 250 as const,
} as const;

// Strongly typed status groups
type LessonStatusGroup = readonly ILessonStatusEnum[];
const STATUS_GROUPS: Record<string, LessonStatusGroup> = {
    NO_SHOW: [ILessonStatusEnum.StudentNoShow, ILessonStatusEnum.TeacherNoShow] as const,
    CANCELLED: [
        ILessonStatusEnum.CancelledByParent,
        ILessonStatusEnum.CancelledByTeacher,
    ] as const,
} as const;

// Strongly typed status display labels
const STATUS_DISPLAY_LABELS: Record<ILessonStatusEnum, string> = {
    [ILessonStatusEnum.Scheduled]: 'Scheduled',
    [ILessonStatusEnum.AwaitingEvaluation]: 'Awaiting Evaluation',
    [ILessonStatusEnum.Completed]: 'Completed',
    [ILessonStatusEnum.StudentNoShow]: 'No Show',
    [ILessonStatusEnum.TeacherNoShow]: 'No Show',
    [ILessonStatusEnum.CancelledByParent]: 'Canceled',
    [ILessonStatusEnum.CancelledByTeacher]: 'Canceled',
    [ILessonStatusEnum.CancelledByAdmin]: 'Canceled',
    [ILessonStatusEnum.PendingConfirmationByTeacher]: 'Pending Confirmation',
} as const;

// Strongly typed package type labels
const PACKAGE_TYPE_LABELS: Record<IPackageTypeEnum, string> = {
    [IPackageTypeEnum.FreeTrial]: 'Free Trial Lesson',
    [IPackageTypeEnum.Gift]: 'Gift Lesson',
    [IPackageTypeEnum.Paid]: 'Paid Lesson',
} as const;



// Strongly typed status configuration interface
interface StatusConfig {
    readonly label: string;
    readonly value: string;
    readonly type: 'status' | 'eventType';
    readonly statusClass: string;
    readonly enumKey?: string;
    readonly enumKeys?: readonly string[];
}

// Strongly typed calendar status configuration
const CALENDAR_STATUS_CONFIG: Record<string, StatusConfig> = {
    all: {
        label: 'All',
        value: 'all',
        type: 'status',
        statusClass: 'status-all',
        enumKey: 'all',
    },
    dayOff: {
        label: 'Day Off / Unavailable',
        value: EVENT_TYPES.DAY_OFF,
        type: 'eventType',
        enumKey: 'dayOff',
        statusClass: CALENDAR_STATUS_CLASSES.daysOff,
    },
    scheduled: {
        label: 'Scheduled',
        value: 'scheduledLessons',
        type: 'status',
        enumKey: ILessonStatusEnum[ILessonStatusEnum.Scheduled],
        enumKeys: [ILessonStatusEnum[ILessonStatusEnum.Scheduled]] as const,
        statusClass: CALENDAR_STATUS_CLASSES.scheduledLessons,
    },
    pendingConfirmation: {
        label: 'Pending Confirmation',
        value: 'pendingLessons',
        type: 'status',
        enumKey: ILessonStatusEnum[ILessonStatusEnum.PendingConfirmationByTeacher],
        enumKeys: [
            ILessonStatusEnum[ILessonStatusEnum.PendingConfirmationByTeacher],
        ] as const,
        statusClass: CALENDAR_STATUS_CLASSES.pendingLessons,
    },
    awaitingEvaluation: {
        label: 'Awaiting Evaluation',
        value: 'awaitingEvaluationLessons',
        type: 'status',
        enumKey: ILessonStatusEnum[ILessonStatusEnum.AwaitingEvaluation],
        enumKeys: [
            ILessonStatusEnum[ILessonStatusEnum.AwaitingEvaluation],
            ILessonStatusEnum[ILessonStatusEnum.PendingConfirmationByTeacher],
        ] as const,
        statusClass: CALENDAR_STATUS_CLASSES.awaitingEvaluationLessons,
    },
    completed: {
        label: 'Completed',
        value: 'completedLessons',
        type: 'status',
        enumKey: ILessonStatusEnum[ILessonStatusEnum.Completed],
        enumKeys: [ILessonStatusEnum[ILessonStatusEnum.Completed]] as const,
        statusClass: CALENDAR_STATUS_CLASSES.completedLessons,
    },
    noShowAndCanceled: {
        label: 'No Show & Canceled',
        value: 'noShowAndCanceledLessons',
        type: 'status',
        enumKeys: [
            ILessonStatusEnum[ILessonStatusEnum.StudentNoShow],
            ILessonStatusEnum[ILessonStatusEnum.TeacherNoShow],
            ILessonStatusEnum[ILessonStatusEnum.CancelledByParent],
            ILessonStatusEnum[ILessonStatusEnum.CancelledByTeacher],
        ] as const,
        statusClass: CALENDAR_STATUS_CLASSES.noShowAndCanceledLessons,
    },
} as const;

// Interfaces
interface DateRange {
    start: Date;
    end: Date;
}

interface StudentCalendarFilterOption {
    userId: string;
    fullName: string;
    profilePhotoUrl: string;
}

interface EventContentInfo {
    view?: { type: string };
    event: {
        title: string;
        start?: Date;
        end?: Date;
        extendedProps?: { type: string; lesson?: ICalendarLessonDto };
    };
}

// Helper function
let eventGuid = 0;
const createEventId = (): string => String(eventGuid++);

@Component({
    selector: 'app-weekly-availability-calendar',
    imports: [
        CommonModule,
        FormsModule,
        FullCalendarModule,
        ScrollPanelModule,
        ButtonModule,
        ToolbarModule,
        CardModule,
        MultiSelectModule,
        ChipModule,
        CheckboxModule,
        TooltipModule,
        TagModule,
        DrawerModule,
        MessageModule,
        PrimeProfilePhotoSingleComponent,
    ],
    templateUrl: './weekly-availability-calendar.component.html',
    styleUrl: './weekly-availability-calendar.component.scss',
})
export class WeeklyAvailabilityCalendarComponent
    implements OnInit, AfterViewInit {
    private readonly apiService = inject(HandleApiResponseService);
    private readonly toastService = inject(ToastService);
    private readonly destroy: DestroyRef = inject(DestroyRef);
    private readonly renderer = inject(Renderer2);
    private readonly authService = inject(AuthStateService);
    private readonly generalService = inject(GeneralService);
    private readonly timezoneService = inject(TimezoneService);
    private readonly calendarService = inject(CalendarService);
    private readonly cdr = inject(ChangeDetectorRef);
    private readonly calendarUtils = inject(CalendarUtilsService);

    @ViewChild('calendar', { static: true })
    calendarComponent!: FullCalendarComponent;
    @ViewChild('dynamicCContainer', { read: ViewContainerRef, static: true })
    dynamicComponentContainer!: ViewContainerRef;

    private lastDateRange: DateRange | null = null;
    private isCalendarInitialized = false;
    private dateChangeSubject = new Subject<DateRange>();
    private originalEvents: EventInput[] = [];
    calendarOptions: CalendarOptions = {};
    currentMonth = signal('');
    isTabletOrMobile = signal(false);
    userBasicInfo = this.authService.getUserBasicInfo() as IBasicProfileInfoDto;
    selectedFilters = model<string[]>([]);
    selectedFilterValues: string[] = [];

    // Strongly typed lesson status options
    readonly lessonStatusOptionsWithAll = [
        { label: 'All Statuses', value: 'all', enumKey: 'all' },
        ...Object.keys(ILessonStatusEnum)
            .filter((key) => isNaN(Number(key)))
            .map((key) => ({
                label: STATUS_DISPLAY_LABELS[
                    ILessonStatusEnum[key as keyof typeof ILessonStatusEnum]
                ] || key,
                value: ILessonStatusEnum[key as keyof typeof ILessonStatusEnum],
                enumKey: key,
            })),
    ] as const;

    CalendarFilterOptions: CalendarFilterOption[] = [
        { label: 'All', value: 'all', type: 'status' },
        {
            label: 'Available Slots',
            value: EVENT_TYPES.AVAILABLE,
            type: 'eventType',
            enumKey: 'available',
        },
        {
            label: 'Day Off / Unavailable',
            value: EVENT_TYPES.DAY_OFF,
            type: 'eventType',
            enumKey: 'dayOff',
        },
        // Add package types
        ...Object.keys(IPackageTypeEnum)
            .filter((key) => isNaN(Number(key)))
            .map((key) => ({
                label:
                    PACKAGE_TYPE_LABELS[
                    IPackageTypeEnum[key as keyof typeof IPackageTypeEnum]
                    ] || key,
                value: String(IPackageTypeEnum[key as keyof typeof IPackageTypeEnum]),
                type: 'packageType',
                enumKey: key,
            })),
        ...Object.keys(ILessonStatusEnum)
            .filter((key) => isNaN(Number(key)))
            .map((key) => ({
                label:
                    STATUS_DISPLAY_LABELS[
                    ILessonStatusEnum[key as keyof typeof ILessonStatusEnum]
                    ] || key,
                value: String(ILessonStatusEnum[key as keyof typeof ILessonStatusEnum]), // Convert to string
                type: 'status',
                enumKey: key,
            })),
    ];
    availableFilters = signal<CalendarFilterOption[]>([]);
    studentCalendarFilterOptions: StudentCalendarFilterOption[] = []; // New property for student filter options
    selectedStudentIds: StudentCalendarFilterOption[] = []; // New property for selected student IDs

    selectedStudents: StudentCalendarFilterOption[] = []; // Renamed from selectedStudentIds
    tempStudents: StudentCalendarFilterOption[] = [];

    isLoading = signal(false);
    showFiltersDrawer = false;
    showMobileLegend = false;

    // Add this property for filter collapse state
    filtersCollapsed = true; // Initialize to true to start collapsed, or false to start open

    // ... existing constructor and other methods ...

    // Add this method to toggle the filter state
    toggleFilters(): void {
        this.filtersCollapsed = !this.filtersCollapsed;
    }

    toggleFiltersDrawer(): void {
        if (this.showFiltersDrawer) {
            this.cancelFilters();
        } else {
            // Initialize temp values
            this.tempFilterValues = [...this.selectedFilterValues];
            this.tempStudents = [...this.selectedStudents];
        }
        this.showFiltersDrawer = !this.showFiltersDrawer;
    }

    cancelFilters(): void {
        this.tempFilterValues = [...this.selectedFilterValues];
        this.tempStudents = [...this.selectedStudents];
        this.showFiltersDrawer = false;
    }

    ngOnInit(): void {
        this.timezoneService.setTimezone(this.userBasicInfo.timeZoneIana!);
        this.loadMockData();
        this.initializeCalendarOptions();
        this.selectedFilters.set(['all']);
        this.selectedFilterValues = [];
        this.debugTimezone();
        this.isCalendarInitialized = true;
        this.dateChangeSubject
            .pipe(debounceTime(1), takeUntilDestroyed(this.destroy))
            .subscribe(({ start, end }) => {
                const utcStart = moment(start).utc().format('YYYY-MM-DDTHH:mm:ss[Z]');
                const utcEnd = moment(end).utc().format('YYYY-MM-DDTHH:mm:ss[Z]');
                this.getCalendarDataByRole(utcStart, utcEnd);
                this.renderCustomHeader();
                this.isLoading.set(true);
            });
    }

    ngAfterViewInit(): void {
        this.handleCalendarInitialized();
        this.addCustomClassesToButtons();
        this.setupEmptyStateObserver();
    }

    // TODO: works but check if needed
    private addCustomClassesToButtons(): void {
        // // Example: Add a class to the 'today' button
        // const todayButton = document.querySelector('.fc-today-button');
        // if (todayButton) {
        //     this.renderer.addClass(todayButton, 'my-custom-today-button');
        // }

        // // Example: Add a class to all 'prev' and 'next' buttons
        // const prevNextButtons = document.querySelectorAll('.fc-prev-button, .fc-next-button');
        // prevNextButtons.forEach(button => {
        //     this.renderer.addClass(button, 'my-custom-nav-button');
        // });

        // // Example: Add a class to the active view button (e.g., timeGridWeek when active)
        // const activeViewButton = document.querySelector('.fc-button.fc-button-primary.fc-button-active');
        // if (activeViewButton) {
        //     this.renderer.addClass(activeViewButton, 'btn-soft-primary');
        // }
    }

    // Setup observer for empty state
    private setupEmptyStateObserver(): void {
        // Use MutationObserver to watch for empty state changes
        const observer = new MutationObserver(() => {
            this.handleEmptyState();
        });

        // Start observing the calendar container
        const calendarElement = document.querySelector('.fc');
        if (calendarElement) {
            observer.observe(calendarElement, {
                childList: true,
                subtree: true,
                attributes: true,
                attributeFilter: ['class']
            });
        }

        // Initial check
        setTimeout(() => this.handleEmptyState(), 100);
    }

    // Handle empty state display
    private handleEmptyState(): void {
        const emptyElement = document.querySelector('.fc-list-empty');
        const existingCustomState = document.querySelector('.calendar-empty-state');

        if (emptyElement && !existingCustomState) {
            // Create custom empty state content
            const customEmptyState = this.renderer.createElement('div');
            this.renderer.addClass(customEmptyState, 'calendar-empty-state');

            // Create the content structure
            customEmptyState.innerHTML = `
                <div class="empty-icon">
                    <i class="pi pi-calendar-times"></i>
                </div>
                <h3 class="empty-title">No Lessons This Week</h3>
                <p class="empty-subtitle">Your calendar is clear for the selected time period</p>

                <div class="empty-suggestions">
                    <div class="suggestion-item">
                        <div class="suggestion-icon">
                            <i class="pi pi-filter"></i>
                        </div>
                        <span class="suggestion-text">Try adjusting your filters</span>
                    </div>
                    <div class="suggestion-item">
                        <div class="suggestion-icon">
                            <i class="pi pi-calendar"></i>
                        </div>
                        <span class="suggestion-text">Navigate to a different week</span>
                    </div>
                    <div class="suggestion-item">
                        <div class="suggestion-icon">
                            <i class="pi pi-plus"></i>
                        </div>
                        <span class="suggestion-text">Schedule new events or lessons</span>
                    </div>
                </div>

                <div class="empty-actions">
                    <button class="action-button" onclick="this.clearFilters()">
                        <i class="pi pi-filter-slash"></i>
                        <span>Clear All Filters</span>
                    </button>
                    <button class="action-button secondary-action" onclick="this.goToToday()">
                        <i class="pi pi-calendar"></i>
                        <span>Go to Today</span>
                    </button>
                </div>
            `;

            // Append to the empty element
            this.renderer.appendChild(emptyElement, customEmptyState);

            // Add event listeners for the buttons
            this.addEmptyStateEventListeners(customEmptyState);
        }
    }

    // Add event listeners for empty state buttons
    private addEmptyStateEventListeners(container: HTMLElement): void {
        const clearFiltersBtn = container.querySelector('.action-button:not(.secondary-action)');
        const goToTodayBtn = container.querySelector('.secondary-action');

        if (clearFiltersBtn) {
            this.renderer.listen(clearFiltersBtn, 'click', () => {
                this.clearAllFilters();
            });
        }

        if (goToTodayBtn) {
            this.renderer.listen(goToTodayBtn, 'click', () => {
                this.goToToday();
            });
        }
    }

    // Navigate to today
    private goToToday(): void {
        const calendarApi = this.calendarComponent.getApi();
        calendarApi.today();
    }

    // Calendar Setup
    private initializeCalendarOptions(): void {
        const userTimezone = this.timezoneService.getTimezone();
        this.calendarOptions = {
            plugins: [
                dayGridPlugin,
                listPlugin,
                timeGridPlugin,
                interactionPlugin,
                momentTimezonePlugin,
            ],
            timeZone: userTimezone,
            nowIndicator: true,
            editable: true,
            eventDurationEditable: true,
            eventStartEditable: true,
            dragScroll: true,
            dayHeaderFormat: { weekday: 'short', day: '2-digit' },
            headerToolbar: {
                left: 'prev,next today',
                center: 'title',
                right: this.isTabletOrMobile()
                    ? 'listWeek'
                    : 'dayGridMonth,timeGridWeek,listWeek',
            },
            views: {
                timeGridWeek: { buttonText: 'Week' },
                listWeek: { buttonText: 'List Week' },
                dayGridMonth: {
                    buttonText: 'Month',
                    showNonCurrentDates: false,
                    fixedWeekCount: false,
                    firstDay: 1, // Start weeks on Monday
                    titleFormat: { month: 'long', year: 'numeric' },
                    dayHeaderFormat: { weekday: 'short' },
                    duration: { months: 1 },
                    monthStartsOn: 1,
                },
            },
            eventSources: [
                {
                    events: [], // Will be populated later
                    // backgroundColor: 'rgba(0, 128, 0, 0.2)' // Default for all background events
                },
            ],
            // eventOverlap: true,
            // slotEventOverlap: false, // Prevents visual overlapping in time grid
            weekends: true,
            allDaySlot: false,
            selectable: true,
            selectOverlap: false,
            selectMirror: false,
            slotDuration: CALENDAR_CONSTANTS.SLOT_DURATION,
            slotLabelInterval: 30,
            slotLabelFormat: {
                hour: '2-digit',
                minute: '2-digit',
                hour12: false,
                meridiem: false,
            },
            dayMaxEvents: 3, // Change this from true to 3
            dayMaxEventRows: 3,
            moreLinkClick: 'popover',
            eventShortHeight: 50,
            slotMinTime: '00:00:00',
            scrollTime: '09:00:00',
            firstDay: 1,
            height: window.innerHeight - CALENDAR_CONSTANTS.CALENDAR_HEIGHT_OFFSET,
            contentHeight: '400',
            longPressDelay: CALENDAR_CONSTANTS.LONG_PRESS_DELAY,
            eventLongPressDelay: CALENDAR_CONSTANTS.LONG_PRESS_DELAY,
            businessHours: false,
            select: this.handleDateSelect.bind(this),
            eventClick: this.handleEventClick.bind(this),
            customButtons: this.getCustomButtons(),
            eventDidMount: this.handleEventMount.bind(this),
            selectAllow: this.handleSelectAllow.bind(this),
            eventContent: this.getEventContent.bind(this),
            datesSet: this.handleDatesRender.bind(this),
        };
        console.log(
            `Today in ${userTimezone}: ${moment
                .tz(userTimezone)
                .format('YYYY-MM-DD HH:mm:ss Z')}`
        );
    }

    // ... existing code ...

    private renderCustomHeader(): void {
        const calendarApi = this.calendarComponent.getApi();
        const headerToolbar = document.querySelector('.fc-header-toolbar');

        // Remove existing title if it exists
        const existingTitle = headerToolbar?.querySelector(
            '.custom-calendar-title'
        );
        if (existingTitle) {
            existingTitle.remove();
        }

        // Create new title container
        const titleContainer = this.renderer.createElement('div');
        this.renderer.addClass(titleContainer, 'custom-calendar-title');

        // Create main title element
        const mainTitle = this.renderer.createElement('div');
        this.renderer.addClass(mainTitle, 'main-title');
        this.renderer.setProperty(mainTitle, 'innerText', calendarApi.view.title);

        // Create timezone element
        const timezone = this.renderer.createElement('div');
        this.renderer.addClass(timezone, 'timezone');

        // this.renderer.addClass(timezone, 'timezone');
        // this.renderer.addClass(timezone, 'timezone-badge');
        // this.renderer.addClass(timezone, 'flex');
        // this.renderer.addClass(timezone, 'align-items-center');
        // this.renderer.addClass(timezone, 'gap-2');
        // this.renderer.addClass(timezone, 'py-1');
        // this.renderer.addClass(timezone, 'px-3');
        // this.renderer.addClass(timezone, 'border-round');
        // this.renderer.addClass(timezone, 'bg-primary-50');
        // this.renderer.addClass(timezone, 'text-xs');
        // this.renderer.setProperty(
        //     timezone,
        //     'innerText',
        //     `${this.getTimezoneDisplay()}`
        // );

        // Append elements to container
        this.renderer.appendChild(titleContainer, mainTitle);
        this.renderer.appendChild(titleContainer, timezone);

        // Insert new title in the center section
        const centerSection = headerToolbar?.querySelector(
            '.fc-toolbar-chunk:nth-child(2)'
        );
        if (centerSection) {
            this.renderer.appendChild(centerSection, titleContainer);
        }
    }

    // ... existing code ...

    private handleCalendarInitialized(): void {
        const calendarApi = this.calendarComponent.getApi();
        this.generalService.deviceKind
            .pipe(
                takeUntilDestroyed(this.destroy),
                switchMap((deviceKind: any) => {
                    this.isTabletOrMobile.set(deviceKind.w992down === true);

                    // Update the header toolbar when device type changes
                    if (this.isTabletOrMobile()) {
                        calendarApi.setOption('headerToolbar', {
                            left: 'prev,next today',
                            center: 'title',
                            right: 'listWeek',
                        });
                    } else {
                        calendarApi.setOption('headerToolbar', {
                            left: 'prev,next today',
                            center: 'title',
                            right: 'dayGridMonth,timeGridWeek,listWeek',
                        });
                    }

                    return of(this.isTabletOrMobile());
                })
            )
            .subscribe((isTabletOrMobile) => {
                if (isTabletOrMobile) {
                    calendarApi.changeView('listWeek');
                    calendarApi.setOption(
                        'height',
                        window.innerHeight - CALENDAR_CONSTANTS.CALENDAR_HEIGHT_OFFSET
                    );
                    this.removeClonedTables();
                } else {
                    calendarApi.changeView('timeGridWeek');
                }
            });
    }

    // Event Handling
    handleEventClick(clickInfo: EventClickArg): void {
        clickInfo.jsEvent.preventDefault();
        const event = clickInfo.event;
        switch (event.extendedProps['type']) {
            case EVENT_TYPES.DAY_OFF:
                console.log('Day off event clicked');
                break;
            case EVENT_TYPES.LESSON:
                const lessonCalendarEvent: ICalendarEventDto = {
                    title: event.title,
                    start: event.start!.toISOString(),
                    end: event.end!.toISOString(),
                    startDateTime: moment
                        .tz(event.start, this.timezoneService.getTimezone())
                        .toDate(),
                    endDateTime: moment
                        .tz(event.end, this.timezoneService.getTimezone())
                        .toDate(),
                    lesson: event.extendedProps?.['lesson'] as ICalendarLessonDto,
                };
                console.log('Lesson event clicked:', lessonCalendarEvent);
                this.generalService.openComponent(
                    LessonInfoActionsDialogComponent,
                    lessonCalendarEvent
                );
                break;
            default:
                console.log('Unknown event type clicked');
                break;
        }
        this.cdr.markForCheck();
    }

    private handleEventMount(info: { el: HTMLElement; event: EventApi }): void {
        const { el: eventElement, event } = info;
        const eventType = event.extendedProps?.['type'];
        const lesson = event.extendedProps?.['lesson'] as
            | ICalendarLessonDto
            | undefined;
        const statusClass =
            eventType === EVENT_TYPES.DAY_OFF
                ? 'status-day-off'
                : eventType === EVENT_TYPES.AVAILABLE
                    ? 'status-available'
                    : lesson
                        ? this.getStatusClass(ILessonStatusEnum[lesson.lessonStatus])
                        : 'status-default';
        this.renderer.addClass(eventElement, statusClass);
    }

    private handleDatesRender(info: {
        view: { type: string; activeStart: Date; activeEnd: Date };
    }): void {
        if (!this.isCalendarInitialized) return;

        const view = info.view;
        let start: Date;
        let end: Date;

        if (view.type === 'dayGridMonth') {
            // For month view, only get current month dates
            start = view.activeStart;
            end = view.activeEnd;
        } else {
            start = view.activeStart;
            end = view.activeEnd;
            setTimeout(() => this.scrollToFirstLesson(), 100);
        }

        // Check if the date range has changed
        if (
            this.lastDateRange &&
            moment(this.lastDateRange.start).isSame(start, 'second') &&
            moment(this.lastDateRange.end).isSame(end, 'second')
        ) {
            return;
        }

        // Update month display in header
        const startMonth = moment(start).format('MMMM');
        const endMonth = moment(end).format('MMMM');
        const endMonthYear = moment(end).format('YYYY');
        this.currentMonth.set(
            startMonth !== endMonth
                ? `${startMonth} - ${endMonth} ${endMonthYear}`
                : moment(start).format('MMMM')
        );

        // Log the date range for debugging
        console.log(
            `Date range: ${moment(start).format('YYYY-MM-DD')} to ${moment(
                end
            ).format('YYYY-MM-DD')} (${moment(end).diff(moment(start), 'days')} days)`
        );

        // Clean up filters that are no longer relevant
        // TODO: check if needed
        // this.cleanupOutOfRangeFilters(start, end);

        this.lastDateRange = { start, end };
        this.dateChangeSubject.next({ start, end });
        // Add tooltips to non-business hours after calendar renders
        setTimeout(() => this.addNonBusinessHoursTooltips(), 100);
    }

    /**
     * Adds tooltips to non-business hour cells in the calendar
     */
    private addNonBusinessHoursTooltips(): void {
        if (this.calendarComponent?.getApi()?.view.type !== 'timeGridWeek') {
            return; // Only apply tooltips in week view
        }

        const nonBusinessCells = document.querySelectorAll(
            '.fc-timegrid-col-bg .fc-non-business'
        );
        nonBusinessCells.forEach((cell) => {
            // Remove standard title to avoid double tooltips
            this.renderer.removeAttribute(cell as HTMLElement, 'title');

            // Add PrimeNG tooltip attributes
            this.renderer.setAttribute(
                cell as HTMLElement,
                'pTooltip',
                'No availability'
            );
            this.renderer.setAttribute(cell as HTMLElement, 'tooltipPosition', 'top');
            this.renderer.setAttribute(
                cell as HTMLElement,
                'tooltipStyleClass',
                'no-availability-tooltip'
            );
            this.renderer.setAttribute(cell as HTMLElement, 'tooltipZIndex', '999');

            // Add class for styling
            this.renderer.addClass(cell as HTMLElement, 'no-availability-cell');
        });
    }

    private handleSelectAllow(info: { start: Date; end: Date }): boolean {
        return moment(info.end).diff(moment(info.start), 'days') === 0;
    }

    private handleDateSelect(_selectInfo: DateSelectArg): boolean {
        return false;
    }

    // Filter Management
    getActiveFiltersDisplay(): CalendarFilterOption[] {
        const selectedValues = this.selectedFilters();
        if (selectedValues.includes('all')) return [];

        return this.availableFilters().filter((option) =>
            selectedValues.includes(option.value)
        );
    }

    removeFilter(value: string): void {
        // Find the filter to determine its type
        const filterToRemove = this.getActiveFilterLabels().find(
            (f) => f.value === value
        );

        if (filterToRemove) {
            if (filterToRemove.type === 'status') {
                // Remove from both temporary and selected filter values
                this.selectedFilterValues = this.selectedFilterValues.filter(
                    (v) => v !== value
                );
                this.tempFilterValues = this.tempFilterValues.filter(
                    (v) => v !== value
                );

                // If no filters remain, show all events
                if (this.selectedFilterValues.length === 0) {
                    this.selectedFilters.set(['all']);
                }
            } else if (filterToRemove.type === 'student') {
                // Remove from both temporary and selected student IDs
                this.selectedStudents = this.selectedStudents.filter(
                    (student) => student.userId !== value
                );
                this.tempStudents = this.tempStudents.filter(
                    (student) => student.userId !== value
                );
            }

            // Apply the filters to update the calendar
            this.applyFilters();
        }
    }

    onStatusFilterChange(event: { value: string[] }): void {
        const selectedValues = event.value;
        if (
            selectedValues.includes('all') &&
            !this.selectedFilters().includes('all')
        ) {
            this.selectedFilters.set(['all']);
        } else {
            const filteredValues = selectedValues.filter((value) => value !== 'all');
            this.selectedFilters.set(
                filteredValues.length > 0 ? filteredValues : ['all']
            );
        }
        this.applyFilters();
    }

    onFilterChange(event: { value: string[] }): void {
        const selectedValues = event.value;
        if (
            selectedValues.includes('all') &&
            !this.selectedFilters().includes('all')
        ) {
            this.selectedFilters.set(['all']);
        } else {
            const filteredValues = selectedValues.filter((value) => value !== 'all');
            this.selectedFilters.set(
                filteredValues.length > 0 ? filteredValues : ['all']
            );
        }
        this.applyFilters();
    }

    onStudentFilterChange(): void {
        // New method for student filter change
        this.applyFilters();
    }

    tempFilterValues: string[] = [];

    hasFilterChanges(): boolean {
        return (
            !this.arraysEqual(this.tempFilterValues, this.selectedFilterValues) ||
            !this.arraysEqual(this.tempStudents, this.selectedStudents)
        );
    }

    private arraysEqual(a: any[], b: any[]): boolean {
        return a.length === b.length && a.every((val, index) => val === b[index]);
    }

    getFilterSeverity(type: string): any {
        switch (type) {
            case 'status':
                return Severity.Info;
            case 'student':
                return Severity.Success;
            default:
                return Severity.Contrast;
        }
    }

    getFilterIcon(type: string): string {
        switch (type) {
            case 'status':
                return 'pi pi-tag';
            case 'student':
                return 'pi pi-user';
            default:
                return 'pi pi-filter';
        }
    }

    getActiveFilterLabels(): Array<{
        label: string;
        value: string;
        type: 'status' | 'student';
        enumKey?: string;
        image?: string;
    }> {
        const labels: any[] = [];

        // Add status filters
        this.selectedFilterValues.forEach((value) => {
            const filter = this.availableFilters().find((f) => f.value === value);
            if (filter) {
                labels.push({
                    label: filter.label,
                    value: filter.value,
                    type: 'status',
                    enumKey: filter.enumKey,
                });
            }
        });

        // Add student filters - Fixed to use StudentCalendarFilterOption objects directly
        this.selectedStudents.forEach((student) => {
            labels.push({
                label: student.fullName,
                value: student.userId,
                type: 'student',
                image: student.profilePhotoUrl,
            });
        });

        return labels;
    }

    getActiveFiltersCount(): number {
        return this.selectedFilterValues.length + this.selectedStudents.length;
    }

    confirmApplyFilters(): void {
        this.selectedFilterValues = [...this.tempFilterValues];
        this.selectedStudents = [...this.tempStudents];
        this.applyFilters();
        this.showFiltersDrawer = false;
    }

    applyFilters(): void {
        const calendarApi = this.calendarComponent.getApi();
        calendarApi.removeAllEventSources();

        // If no status/event type filters and no student filters selected, show all events
        if (
            this.selectedFilterValues.length === 0 &&
            this.selectedStudentIds.length === 0
        ) {
            calendarApi.addEventSource(this.originalEvents);
            return;
        }

        const filteredEvents = this.originalEvents.filter((event) => {
            const eventType = event.extendedProps?.['type'];
            const lesson = event.extendedProps?.['lesson'] as
                | ICalendarLessonDto
                | undefined;

            // Apply status/event type filters
            let passesEventTypeAndStatusFilter = false;
            if (this.selectedFilterValues.length === 0) {
                // If no status/event type filters are active, pass all
                passesEventTypeAndStatusFilter = true;
            } else {
                // Check if event type is selected
                if (
                    eventType &&
                    this.selectedFilterValues.includes(eventType.toString())
                ) {
                    passesEventTypeAndStatusFilter = true;
                }

                // Check if lesson status or its group is selected
                if (lesson && lesson.lessonStatus !== undefined) {
                    // Direct status match
                    if (
                        this.selectedFilterValues.includes(lesson.lessonStatus.toString())
                    ) {
                        passesEventTypeAndStatusFilter = true;
                    }

                    // Check if the lesson status is included in any selected filter's enumKeys
                    const lessonStatusKey = ILessonStatusEnum[lesson.lessonStatus];

                    // Check each selected filter
                    for (const filterValue of this.selectedFilterValues) {
                        // Find the filter configuration for this value
                        const filterConfig = Object.values(CALENDAR_STATUS_CONFIG).find(
                            (config) => config.value === filterValue
                        );

                        // If this filter has enumKeys and includes the current lesson's status
                        if (
                            filterConfig &&
                            'enumKeys' in filterConfig &&
                            filterConfig.enumKeys &&
                            filterConfig.enumKeys.includes(lessonStatusKey)
                        ) {
                            passesEventTypeAndStatusFilter = true;
                            break;
                        }
                    }

                    // Group status match - No-Show
                    if (
                        this.selectedFilterValues.includes('no-show-group') &&
                        STATUS_GROUPS['NO_SHOW'].includes(lesson.lessonStatus)
                    ) {
                        passesEventTypeAndStatusFilter = true;
                    }

                    // Group status match - Cancelled
                    if (
                        this.selectedFilterValues.includes('cancelled-group') &&
                        STATUS_GROUPS['CANCELLED'].includes(lesson.lessonStatus)
                    ) {
                        passesEventTypeAndStatusFilter = true;
                    }
                }
            }

            // Apply student filters
            let passesStudentFilter = false;
            if (this.selectedStudents && this.selectedStudents.length === 0) {
                // If no student filters are active, pass all
                passesStudentFilter = true;
            } else {
                if (lesson && lesson.students && lesson.students.length > 0) {
                    passesStudentFilter = lesson.students.some((student) =>
                        this.selectedStudents.some(
                            (selectedStudent) => selectedStudent.userId === student.userId
                        )
                    );
                }
            }

            // An event must pass *both* event type/status filters AND student filters to be displayed
            return passesEventTypeAndStatusFilter && passesStudentFilter;
        });

        calendarApi.addEventSource(filteredEvents);
    }

    clearAllFilters(): void {
        this.selectedFilterValues = [];
        this.selectedStudentIds = []; // Clear selected students
        this.showFiltersDrawer = false;
        this.applyFilters();
    }

    hasActiveFilters(): boolean {
        return (
            this.selectedFilterValues.length > 0 ||
            (this.selectedStudents ? this.selectedStudents.length > 0 : false)
        );
    }

    isStatusSelected(status: string): boolean {
        return this.selectedFilters().includes(status);
    }

    // Event Content
    private getEventContent(info: EventContentInfo): { html: string } {
        try {
            if (!info?.event) {
                console.warn('Invalid event info provided');
                return this.createDefaultEventContent('Invalid Event');
            }

            const isMonthView = info.view?.type === 'dayGridMonth';
            const { type, lesson } = info.event.extendedProps || {};

            // Skip content for background events (available slots)
            if (type === EVENT_TYPES.AVAILABLE) {
                return this.createAvailableEventContent(isMonthView, info.event);
            }

            if (type === EVENT_TYPES.DAY_OFF) {
                return this.createDayOffEventContent(isMonthView, info.event);
            }

            if (lesson) {
                return this.createLessonEventContent(lesson, isMonthView, info.event);
            }

            return this.createDefaultEventContent(info.event.title, info.event);
        } catch (error) {
            console.error('Error generating event content:', error);
            return this.createDefaultEventContent('Error displaying event');
        }
    }

    private createAvailableEventContent(
        isMonthView: boolean,
        event: EventInput
    ): { html: string } {
        const localStart = this.timezoneService.convertUtcToLocal(
            moment(event.start).toDate(),
            {
                timezone: this.timezoneService.getTimezone(),
            }
        );
        const localEnd = this.timezoneService.convertUtcToLocal(
            moment(event.end).toDate(),
            {
                timezone: this.timezoneService.getTimezone(),
            }
        );
        if (isMonthView) {
            return {
                html: `
                <div class="event-container available">
                    <div class="event-card">
                        <div class="event-header">
                            <span class="event-title">Available</span>
                        </div>
                    </div>
                </div>`,
            };
        }

        return {
            html: `
            <div class="event-container available">
                <div class="event-header">
                    <i class="pi pi-calendar-plus"></i>
                    <span class="event-title">Available</span>
                </div>
                <div class="event-time">
                    ${moment(localStart).format('HH:mm')} - ${moment(
                localEnd
            ).format('HH:mm')}
                </div>
            </div>`,
        };
    }

    private createDayOffEventContent(
        isMonthView: boolean,
        event: EventInput
    ): { html: string } {
        if (isMonthView) {
            return {
                html: `
                <div class="event-container day-off">
                    <div class="event-card">
                        <div class="event-header">
                            <span class="event-title">Day Off</span>
                        </div>
                    </div>
                </div>`,
            };
        }

        const isAllDay = moment(event.end).diff(moment(event.start), 'hours') >= 24;

        return {
            html: `
            <div class="event-container day-off">
                <div class="event-header">
                    <i class="pi pi-calendar-times"></i>
                    <span class="event-title">Day Off</span>
                </div>
                <div class="event-time">
                    ${isAllDay
                    ? 'All Day'
                    : `${moment(event.start).format('HH:mm')} - ${moment(
                        event.end
                    ).format('HH:mm')}`
                }
                </div>
            </div>`,
        };
    }

    private createLessonEventContent(
        lesson: ICalendarLessonDto,
        isMonthView: boolean,
        event: any
    ): { html: string } {
        if (isMonthView) {
            return {
                html: `
          <div class="event-container">
            <div class="event-card">
              <div class="event-header">
                <span class="event-title">
                  ${this.sanitize(lesson.teachingLanguageName || 'Lesson')}
                  ${this.generalService.getILanguageLevelsEnumText(lesson!.languageLevel, false)}
                  ${lesson.groupId
                        ? '<i class="pi pi-users" style="margin-left: 4px; font-size: 0.9rem;"></i>'
                        : '<i class="pi pi-user" style="margin-left: 4px; font-size: 0.9rem;"></i>'
                    }

                </span>
              </div>
            </div>
          </div>`,
            };
        }

        //   <span class="package-badge ${this.getPackageBadgeClass(lesson.lessonType)}">
        //     ${this.getPackageTypeLabel(lesson.lessonType)}
        //   </span>

        const statusClass = this.getStatusClass(ILessonStatusEnum[lesson.lessonStatus]);
        const timeRange = this.formatTimeRange(event.start, event.end, true);
        const isGroup = !!lesson.groupId;
        const packageBadge = this.getPackageTypeLabel(lesson.lessonType);
        const packageBadgeClass = this.getPackageBadgeClass(lesson.lessonType);


        return {
            html: `
        <div class="event-container ${this.sanitize(statusClass)}">
          <div class="event-card">
            <div class="event-header">
              <div class="event-title-wrapper">
                <span class="event-title">
                  ${this.sanitize(lesson.teachingLanguageName || 'Lesson')}
                  <span class="level-badge"> ${this.generalService.getILanguageLevelsEnumText(lesson!.languageLevel, false)}</span>
                </span>
              </div>
    <div class="event-badges">
                <span class="package-badge ${packageBadgeClass}">${packageBadge}</span>
              </div>
            </div>
            <div class="event-body flex flex-row w-full justify-content-between">
              <div class="event-time">
                <i class="pi pi-clock"></i>
                ${this.sanitize(timeRange)}
              </div>
              <span class="lesson-type-indicator">
                ${isGroup
                    ? '<i class="pi pi-users" title="Group Lesson"></i>'
                    : '<i class="pi pi-user" title="One-on-One Lesson"></i>'
                }
              </span>
            </div>
            <div class="event-footer">
              <span class="event-status-indicator"></span>
              <span class="event-status">${this.sanitize(
                    this.formatLessonStatus(String(lesson.lessonStatus))
                )}</span>
            </div>
          </div>
        </div>`,
        };
    }

    private createDefaultEventContent(
        title: string = 'Event',
        event?: any
    ): { html: string } {
        const timeDisplay = event
            ? `
        <div class="event-body">
          <div class="event-time">
            <i class="pi pi-clock"></i>
            ${this.sanitize(this.formatTimeRange(event.start, event.end))}
          </div>
        </div>`
            : '';
        return {
            html: `
        <div class="event-container default">
          <div class="event-card">
            <div class="event-header">
              <span class="event-title">${this.sanitize(title)}</span>
            </div>
            ${timeDisplay}
          </div>
        </div>`,
        };
    }

    // Formatting Utilities using service
    private sanitize(value: string | undefined | null): string {
        return this.calendarUtils.sanitizeHtml(value);
    }

    // Strongly typed helper methods using utility service
    private getPackageTypeLabel(packageType: IPackageTypeEnum): string {
        return this.calendarUtils.getShortPackageLabel(packageType);
    }

    private getPackageBadgeClass(packageType: IPackageTypeEnum): string {
        return this.calendarUtils.getPackageBadgeClass(packageType);
    }

    private formatLessonStatus(status: string): string {
        return this.calendarUtils.formatLessonStatus(status);
    }

    private formatTimeRange(
        start: Date,
        end: Date,
        showEndTime: boolean = true
    ): string {
        const timezone = this.timezoneService.getTimezone();
        const startTime = this.timezoneService.formatInTimezone(
            start,
            'HH:mm',
            timezone
        );
        const endTime = this.timezoneService.formatInTimezone(
            end,
            'HH:mm',
            timezone
        );
        return showEndTime ? `${startTime} - ${endTime}` : startTime;
    }

    getTimezoneDisplay(): string {
        return `${this.userBasicInfo.timeZoneDisplayName}`;
    }



    // Navigation
    gotoNextMonth(): void {
        const calendarApi = this.calendarComponent.getApi();
        const nextMonthFirstDate = moment(calendarApi.getDate())
            .add(1, 'month')
            .startOf('month');
        calendarApi.gotoDate(nextMonthFirstDate.toDate());
        this.logCurrentDate();
    }

    gotoPreviousMonth(): void {
        const calendarApi = this.calendarComponent.getApi();
        const previousMonthFirstDate = moment(calendarApi.getDate())
            .subtract(1, 'month')
            .startOf('month');
        calendarApi.gotoDate(previousMonthFirstDate.toDate());
        this.logCurrentDate();
    }

    goToTeacherAvailability(): void {
        this.generalService.openComponent(
            TeacherAvailabilitySelectDialogComponent,
            {}
        );
    }

    // Data Fetching
    private getCalendarDataByRole(startDate: string, endDate: string): void {
        const userRole = this.authService.getUserRole();
        const userId = this.authService.getUserClaims()?.id;
        if (!userId) return;

        switch (userRole) {
            case IUserRole.TEACHER:
                this.getTeacherCalendarData(startDate, endDate);
                break;
            case IUserRole.STUDENT:
            case IUserRole.PARENT:
                this.getStudentCalendarData(startDate, endDate);
                break;
            default:
                console.error('Unknown user role:', userRole);
        }
    }

    private getTeacherCalendarData(startDate: string, endDate: string): void {
        const teacherId = this.userBasicInfo.userId!;
        const request: IGetTeacherCalendarRequest = {
            teacherId,
            startDateUtc: moment(startDate).toISOString() as unknown as Date,
            endDateUtc: moment(endDate).toISOString() as unknown as Date,
        };

        this.apiService
            .getApiData<IGetTeacherCalendarResponse>(
                { url: CalendarRoutes.getTeacherCalendar, method: 'GET' },
                request
            )
            .pipe(
                takeUntilDestroyed(this.destroy),
                map((response) => this.processTeacherCalendarResponse(response))
            )
            .subscribe({
                next: (events) => {
                    this.originalEvents = events;
                    this.updateCalendarEvents(events);
                    // this.updateBusinessHours();
                    this.applyFilters();
                    this.isLoading.set(false);
                },
                error: (error) => {
                    console.error('Error loading teacher calendar data:', error);
                    this.toastService.show({
                        severity: 'error',
                        summary: 'Error',
                        detail: 'Failed to load calendar data',
                    });
                    // Set loading to false on error
                    this.cdr.detectChanges();
                    this.isLoading.set(false);
                },
            });
    }

    private getStudentCalendarData(startDate: string, endDate: string): void {
        const userRole = this.authService.getUserRole();
        const userId = this.authService.getUserClaims()?.id;
        const request: IGetStudentsLessonsCalendarRequest = {
            startDateUtc: moment(startDate).toISOString() as unknown as Date,
            endDateUtc: moment(endDate).toISOString() as unknown as Date,
        };

        // Add the appropriate ID based on user role
        if (userRole === IUserRole.STUDENT) {
            request.studentId = userId;
        } else if (userRole === IUserRole.PARENT) {
            request.parentId = userId;
        }

        this.apiService
            .getApiData<IGetStudentsLessonsCalendarResponse>(
                { url: CalendarRoutes.getStudentsCalendar, method: 'GET' },
                request
            )
            .pipe(
                takeUntilDestroyed(this.destroy),
                map((response) => this.processStudentCalendarResponse(response))
            )
            .subscribe({
                next: (events) => {
                    this.originalEvents = events;
                    this.updateCalendarEvents(events);
                    this.applyFilters();
                    this.isLoading.set(false);
                },
                error: () => {
                    this.isLoading.set(false);
                },
            });
    }

    private updateCalendarEvents(events: EventInput[]): void {
        const calendarApi = this.calendarComponent?.getApi();
        if (calendarApi) {
            calendarApi.removeAllEventSources();
            calendarApi.addEventSource(events);
            // After processing events, generate filters
            this.generateFiltersFromEvents(this.originalEvents);
            this.populateStudentCalendarFilterOptions(this.originalEvents); // Populate student filters
            setTimeout(() => this.scrollToFirstLesson(), 100);
        }
    }

    private populateStudentCalendarFilterOptions(events: EventInput[]): void {
        const studentMap = new Map<string, StudentCalendarFilterOption>();

        events.forEach((event) => {
            const lesson = event.extendedProps?.['lesson'] as
                | ICalendarLessonDto
                | undefined;

            if (lesson && lesson.students && lesson.students.length > 0) {
                lesson.students.forEach((student) => {
                    if (!studentMap.has(student.userId)) {
                        studentMap.set(student.userId, {
                            userId: student.userId,
                            fullName: `${student.firstName} ${student.lastName}`,
                            profilePhotoUrl: student.profilePhotoUrl!,
                        });
                    }
                });
            }
        });

        this.studentCalendarFilterOptions = Array.from(studentMap.values());
    }

    private processTeacherCalendarResponse(
        response: IGetTeacherCalendarResponse
    ): EventInput[] {
        return response?.teacherScheduleDto
            ? this.processScheduleDto(response.teacherScheduleDto)
            : [];
    }

    private processStudentCalendarResponse(
        response: IGetStudentsLessonsCalendarResponse
    ): EventInput[] {
        return response?.studentsLessonsScheduleDto
            ? this.processScheduleDto(response.studentsLessonsScheduleDto)
            : [];
    }

    private processScheduleDto(
        schedule:
            | ITeacherScheduleDto
            | IStudentsLessonsScheduleDto = mockTeacherLessonsSchedule
    ): EventInput[] {
        const events: EventInput[] = [];

        // if ('availableSlots' in schedule) {
        //     schedule.availableSlots.forEach((slot) => events.push(this.createAvailabilityEvent(slot)));
        // }

        if ('availableSlots' in schedule) {
            const businessHours = this.convertAvailableSlotsToBusinessHours(
                schedule.availableSlots
            );
            const calendarApi = this.calendarComponent.getApi();
            calendarApi.setOption('businessHours', businessHours);
        }

        if ('daysOff' in schedule) {
            schedule.daysOff.forEach((dayOff) =>
                events.push(this.createDayOffEvent(dayOff))
            );
        }

        const lessonTypes = [
            schedule.scheduledLessons,
            schedule.pendingLessons,
            schedule.ongoingLessons,
            schedule.completedLessons,
            schedule.awaitingEvaluationLessons,
            schedule.noShowAndCanceledLessons,
        ];
        this.calendarService.generateCalendarColors(schedule);

        console.log(
            ' this.calendarService.generateCalendarColors(schedule: ',
            this.calendarService.generateCalendarColors(schedule)
        );
        lessonTypes.forEach((lessons) =>
            this.processLessonsByStatus(events, lessons)
        );

        return events;
    }

    private convertAvailableSlotsToBusinessHours(availableSlots: any[]): any[] {
        const slotsByDay = new Map<number, Array<{ startTime: string; endTime: string }>>();
        const userTimezone = this.timezoneService.getTimezone();

        availableSlots.forEach((slot) => {
            const start = moment.tz(slot.startDateTime, userTimezone);
            const end = moment.tz(slot.endDateTime, userTimezone);

            const dayOfWeek = start.day(); // 0 = Sunday
            const startTime = start.format('HH:mm');
            const endTime = end.format('HH:mm');

            if (!slotsByDay.has(dayOfWeek)) {
                slotsByDay.set(dayOfWeek, []);
            }

            slotsByDay.get(dayOfWeek)!.push({ startTime, endTime });
        });

        // Flatten into fullcalendar businessHours format
        const businessHours: any[] = [];

        slotsByDay.forEach((timeRanges, dayOfWeek) => {
            timeRanges.forEach(({ startTime, endTime }) => {
                businessHours.push({
                    daysOfWeek: [dayOfWeek],
                    startTime,
                    endTime,
                });
            });
        });

        return businessHours;
    }


    private processLessonsByStatus(
        events: EventInput[],
        lessons: ICalendarEventDto[]
    ): void {
        lessons?.forEach((event) =>
            events.push({
                id: createEventId(),
                title: this.getLessonTitle(event),
                start: this.timezoneService.convertUtcToLocal(event.start, {
                    timezone: this.timezoneService.getTimezone(),
                    outputFormat: 'string',
                }),
                end: this.timezoneService.convertUtcToLocal(event.end, {
                    timezone: this.timezoneService.getTimezone(),
                    outputFormat: 'string',
                }),
                extendedProps: { type: EVENT_TYPES.LESSON, lesson: event.lesson },
            })
        );
    }

    private getLessonTitle(event: ICalendarEventDto): string {
        return event.lesson?.teachingLanguageName
            ? `${event.lesson.teachingLanguageName} Lesson`
            : event.title || 'Lesson';
    }



    private createDayOffEvent(dayOff: ICalendarEventDto): EventInput {
        return {
            id: createEventId(),
            title: 'Day Off',
            start: this.timezoneService.convertUtcToLocal(dayOff.start, {
                timezone: this.timezoneService.getTimezone(),
                outputFormat: 'string',
            }),
            end: this.timezoneService.convertUtcToLocal(dayOff.end, {
                timezone: this.timezoneService.getTimezone(),
                outputFormat: 'string',
            }),
            extendedProps: { type: EVENT_TYPES.DAY_OFF },
            className: 'status-day-off',
        };
    }

    // Utilities

    private getCustomButtons(): {
        [key: string]: { text: string; click: () => void };
    } {
        return {
            prevMonthButton: { text: '«', click: this.gotoPreviousMonth.bind(this) },
            nextMonthButton: { text: '»', click: this.gotoNextMonth.bind(this) },
            legendButton: { text: 'Legend', click: () => { } },
        };
    }

    private loadMockData(): void {
        this.originalEvents = [];
        console.log('Mock data loaded:', mockTeacherLessonsSchedule);
    }

    private debugTimezone(): void {
        const timezone = this.timezoneService.getTimezone();
        const now = moment.tz(timezone);
        const calendarNow = moment.tz(this.calendarOptions.now as Date, timezone);
        console.log(
            `Browser timezone: ${Intl.DateTimeFormat().resolvedOptions().timeZone}`
        );
        console.log(`Current timezone: ${timezone}`);
        console.log(
            `Current time in ${timezone}: ${now.format('YYYY-MM-DD HH:mm:ss Z')}`
        );
        console.log(`Calendar now: ${calendarNow.format('YYYY-MM-DD HH:mm:ss Z')}`);
        console.log(`Today in ${timezone}: ${now.format('YYYY-MM-DD')}`);
        console.log(
            `Sample event start: ${moment
                .tz(mockTeacherLessonsSchedule.scheduledLessons[0].start, timezone)
                .format('YYYY-MM-DD HH:mm:ss Z')}`
        );
    }

    private removeClonedTables(): void {
        document
            .querySelectorAll('.cloned-table')
            .forEach((table) => table.remove());
    }

    private logCurrentDate(): void {
        const calendarApi = this.calendarComponent.getApi();
        console.log(calendarApi.getDate());
    }

    getStatusClass(lessonStatus: string): string {
        // Find matching status config
        const statusConfig = Object.values(CALENDAR_STATUS_CONFIG).find(
            (config) => {
                if ('enumKeys' in config && config.enumKeys) {
                    return config.enumKeys.includes(lessonStatus);
                }
                return config.enumKey === lessonStatus;
            }
        );

        return (
            statusConfig?.statusClass || CALENDAR_STATUS_CONFIG['scheduled'].statusClass
        );
    }

    private generateFiltersFromEvents(events: EventInput[]): void {
        const filterMap = new Map<string, CalendarFilterOption>();
        const studentMap = new Map<string, StudentCalendarFilterOption>();

        // Exclude 'Day Off / Unavailable' from status filter options
        Object.values(CALENDAR_STATUS_CONFIG)
            .filter(
                (config) =>
                    config.value !== 'all' && config.value !== EVENT_TYPES.DAY_OFF
            )
            .forEach((config) => {
                const filterOption: CalendarFilterOption = {
                    label: config.label,
                    value: config.value,
                    type: 'status',
                };

                if ('enumKeys' in config && config.enumKeys) {
                    filterOption.enumKeys = [...config.enumKeys];
                    filterOption.enumKey = config.enumKeys[0];
                } else if ('enumKey' in config) {
                    filterOption.enumKey = config.enumKey;
                }

                filterMap.set(config.value, filterOption);
            });

        events.forEach((event) => {
            const lesson = event.extendedProps?.['lesson'] as
                | ICalendarLessonDto
                | undefined;
            if (!lesson) return;
            if (lesson!.students && lesson!.students?.length > 0) {
                lesson!.students.forEach((student) => {
                    if (!studentMap.has(student.userId)) {
                        studentMap.set(student.userId, {
                            userId: student.userId,
                            fullName: `${student.firstName} ${student.lastName}`,
                            profilePhotoUrl: student.profilePhotoUrl || '',
                        });
                    }
                });
            }
        });

        this.availableFilters.set(Array.from(filterMap.values()));
        this.studentCalendarFilterOptions = Array.from(studentMap.values());
    }



    private scrollToFirstLesson(): void {
        const calendarApi = this.calendarComponent?.getApi();

        if (!calendarApi) {
            console.warn('Calendar API not available.');
            return;
        }

        const events = calendarApi.getEvents();

        // Filter for lesson events and sort them by start time to find the earliest
        const lessonEvents = events
            .filter((event) => event.extendedProps?.['type'] === EVENT_TYPES.LESSON || event.extendedProps?.['type'] === EVENT_TYPES.DAY_OFF)
            .sort((a, b) => {
                const startA = a.start ? new Date(a.start).getTime() : Infinity;
                const startB = b.start ? new Date(b.start).getTime() : Infinity;
                return startA - startB;
            });

        const earliestLessonEvent = lessonEvents.length > 0 ? lessonEvents[0] : null;

        if (earliestLessonEvent && earliestLessonEvent.start) {
            console.log('Earliest lesson event found:', earliestLessonEvent);

            const userTimezone = this.timezoneService.getTimezone();
            const localTime = moment
                .tz(earliestLessonEvent.start, userTimezone)
                .format('HH:mm:ss');

            const currentView = calendarApi.view.type;

            // Scroll to the event's local time for time-based views
            if (currentView === 'timeGridWeek' || currentView === 'timeGridDay') {
                calendarApi.scrollToTime(localTime);
                console.log(`Scrolled to time: ${localTime} in view: ${currentView}`);
            } else if (currentView === 'dayGridMonth' || currentView === 'listWeek') {
                // For month or list views, navigate to the date of the first lesson
                // calendarApi.gotoDate(earliestLessonEvent.start);
                console.log(`Navigated to date: ${earliestLessonEvent.start} in view: ${currentView}`);
            } else {
                console.warn(`Unsupported view type for scrolling: ${currentView}`);
            }
        } else {
            // If no lesson events found, scroll to current time
            const currentTime = moment().format('HH:mm:ss');
            calendarApi.scrollToTime(currentTime);
            console.log('No lesson events found, scrolled to current time:', currentTime);
        }
    }

    onFilterCheckboxChange(): void {
        this.applyFilters();
    }

    // Add this method to get unique status filters for the legend
    getLegendLabels(): CalendarFilterOption[] {
        return Object.values(CALENDAR_STATUS_CONFIG)
            .filter((config) => config.value !== 'all')
            .map((config) => {
                const legendItem: CalendarFilterOption = {
                    label: config.label,
                    value: config.value,
                    type: config.type,
                };

                // Handle both single and multiple enum keys
                if ('enumKeys' in config && config.enumKeys) {
                    legendItem.enumKeys = [...config.enumKeys];
                    legendItem.enumKey = config.enumKeys[0]; // Primary key for backward compatibility
                } else if ('enumKey' in config) {
                    legendItem.enumKey = config.enumKey;
                }

                return legendItem;
            });
    }

    getLegendItems(): { label: string; color: string }[] {
        // Get all unique status values from the filter options
        const statusValues = this.availableFilters()
            .filter((f) => f.type === 'status')
            .map((f) => f.value);

        // Create legend items
        return statusValues.map((statusValue) => {
            const statusClass = this.getStatusClass(statusValue);
            const color = this.getStatusColor(statusClass);
            const label = this.getLessonStatusDisplayText(statusValue);

            return {
                label,
                color,
            };
        });
    }

    private getLessonStatusDisplayText(statusValue: string): string {
        const statusEnum = Number(statusValue);
        return STATUS_DISPLAY_LABELS[statusEnum as ILessonStatusEnum] || statusValue;
    }

    private getStatusColor(statusClass: string): string {
        // Define a mapping of status classes to colors
        const statusColors: { [key: string]: string } = {
            'status-scheduled': '#4CAF50', // Green
            'pending-confirmation-by-teacher': '#FF9800', // Orange
            'status-ongoing': '#2196F3', // Blue
            'status-completed': '#673AB7', // Purple
            'status-awaiting-evaluation': '#9C27B0', // Pink
            'status-no-show': '#F44336', // Red
            'status-available': '#8BC34A', // Light Green
            'status-day-off': '#795548', // Brown
        };

        return statusColors[statusClass] || '#4CAF50'; // Default to scheduled color
    }


}
