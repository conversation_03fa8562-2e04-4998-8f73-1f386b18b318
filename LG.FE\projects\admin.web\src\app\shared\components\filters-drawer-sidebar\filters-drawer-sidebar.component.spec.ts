import { ComponentFixture, TestBed } from '@angular/core/testing';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import { FiltersDrawerSidebarComponent } from './filters-drawer-sidebar.component';

describe('FiltersDrawerSidebarComponent', () => {
  let component: FiltersDrawerSidebarComponent;
  let fixture: ComponentFixture<FiltersDrawerSidebarComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [
        FiltersDrawerSidebarComponent,
        NoopAnimationsModule
      ]
    })
    .compileComponents();

    fixture = TestBed.createComponent(FiltersDrawerSidebarComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should have default configuration', () => {
    expect(component.config.headerText).toBe('Filters');
    expect(component.config.position).toBe('right');
    expect(component.config.showApplyButton).toBe(true);
    expect(component.config.showResetButton).toBe(true);
    expect(component.config.showCloseButton).toBe(true);
  });

  it('should emit visibleChange when visibility changes', () => {
    spyOn(component.visibleChange, 'emit');
    
    component.onVisibleChange(true);
    
    expect(component.visibleChange.emit).toHaveBeenCalledWith(true);
    expect(component.visible).toBe(true);
  });

  it('should emit actionClicked when apply button is clicked', () => {
    spyOn(component.actionClicked, 'emit');
    const mockEvent = new Event('click');
    
    component.onApplyClick(mockEvent);
    
    expect(component.actionClicked.emit).toHaveBeenCalledWith({
      action: 'apply',
      event: mockEvent
    });
  });

  it('should emit actionClicked when reset button is clicked', () => {
    spyOn(component.actionClicked, 'emit');
    const mockEvent = new Event('click');
    
    component.onResetClick(mockEvent);
    
    expect(component.actionClicked.emit).toHaveBeenCalledWith({
      action: 'reset',
      event: mockEvent
    });
  });

  it('should close drawer and emit events when close button is clicked', () => {
    spyOn(component.visibleChange, 'emit');
    spyOn(component.actionClicked, 'emit');
    const mockEvent = new Event('click');
    
    component.visible = true;
    component.onCloseClick(mockEvent);
    
    expect(component.visible).toBe(false);
    expect(component.visibleChange.emit).toHaveBeenCalledWith(false);
    expect(component.actionClicked.emit).toHaveBeenCalledWith({
      action: 'close',
      event: mockEvent
    });
  });

  it('should open drawer', () => {
    spyOn(component.visibleChange, 'emit');
    
    component.open();
    
    expect(component.visible).toBe(true);
    expect(component.visibleChange.emit).toHaveBeenCalledWith(true);
  });

  it('should close drawer', () => {
    spyOn(component.visibleChange, 'emit');
    
    component.visible = true;
    component.close();
    
    expect(component.visible).toBe(false);
    expect(component.visibleChange.emit).toHaveBeenCalledWith(false);
  });

  it('should toggle drawer visibility', () => {
    spyOn(component.visibleChange, 'emit');
    
    // Initially false
    expect(component.visible).toBe(false);
    
    component.toggle();
    expect(component.visible).toBe(true);
    expect(component.visibleChange.emit).toHaveBeenCalledWith(true);
    
    component.toggle();
    expect(component.visible).toBe(false);
    expect(component.visibleChange.emit).toHaveBeenCalledWith(false);
  });

  it('should update config when set', () => {
    const newConfig = {
      headerText: 'Custom Filters',
      position: 'left' as const,
      showApplyButton: false
    };
    
    component.config = newConfig;
    
    expect(component.config.headerText).toBe('Custom Filters');
    expect(component.config.position).toBe('left');
    expect(component.config.showApplyButton).toBe(false);
    // Should maintain other default values
    expect(component.config.showResetButton).toBe(true);
  });
});
