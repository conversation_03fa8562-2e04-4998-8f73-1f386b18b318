// Teachers List Filters Component Styles
@use "mixins";

:host {
  // Ensure the component itself fits within the drawer
  height: 100%;
  display: block;
  overflow: hidden;
}

.filters-content {
  padding-bottom: 1rem; // Add some bottom padding for better UX

  .field {
    margin-bottom: 1rem; // Reduced from 1.25rem for more compact layout

    label {
      font-weight: 600;
      color: var(--text-color);
      font-size: 0.875rem;
      margin-bottom: 0.5rem;
      display: block;
    }
  }



  // Modern checkbox styling


  // Enhanced spacing for drawer layout - more compact
  .grid {
    margin: 0;

    .col-12 {
      padding: 0 0 0.75rem 0; // Reduced padding for more compact layout
    }
  }

}

// Responsive design using mixins - more compact for drawer
@include mixins.breakpoint(mobile) {
  .filters-content {
    .field {
      margin-bottom: 0.75rem; // Even more compact on mobile
    }

    .field-checkbox {
      padding: 0.5rem;
      margin-bottom: 0.75rem;
    }

    .flex.gap-2 {
      flex-direction: column;
      gap: 0.5rem; // Reduced gap

      .p-datepicker {
        width: 100%;
      }
    }

    .p-multiselect-token {
      font-size: 0.7rem !important;
      padding: 0.2rem 0.5rem !important;
    }

    .field.p-2 {
      padding: 0.75rem !important;
      margin-bottom: 0.75rem !important;
    }
  }
}

@include mixins.breakpoint(tablet) {
  .filters-content {
    .field {
      margin-bottom: 1.125rem;
    }
  }
}
// Focus and accessibility improvements
.filters-content {
  .p-component:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
  }

  // Date range container
  .flex.gap-2 {
    .p-datepicker {
      flex: 1;
    }
  }

  // Enhanced visual hierarchy
  .flex.flex-column.gap-1 {
    gap: 1rem;
  }
}

// Dark mode support
// @media (prefers-color-scheme: dark) {
//   .filters-content {
//     .field-checkbox {

//       &:hover {
//         background: var(--surface-700);
//       }
//     }

//     .field.p-2 {
//       background: linear-gradient(135deg, var(--surface-800) 0%, var(--surface-700) 100%);
//       border-color: var(--surface-600);
//     }
//   }
// }
