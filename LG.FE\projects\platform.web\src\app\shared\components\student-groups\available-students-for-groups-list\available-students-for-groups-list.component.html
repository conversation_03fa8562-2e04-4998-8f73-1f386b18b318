<!-- Loading State -->
@if (isLoading()) {
<div class="suggestions-container border-1 border-round surface-border p-3">
  <lib-skeleton-loader
    layout="list"
    [count]="3"
    width="100%"
    height="4rem"
    styleClass="mb-3">
  </lib-skeleton-loader>
</div>
}

<!-- Error State -->
@else if (hasError()) {
<div class="suggestions-container border-1 border-round surface-border p-3">
  <div class="flex flex-column align-items-center justify-content-center gap-3 py-4">
    <i class="pi pi-exclamation-triangle text-orange-500" style="font-size: 2rem;"></i>
    <h4 class="text-orange-700 m-0">Unable to Load Recommendations</h4>
    <p class="text-600 text-center m-0">{{ errorMessage() }}</p>
    <p-button
      label="Try Again"
      icon="pi pi-refresh"
      size="small"
      [outlined]="true"
      (click)="refreshStudents()">
    </p-button>
  </div>
</div>
}

<!-- Main Container for Students and Groups -->
@else if (getAvailableStudentsCount() > 0 || (availableGroups() && availableGroups().length > 0)) {
<div class="suggestions-container">
  <!-- Personalized Recommendations Header - Only show when there are available students -->
  @if (getAvailableStudentsCount() > 0) {
    <div class="suggestions-header flex align-items-center justify-content-between flex-wrap gap-2 mb-3">
      <div class="header-content text-left flex-1">
        <div class="header-badge flex align-items-center gap-2 mb-2">
          <i class="pi pi-sparkles text-primary"></i>
          <span class="badge-text">Personalized Recommendations</span>
        </div>
        <!-- <h3 class="header-title m-0 mb-1">Learning Companions</h3> -->
        <!-- <p class="header-subtitle m-0">We found {{ getAvailableStudentsCount() }} students who match your preferences</p> -->
      </div>
      <div class="suggestions-count">
        <span class="count-badge">{{ getAvailableStudentsCount() }} matches</span>
      </div>
    </div>
  }

  <!-- Create Group Action Section - Only show when there are available students -->
  @if (getAvailableStudentsCount() > 0) {
    <div class="create-group-section text-center mb-3">
      <p-button
        (click)="navigateToCreateGroup()"
        [rounded]="true"
        icon="pi pi-plus-circle"
        iconPos="left"
        label="Create Group"
        styleClass="p-button-success p-button-sm">
      </p-button>

      <div class="student-count-info mt-2">
        <span class="count-text text-600">{{ getAvailableStudentsCount() }} available students</span>
      </div>
    </div>
  }

  <!-- Conditional Divider for Groups - Only show when both students and groups are available -->
  @if (getAvailableStudentsCount() > 0 && availableGroups() && availableGroups().length > 0) {
    <div class="divider-section mb-3">
      <div class="divider-container flex align-items-center gap-3">
        <div class="divider-line flex-1"></div>
        <span class="divider-text text-600 font-medium">Or choose existing group</span>
        <div class="divider-line flex-1"></div>
      </div>
    </div>
  }

  <!-- Available Groups Section -->
@if (availableGroups() && availableGroups().length > 0) {
<div class="available-groups-container border-1 border-round surface-border p-3"
     [class.mt-3]="getAvailableStudentsCount() === 0">

  <!-- Groups Header - Show when no students available but groups exist -->
  @if (getAvailableStudentsCount() === 0) {
    <div class="groups-header flex align-items-center justify-content-between flex-wrap gap-2 mb-3">
      <div class="header-content text-left flex-1">
        <div class="header-badge flex align-items-center gap-2 mb-2">
          <i class="pi pi-users text-primary"></i>
          <span class="badge-text">Available Groups</span>
        </div>
        <h3 class="header-title m-0 mb-1">Join Existing Groups</h3>
        <p class="header-subtitle m-0">Choose the perfect group for your learning journey</p>
      </div>
      <div class="groups-count">
        <span class="count-badge">{{ availableGroups().length }} groups</span>
      </div>
    </div>
  }

  <!-- Groups List -->
  <div class="groups-list text-left">
    <div
      class="group-card"
      *ngFor="let group of availableGroups(); trackBy: trackByGroupId"
      (click)="selectGroup(group)">

      <!-- Group Header -->
      <div class="group-header">
        <div class="group-info">
          <p class="group-name">{{ group.groupName }}</p>
          <div class="group-meta">
            @if (group.teachingLanguageName) {
            <span class="language-info">
              <i class="pi pi-globe"></i>
              {{ group.teachingLanguageName }} {{services.general.getILanguageLevelsEnumText(group.languageLevel, false)}}
            </span>
            }
            @if (group.languageLevel) {
            <span class="level-badge" [class]="'level-' + group.languageLevel">
              {{ services.general.getILanguageLevelsEnumText(group.languageLevel) }}
            </span>
            }
          </div>
        </div>

        <div class="group-stats">
          @if (group.memberCount !== undefined && group.memberCount !== null) {
          <div class="member-count">
            <i class="pi pi-users"></i>
            <span>{{ group.memberCount }}</span>
          </div>
          }
          <div class="group-actions">
            <button class="join-btn" (click)="joinGroup(group, $event)">
              <i class="pi pi-pencil"></i>
              <span>View Group Details</span>
            </button>
            <!-- <button class="info-btn" (click)="viewGroupDetails(group, $event)">
              <i class="pi pi-info-circle"></i>
            </button> -->
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
}


</div>
}

<!-- Empty State -->
@else if (!isLoading() && !hasError()) {
<!-- <div class="suggestions-container border-1 border-round surface-border p-3">
  <div class="flex flex-column align-items-center justify-content-center gap-3 py-4">
    <i class="pi pi-users text-gray-400" style="font-size: 2rem;"></i>
    <h4 class="text-gray-600 m-0">No Recommendations Available</h4>
    <p class="text-600 text-center m-0">We couldn't find any students or groups that match your preferences at the moment.</p>
    <p-button
      label="Refresh"
      icon="pi pi-refresh"
      size="small"
      [outlined]="true"
      (click)="refreshStudents()">
    </p-button>
  </div>
</div> -->
}
