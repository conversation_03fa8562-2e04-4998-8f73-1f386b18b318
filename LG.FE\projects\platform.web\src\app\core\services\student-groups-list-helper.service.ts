import { Injectable } from '@angular/core';
import { Params } from '@angular/router';
import {
  IGetStudentGroupsRequest,
  ILanguageLevelsEnum,
  nameOf
} from 'SharedModules.Library';

/**
 * Helper service for student groups list component
 * Provides utilities for request mapping, defaults, and query parameter handling
 * Following the same pattern as StudentsListHelperService
 */
@Injectable({
  providedIn: 'root'
})
export class StudentGroupsListHelperService {
  // Default pagination values
  static readonly DEFAULT_PAGE_NUMBER = 1;
  static readonly DEFAULT_PAGE_SIZE = 5;
  static readonly DEFAULT_SORT_COLUMN = null;
  static readonly DEFAULT_SORT_DIRECTION = null;

  /**
   * Creates a default IGetStudentGroupsRequest object with standard values for backend pagination
   * @returns IGetStudentGroupsRequest with default values optimized for pagination
   */
  createDefaultStudentGroupsRequestForParent(): IGetStudentGroupsRequest {
    return {
      pageNumber: StudentGroupsListHelperService.DEFAULT_PAGE_NUMBER,
      pageSize: StudentGroupsListHelperService.DEFAULT_PAGE_SIZE,
      sortColumn: StudentGroupsListHelperService.DEFAULT_SORT_COLUMN,
      sortDirection: StudentGroupsListHelperService.DEFAULT_SORT_DIRECTION,
      searchTerm: null,
      teachingLanguage: null,
      teachingLanguageLevel: null,
      parentId: null,
      teacherId: null,
      studentId: null,
      includeDeleted: false
    };
  }

  /**
   * Maps URL query parameters to an IGetStudentGroupsRequest object
   * @param params Route parameters from ActivatedRoute
   * @param defaultRequest Optional default request to use as base
   * @returns IGetStudentGroupsRequest populated with values from URL parameters
   */
  mapQueryParamsToStudentGroupsRequest(params: Params, defaultRequest?: IGetStudentGroupsRequest): IGetStudentGroupsRequest {
    // Start with clean defaults or use provided default
    const request: IGetStudentGroupsRequest = defaultRequest || this.createDefaultStudentGroupsRequestForParent();

    // Use nameOf to get type-safe property names
    const paramsMap = nameOf<IGetStudentGroupsRequest>();

    // Map pagination parameters
    if (params[paramsMap.pageNumber]) {
      const pageNumber = parseInt(params[paramsMap.pageNumber], 10);
      if (!isNaN(pageNumber) && pageNumber > 0) {
        request.pageNumber = pageNumber;
      }
    }

    if (params[paramsMap.pageSize]) {
      const pageSize = parseInt(params[paramsMap.pageSize], 10);
      if (!isNaN(pageSize) && pageSize > 0) {
        request.pageSize = pageSize;
      }
    }

    // Map search and filter parameters
    if (params[paramsMap.searchTerm!]) {
      request.searchTerm = params[paramsMap.searchTerm!];
    }

    if (params[paramsMap.teachingLanguage!]) {
      request.teachingLanguage = params[paramsMap.teachingLanguage!];
    }

    if (params[paramsMap.teachingLanguageLevel!]) {
      const level = parseInt(params[paramsMap.teachingLanguageLevel!], 10);
      if (!isNaN(level)) {
        request.teachingLanguageLevel = level as ILanguageLevelsEnum;
      }
    }

    if (params[paramsMap.sortColumn!]) {
      request.sortColumn = params[paramsMap.sortColumn!];
    }

    if (params[paramsMap.sortDirection!]) {
      request.sortDirection = params[paramsMap.sortDirection!];
    }

    if (params[paramsMap.includeDeleted]) {
      request.includeDeleted = params[paramsMap.includeDeleted] === 'true';
    }

    return request;
  }

  /**
   * Converts an IGetStudentGroupsRequest object to URL query parameters
   * @param request The request object to convert
   * @returns Object containing query parameters
   */
  convertStudentGroupsRequestToQueryParams(request: IGetStudentGroupsRequest): { [key: string]: any } {
    const params: { [key: string]: any } = {};
    const paramsMap = nameOf<IGetStudentGroupsRequest>();

    // Only include non-null, non-undefined, and non-default values
    if (request.pageNumber && request.pageNumber !== StudentGroupsListHelperService.DEFAULT_PAGE_NUMBER) {
      params[paramsMap.pageNumber] = request.pageNumber;
    }

    if (request.pageSize && request.pageSize !== StudentGroupsListHelperService.DEFAULT_PAGE_SIZE) {
      params[paramsMap.pageSize] = request.pageSize;
    }

    if (request.searchTerm) {
      params[paramsMap.searchTerm!] = request.searchTerm;
    }

    if (request.teachingLanguage) {
      params[paramsMap.teachingLanguage!] = request.teachingLanguage;
    }

    if (request.teachingLanguageLevel !== null && request.teachingLanguageLevel !== undefined) {
      params[paramsMap.teachingLanguageLevel!] = request.teachingLanguageLevel;
    }

    if (request.sortColumn) {
      params[paramsMap.sortColumn!] = request.sortColumn;
    }

    if (request.sortDirection) {
      params[paramsMap.sortDirection!] = request.sortDirection;
    }

    if (request.includeDeleted) {
      params[paramsMap.includeDeleted] = request.includeDeleted;
    }

    return params;
  }
}
