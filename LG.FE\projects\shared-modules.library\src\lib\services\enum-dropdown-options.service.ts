﻿// teacher-list-component-helper.service.ts
import {Injectable} from '@angular/core';
import { IEnumDropdownOptions } from '../models/enum-dropdown-options.model';
import {
  IAvailabilityStatusOptionsEnum, IGenderEnum, ILanguageLevelsEnum, ITeacherAvailabilityStatusEnum,
  ITeacherStudentAgesExperienceEnum,
  ITeacherStudentAgesPreferenceEnum,
  IUserAccountStatus
} from '../GeneratedTsFiles';


@Injectable({
  providedIn: 'root'
})
export class EnumDropdownOptionsService {
  // this is the teacher availability status the same as its been saved in the database
  teacherAvailabilityStatusOptions: IEnumDropdownOptions[] = [
    {value: ITeacherAvailabilityStatusEnum.NotSet, label: 'Not Set'},
    {value: ITeacherAvailabilityStatusEnum.Open, label: 'Open'},
    {value: ITeacherAvailabilityStatusEnum.Full, label: 'Full'},
    {value: ITeacherAvailabilityStatusEnum.Limited, label: 'Limited'}
  ];

  // this is the teacher availability statuses used only as multi-select filter
  availabilityStatusEnumFlagsOptions: IEnumDropdownOptions[] = [
    {label: 'Not Set', value: IAvailabilityStatusOptionsEnum.NotSet},
    {label: 'Open', value: IAvailabilityStatusOptionsEnum.Open},
    {label: 'Full', value: IAvailabilityStatusOptionsEnum.Full},
    {label: 'Limited', value: IAvailabilityStatusOptionsEnum.Limited},
    {label: 'Resigned', value: IAvailabilityStatusOptionsEnum.Resigned}
  ];

  genderOptions: IEnumDropdownOptions[] = [
    {label: 'All', value: IGenderEnum.None},
    {label: 'Male', value: IGenderEnum.Male},
    {label: 'Female', value: IGenderEnum.Female}
  ];

  teachingAgesExperienceEnumFlagsOptions: IEnumDropdownOptions[] = [
    {label: 'None', value: ITeacherStudentAgesExperienceEnum.None},
    {label: '2-4 Years', value: ITeacherStudentAgesExperienceEnum.TowToFour},
    {label: '4-6 Years', value: ITeacherStudentAgesExperienceEnum.FourToSix},
    {label: '6-8 Years', value: ITeacherStudentAgesExperienceEnum.SixToEight},
    {label: '8-10 Years', value: ITeacherStudentAgesExperienceEnum.EightToTen},
    {label: 'Above 10 Years', value: ITeacherStudentAgesExperienceEnum.AboveTen}
  ];

  teacherStudentAgesPreferenceEnumFlagsOptions: IEnumDropdownOptions[] = [
    {label: '2-4 Years', value: ITeacherStudentAgesPreferenceEnum.TowToFour},
    {label: '4-6 Years', value: ITeacherStudentAgesPreferenceEnum.FourToSix},
    {label: '6-8 Years', value: ITeacherStudentAgesPreferenceEnum.SixToEight},
    {label: '8-10 Years', value: ITeacherStudentAgesPreferenceEnum.EightToTen},
    {label: 'Above 10 Years', value: ITeacherStudentAgesPreferenceEnum.AboveTen}
  ];

  userAccountStatusOptions: IEnumDropdownOptions[] = [
    {label: 'Active', value: IUserAccountStatus.Active},
    {label: 'Pending', value: IUserAccountStatus.Deleted},
    {label: 'Suspended', value: IUserAccountStatus.InActive},
    {label: 'Closed', value: IUserAccountStatus.Resigned}
  ];

  languageLevelsOptions: IEnumDropdownOptions[] = [
    {label: 'None', value: ILanguageLevelsEnum.None},
    {label: 'A1 - Beginner', value: ILanguageLevelsEnum.A1},
    {label: 'A2 - Elementary', value: ILanguageLevelsEnum.A2},
    {label: 'B1 - Intermediate', value: ILanguageLevelsEnum.B1},
    {label: 'B2 - Upper Intermediate', value: ILanguageLevelsEnum.B2},
    {label: 'C1 - Advanced', value: ILanguageLevelsEnum.C1},
    {label: 'C2 - Proficient', value: ILanguageLevelsEnum.C2}
  ];


  constructor() {
  }

  /**
   * Gets the label for a given value from a specified options array
   * @param optionsArray The array of IEnumDropdownOptions to search in
   * @param value The enum value to find the label for
   * @param defaultLabel Optional default label to return if value is not found
   * @returns The label corresponding to the value, or the defaultLabel if not found
   */
  getLabelFromValue<T>(optionsArray: IEnumDropdownOptions[], value: T, defaultLabel: string = ''): string {
    const option = optionsArray.find(opt => opt.value === value);
    return option ? option.label : defaultLabel;
  }

  /**
   * Gets the label for a language level enum value
   * @param languageLevel The ILanguageLevelsEnum value
   * @param defaultLabel Optional default label to return if value is not found
   * @returns The label corresponding to the language level
   */
  getLanguageLevelLabel(languageLevel: ILanguageLevelsEnum, defaultLabel: string = ''): string {
    return this.getLabelFromValue(this.languageLevelsOptions, languageLevel, defaultLabel);
  }

  /**
   * Gets a display-friendly label for language level in group context
   * @param languageLevel The ILanguageLevelsEnum value
   * @param hideNoneLevel Whether to hide the level when it's None (returns empty string)
   * @returns The label corresponding to the language level, optimized for group display
   */
  getLanguageLevelDisplayLabel(languageLevel: ILanguageLevelsEnum, hideNoneLevel: boolean = true): string {
    // If it's None/0 and we want to hide it, return empty string
    if (languageLevel === ILanguageLevelsEnum.None && hideNoneLevel) {
      return '';
    }

    // For group display, we want shorter, cleaner labels
    switch (languageLevel) {
      case ILanguageLevelsEnum.None:
        return 'None';
      case ILanguageLevelsEnum.A1:
        return 'A1';
      case ILanguageLevelsEnum.A2:
        return 'A2';
      case ILanguageLevelsEnum.B1:
        return 'B1';
      case ILanguageLevelsEnum.B2:
        return 'B2';
      case ILanguageLevelsEnum.C1:
        return 'C1';
      case ILanguageLevelsEnum.C2:
        return 'C2';
      default:
        return '';
    }
  }

}
