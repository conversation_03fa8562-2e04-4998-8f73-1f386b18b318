import { FormControl } from "@angular/forms";
import { ILessonStatusEnum } from "../GeneratedTsFiles";

export interface Country {
  name: string,
  code: string,
  emoji: string,
  unicode: string,
  image: string
}

export interface Timezone {
  value: string,
  abbr: string,
  offset: number,
  isdst: boolean,
  text: string,
  utc: string[],
  countries?: string[]
}

export interface Language {
  code: string;
  name: string;
  nativeName?: string;
}

export interface TopMenuItem {
  id: string;
  text: string;
  link?: string;
  icon?: string;
  image?: string;
  class?: string;
  title?: string;
  target?: '_blank' | '_self' | '_parent' | '_top';
  role?: string;
  position?: 'left' | 'right' | 'top' | 'bottom';
  dataAttributes?: { [key: string]: string };
  style?: string;
  disabled?: boolean;
  active?: boolean;
  tooltip?: string;
  order?: number;
  badge?: string;
  separator?: boolean;
  shortcut?: string;
  prefixHtml?: string;
  suffixHtml?: string;
  submenu?: TopMenuItem[]; // Recursive for submenu items
  loggedIn?: boolean;
  hidden?: boolean;
  guestOnly?: boolean;
  callback?: (event: any) => void;
}

export enum IUserRole {
  STUDENT = 'Student',
  TEACHER = 'Teacher',
  PARENT = 'Parent',
  ADMIN = 'Admin',
}

export interface DecodedJWT {
  unique_name: string;
  jti: string;
  id: string;
  firstName: string;
  lastName: string;
  HasCompletedRegistration: boolean;
  dateOfRegistrationCompletion: Date | null;
  hasCompletedOnBoardingTutorial: boolean;
  timeZoneIana: string;
  timeZoneDisplayName: string;
  gender: string;
  age: string;
  isGoogleAccount: boolean;
  hasSetupPassword: boolean;
  isImpersonated: boolean;
  originalUseId: string;
  originalUserFirstName: string;
  originalUserLastName: string;
  role: string;
  nbf: number;
  exp: number;
  iat: number;
  iss: string;
  aud: string;
}

export enum LoginModeEnum {
  None = 'none',
  Student = 'student',
  Parent = 'parent',
  Teacher = 'teacher',
  ForgotPassword = 'forgot-password',
  SetPasswordFirstTime = 'set-password-first-time',
}

export enum FreeTrialReasonModeEnum {
  NEW_TEACHER = 'teacher',
  NEW_LANGUAGE = 'language',
  NEW_STUDENT = 'student',
  OTHER = 'other',
}

export interface DeviceKind {
  w576down: boolean,
  w768down: boolean,
  w992down: boolean,
  w1024down: boolean,
  w1200down: boolean,
  is1366: boolean,
  isBig: boolean,
  w576up?: boolean,
  w768up?: boolean,
  w992up?: boolean,
  w1024up?: boolean,
  w1366up?: boolean,
  w1500up?: boolean,
}

export enum Dimentions {
  w576 = 576,
  w768 = 768,
  w992 = 992,
  w1024 = 1024,
  w1200 = 1200,
  w1366 = 1366,
  w1500 = 1500,
  h850 = 850
}

export enum LibraryViewCardEnum {
  View = 'view',
  Edit = 'edit',
  Delete = 'delete',
  Create = 'create',
}

export enum EvaluationType {
  Reading = 'Reading',
  Writing = 'Writing',
  Listening = 'Listening',
  Speaking = 'Speaking',
  Attentiveness = 'Attentiveness',
  Motivation = 'Motivation'
}


export enum GroupDialogState {
  None = -1,
  CreateGroupSuggestionStep = 0,
  CreateGroup = 1,
  DeleteGroup = 3,
  EditGroup = 4,
  AfterCreateSuccess = 6,
  AfterEditSuccess = 7,
  ViewGroup = 8,
}

export interface ProfileFieldConfig {
  fieldName: string;
  required: boolean;
  disabled?: boolean;
  propertyName?: string;
}

export interface RoleProfileConfig {
  [role: string]: ProfileFieldConfig[];
}

// Create a mapped type that converts each property of T to a FormControl
export type FormControlsFor<T> = {
  [K in keyof T]: FormControl<T[K] | null>;
};

// Remove the separate interface declaration
export const nameOf: {
  <T>(): { [K in keyof T]: K };
  cache?: WeakMap<object, object>;
} = function nameOf<T>() {
  const cache = (nameOf as any).cache ?? new WeakMap<object, object>();
  (nameOf as any).cache = cache;

  const typeKey = Object.prototype;
  return (cache.get(typeKey) ??
    cache.set(typeKey, new Proxy({}, { get: (_, prop) => prop })).get(typeKey)
  ) as { [K in keyof T]: K };
};

export interface UserRolesContext {
  parentId?: string;
  studentId?: string;
  teacherId?: string;
  actingRole?: IUserRole;
}

export class UserRolesContextBuilder {
  private context: Partial<UserRolesContext> = {};

  static create(): UserRolesContextBuilder {
    return new UserRolesContextBuilder();
  }

  forParent(id: string): this {
    this.context.parentId = id;
    return this;
  }

  forStudent(id: string): this {
    this.context.studentId = id;
    return this;
  }

  forTeacher(id: string): this {
    this.context.teacherId = id;
    return this;
  }

  withActingRole(role: IUserRole): this {
    this.context.actingRole = role;
    return this;
  }

  build(): UserRolesContext {
    if (!this.context.actingRole) {
      throw new Error('actingRole is required to build UserRolesContext');
    }

    return this.context as UserRolesContext;
  }
}

export function getUserRolesContext(
  ids: {
    actingRole: IUserRole;
    userId: string;
    studentId?: string;
    teacherId?: string;
    parentId?: string;
  },
  includes: {
    includeParentId?: boolean;
    includeStudentId?: boolean;
    includeTeacherId?: boolean;
  }
): UserRolesContext {
  const builder = UserRolesContextBuilder.create().withActingRole(ids.actingRole);

  if (includes.includeParentId) {
    builder.forParent(ids.parentId ?? ids.userId);
  }

  if (includes.includeStudentId) {
    builder.forStudent(ids.studentId ?? ids.userId);
  }

  if (includes.includeTeacherId) {
    builder.forTeacher(ids.teacherId ?? ids.userId);
  }

  return builder.build();
}



// Define a type for the Lesson Status Titles map
type LessonStatusTitles = {
  [key in ILessonStatusEnum]: string;
};

// Create a strongly typed const map for lesson titles
export const LESSON_STATUS_TITLES: LessonStatusTitles = {
  [ILessonStatusEnum.PendingConfirmationByTeacher]: 'Pending Confirmation',
  [ILessonStatusEnum.Scheduled]: 'Scheduled',
  [ILessonStatusEnum.AwaitingEvaluation]: 'Awaiting Evaluation',
  [ILessonStatusEnum.Completed]: 'Completed',
  [ILessonStatusEnum.StudentNoShow]: 'Student No-Show',
  [ILessonStatusEnum.TeacherNoShow]: 'Teacher No-Show',
  [ILessonStatusEnum.CancelledByParent]: 'Cancelled by Parent',
  [ILessonStatusEnum.CancelledByTeacher]: 'Cancelled by Teacher',
  [ILessonStatusEnum.CancelledByAdmin]: 'Cancelled by Admin',
};


export const CALENDAR_STATUS_CLASSES = {
  scheduledLessons: 'status-scheduled',
  pendingLessons: 'status-pending-confirmation-by-teacher',
  ongoingLessons: 'status-ongoing',
  completedLessons: 'status-completed',
  awaitingEvaluationLessons: 'status-awaiting-evaluation',
  noShowAndCanceledLessons: 'status-no-show', // Standardize to 'status-no-show'
  availableSlots: 'status-available',
  daysOff: 'status-day-off',
} as const;


export enum LessonStatusLegend {
  Scheduled = "Scheduled Lessons",
  Pending = "Pending Lessons",
  Ongoing = "Ongoing Lessons",
  AwaitingEvaluation = "Awaiting Evaluation Lessons",
  Completed = "Completed Lessons",
  NoShowAndCanceled = "No-Show and Canceled Lessons",
}


export interface CalendarFilterOption {
  label: string;
  value: string;
  type: string;
  enumKey?: string;
  enumKeys?: string[];
}

export const EVENT_TYPES = {
  LESSON: 'lesson',
  DAY_OFF: 'dayOff',
  AVAILABLE: 'available',
} as const;

export const AVAILABILITY_LEGEND_ITEMS: CalendarFilterOption[] = [
  {
      label: 'Day Off',
      value: 'dayOff',
      type: EVENT_TYPES.DAY_OFF
  },
  {
      label: 'Available',
      value: 'available',
      type: EVENT_TYPES.AVAILABLE
  }
];

export const LESSON_STATUS_LEGEND_ITEMS: CalendarFilterOption[] = [
  {
      label: LESSON_STATUS_TITLES[ILessonStatusEnum.Scheduled],
      value: 'scheduledLessons',
      type: EVENT_TYPES.LESSON,
      enumKey: ILessonStatusEnum[ILessonStatusEnum.Scheduled]
  },
  {
      label: LESSON_STATUS_TITLES[ILessonStatusEnum.PendingConfirmationByTeacher],
      value: 'pendingLessons',
      type: EVENT_TYPES.LESSON,
      enumKey: ILessonStatusEnum[ILessonStatusEnum.PendingConfirmationByTeacher]
  },
  {
      label: LESSON_STATUS_TITLES[ILessonStatusEnum.AwaitingEvaluation],
      value: 'awaitingEvaluationLessons',
      type: EVENT_TYPES.LESSON,
      enumKey: ILessonStatusEnum[ILessonStatusEnum.AwaitingEvaluation]
  },
  {
      label: LESSON_STATUS_TITLES[ILessonStatusEnum.Completed],
      value: 'completedLessons',
      type: EVENT_TYPES.LESSON,
      enumKey: ILessonStatusEnum[ILessonStatusEnum.Completed]
  },
  {
      label: 'No Show & Canceled',
      value: 'noShowAndCanceledLessons',
      type: EVENT_TYPES.LESSON
  }
];