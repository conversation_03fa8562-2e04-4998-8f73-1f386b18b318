import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, inject, signal } from '@angular/core';
import { ActivatedRoute, RouterModule } from '@angular/router';
import { ButtonModule } from 'primeng/button';
import { RegisterService } from '@platform.app/core/services/register.service';
import { AvailableStudentsForGroupsListComponent } from '@platform.app/shared/components/student-groups/available-students-for-groups-list/available-students-for-groups-list.component';
import { GalacticSuccessCardComponent, GalacticSuccessConfig } from '@platform.app/shared/components/galactic-success-card';

@Component({
  selector: 'app-register-success',
  imports: [
    CommonModule,
    ButtonModule,
    RouterModule,
    AvailableStudentsForGroupsListComponent,
    GalacticSuccessCardComponent,
  ],
  templateUrl: './register-success.component.html',
  styleUrl: './register-success.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class RegisterSuccessComponent {
  private readonly route = inject(ActivatedRoute);

  constructor(private registerService: RegisterService) {
  }

  // Reactive state using signals
  studentId = signal<string | null>(null);
  teachingLanguageId = signal<string | null>(null);
  teachingLanguageName = signal<string | null>(null);
  isAnimationComplete = signal(false);

  galacticConfig = signal<GalacticSuccessConfig>({
    title: '🚀Student registered successfully!',
    subtitle: 'Your learning adventure begins now',
    badgeText: 'FREE',
    badgeIcon: 'pi pi-star-fill',
    equipmentItems: ['🛡️ No cost, no risk', '👨‍🚀 Expert teachers', '📡 24h support'],
    showDestinationName: true,
    destinationName: '',
    theme: 'success'
  });

  ngOnInit(): void {
    this.registerService.clear('registerStudentRequest');
    // Subscribe to query parameters and update signals
    this.route.queryParams.subscribe(params => {
      this.studentId.set(params['studentId'] || null);
      this.teachingLanguageId.set(params['languageOfInterestId'] || null);
      this.teachingLanguageName.set(params['teachingLanguageName'] || null);
      console.log('Query Params:', {
        studentId: this.studentId(),
        teachingLanguageId: this.teachingLanguageId(),
        teachingLanguageName: this.teachingLanguageName()
      });
    });
    this.updateGalacticConfig();

    // Trigger animation completion after a delay
    setTimeout(() => {
      this.isAnimationComplete.set(true);
    }, 1500);
  }

  /**
   * Updates the galactic config with current language information
   */
  private updateGalacticConfig(): void {
    const currentConfig = this.galacticConfig();
    this.galacticConfig.set({
      ...currentConfig,
      subtitle: `Your ${this.teachingLanguageName()} adventure begins now`,
      badgeText: `FREE ${this.teachingLanguageName()} Trial Lesson`
    });
  }

  addAnotherStudent(): void {
    this.registerService.goToRegisterNewStudent();
  }

  goToDashboard(): void {
    this.registerService.navigateToNextStep('dashboard');
  }
}
