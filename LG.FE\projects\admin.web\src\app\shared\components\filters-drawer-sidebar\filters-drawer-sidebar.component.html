<!-- Filters Drawer Sidebar -->
<p-drawer 
  [visible]="isVisible()" 
  (visibleChange)="onVisibleChange($event)"
  [position]="drawerConfig().position || 'right'"
  [blockScroll]="drawerConfig().blockScroll"
  [modal]="drawerConfig().modal"
  [dismissible]="drawerConfig().dismissibleOnEscape"
  [style]="{ 
    width: drawerConfig().width, 
    height: drawerConfig().height 
  }"
  styleClass="filters-drawer">
  
  <ng-template pTemplate="headless">
    <div class="filters-drawer-container h-full flex flex-column">
      
      <!-- Header -->
      <div class="filters-drawer-header flex justify-content-between align-items-center p-3 border-bottom-1 surface-border">
        <div class="flex align-items-center">
          <i [class]="drawerConfig().headerIcon" class="text-primary mr-2 text-xl"></i>
          <h3 class="text-900 font-semibold m-0">{{ drawerConfig().headerText }}</h3>
        </div>
        
        <p-button 
          *ngIf="drawerConfig().showCloseButton"
          [icon]="drawerConfig().closeButtonIcon"
          class="p-button-rounded p-button-text p-button-sm"
          (onClick)="onCloseClick($event)">
        </p-button>
      </div>

      <!-- Scrollable Content Area -->
      <div class="filters-drawer-content flex-1 overflow-y-auto p-3">
        <!-- Filter Content Template -->
        <ng-container *ngIf="filterContentTemplate">
          <ng-container *ngTemplateOutlet="filterContentTemplate"></ng-container>
        </ng-container>
        
        <!-- Default content if no template provided -->
        <ng-container *ngIf="!filterContentTemplate">
          <div class="text-center text-500 p-4">
            <i class="pi pi-filter text-4xl mb-3"></i>
            <p class="m-0">No filter content provided</p>
            <small class="text-400">Use [filterContentTemplate] to provide filter content</small>
          </div>
        </ng-container>
      </div>

      <!-- Fixed Bottom Actions -->
      <div class="filters-drawer-actions border-top-1 surface-border p-3">
        <div class="flex gap-2 w-full justify-content-between">
          
          <!-- Reset Button -->
          <p-button 
            *ngIf="drawerConfig().showResetButton"
            [label]="drawerConfig().resetButtonLabel"
            [icon]="drawerConfig().resetButtonIcon"
            severity="secondary"
            class="p-button-outlined flex-1 md:flex-none"
            (onClick)="onResetClick($event)">
          </p-button>

          <!-- Apply Button -->
          <p-button 
            *ngIf="drawerConfig().showApplyButton"
            [label]="drawerConfig().applyButtonLabel"
            [icon]="drawerConfig().applyButtonIcon"
            severity="primary"
            class="flex-1 md:flex-none"
            (onClick)="onApplyClick($event)">
          </p-button>
          
        </div>
      </div>
      
    </div>
  </ng-template>
</p-drawer>
