// Applied filters styling - Original from teachers-list component
  // Applied filters styling

.applied-filters-container {
  margin-bottom: 1rem;
  box-sizing: border-box;
  width: 100%;
  // width: calc(100% - 360px); // Account for sidebar width + gap
  display: inline-block;
  font-size: 16px;
  color: #fff !important;

  .filter-chip {
    background-color: #5f4896 !important;
    border: 1px solid var(--surface-200);
    display: inline-block;
    margin: 0.25rem;
    padding: 2px !important;
    color: #fff !important;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;

    span, i {
      font-size: 12px !important;
      color: #fff !important;
    }

    .p-button {
      color: #fff !important;
      width: 1.5rem;
      height: 1.5rem;
      padding: 0;
      border-radius: 50%;

      &:hover {
        background-color: #fff !important;
        color: var(--p-button-outlined-danger-color) !important;
      }
    }
  }
}
