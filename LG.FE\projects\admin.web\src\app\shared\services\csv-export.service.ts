import { Injectable } from '@angular/core';
import { IDataGridFields } from 'SharedModules.Library';
import moment from 'moment-timezone';

export interface ICsvExportOptions {
  filename?: string;
  dateFormat?: string;
  separator?: string;
  includeHeaders?: boolean;
}

@Injectable({
  providedIn: 'root'
})
export class CsvExportService {

  /**
   * Generic method to export any data to CSV
   * @param data Array of objects to export
   * @param columns Column definitions for the export
   * @param options Export options
   */
  exportToCSV<T extends Record<string, any>>(
    data: T[],
    columns: IDataGridFields[],
    options: ICsvExportOptions = {}
  ): void {
    if (!data || data.length === 0) {
      console.warn('No data to export');
      return;
    }

    const defaultOptions: ICsvExportOptions = {
      filename: `export_${moment().format('YYYY-MM-DD_HH-mm-ss')}.csv`,
      separator: ',',
      includeHeaders: true,
      ...options
    };

    // Convert data to CSV format
    const csvContent = this.convertToCSV(data, columns, defaultOptions);

    // Download the CSV file
    this.downloadCSV(csvContent, defaultOptions.filename!);
  }

  /**
   * Converts data array to CSV string format
   */
  private convertToCSV<T extends Record<string, any>>(
    data: T[],
    columns: IDataGridFields[],
    options: ICsvExportOptions
  ): string {
    if (data.length === 0) {
      return '';
    }

    const fields = columns.map(col => col.field);
    let csvString = '';

    // Add headers if requested
    if (options.includeHeaders) {
      const headers = columns.map(col => col.header);
      const escapedHeaders = headers.map(header => this.escapeCsvValue(header));
      csvString = escapedHeaders.join(options.separator!) + '\r\n';
    }

    // Add data rows
    for (const item of data) {
      const values = fields.map(field => {
        const value = item[field];
        // Get the raw value, defaulting to empty string for null/undefined
        const safeValue = value !== undefined && value !== null ? value : '';
        return this.escapeCsvValue(String(safeValue));
      });

      // Ensure we have the correct number of columns
      if (values.length !== fields.length) {
        console.warn(`CSV row has ${values.length} values but expected ${fields.length} columns`);
      }

      csvString += values.join(options.separator!) + '\r\n';
    }

    return csvString;
  }

  /**
   * Properly escapes CSV values by wrapping in quotes only when necessary
   */
  private escapeCsvValue(value: string): string {
    // Convert to string if not already (handles null, undefined)
    const stringValue = value == null ? '' : String(value);

    // Return empty quoted string for empty values to maintain CSV structure
    if (stringValue === '') {
      return '""';
    }

    // Check if the value contains special characters that require escaping
    // Special characters: comma, double quote, newline, carriage return
    const needsQuoting = /[",\r\n]/.test(stringValue);

    if (needsQuoting) {
      // Escape internal quotes by doubling them
      const escapedValue = stringValue.replace(/"/g, '""');
      return `"${escapedValue}"`;
    }

    // Return the value as-is if no special characters are present
    return stringValue;
  }

  /**
   * Downloads CSV content as a file
   */
  private downloadCSV(csvContent: string, filename: string): void {
    const blob = new Blob([csvContent], {
      type: 'text/csv;charset=utf-8;'
    });

    // Create download link and trigger it
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = filename;
    link.style.display = 'none';
    document.body.appendChild(link);
    link.click();

    // Clean up
    setTimeout(() => {
      document.body.removeChild(link);
      URL.revokeObjectURL(link.href);
    }, 100);
  }

  /**
   * Utility method to format data before export
   * This can be used by specific services to transform their data
   */
  formatDataForExport<TSource, TTarget extends Record<string, any>>(
    sourceData: TSource[],
    columns: IDataGridFields[],
    formatter: (item: TSource, columns: IDataGridFields[]) => TTarget
  ): TTarget[] {
    return sourceData.map(item => formatter(item, columns));
  }
}
