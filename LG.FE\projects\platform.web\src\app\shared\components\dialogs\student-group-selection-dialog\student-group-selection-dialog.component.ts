import { CommonModule } from '@angular/common';
import {
  ChangeDetectionStrategy,
  Component,
  computed,
  DestroyRef,
  Inject,
  inject,
  Injector,
  OnDestroy,
  type OnInit,
  signal,
  ViewChild
} from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { ButtonModule } from 'primeng/button';
import { TooltipModule } from 'primeng/tooltip';

import { ActionableAlertComponent, CustomDialogPopupComponent, DefaultGetStudentsRequest, GroupDialogState, IApiResponseBase, ICreateStudentGroupResponse, State } from 'SharedModules.Library';
import {
  FormFieldValidationMessageComponent,
  IUserRole
} from 'SharedModules.Library';

import { EmitEvent, EventBusService, Events } from 'SharedModules.Library';
import { Subscription } from 'rxjs';
import { DataApiStateService } from 'SharedModules.Library';
import { GeneralService } from 'SharedModules.Library';
import { UserService } from '@platform.src/app/core/services/user.service';

import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { HandleApiResponseService } from 'SharedModules.Library';
import { StoudentGroupRuleMessage, StudentGroupService } from '@platform.src/app/core/services/student-group.service';
import { Severity } from 'SharedModules.Library';
import { AccordionModule } from 'primeng/accordion';

import { CarouselModule } from 'primeng/carousel';
import { CarouselService } from '@platform.src/app/core/services/carousel.service';
import {
  StudentGroupSelectionSuggestionTextStepComponent
} from './student-group-selection-suggestion-text-step/student-group-selection-suggestion-text-step.component';

import {
  StudentGroupSelectionMembersStepComponent
} from './student-group-selection-members-step/student-group-selection-members-step.component';
import {
  IAvailability,
  IEditAvailabilityRequest,
  IGetAvailabilityResponse,
  IGetStudentGroupResponse,
  IStudentGroupDto,
  ITeachingLanguageDto,
  StudentGroupRoutes
} from 'SharedModules.Library';
import { toObservable } from '@angular/core/rxjs-interop';
import {
  StudentGroupSelectionAvailabilityStepComponent
} from './student-group-selection-availability-step/student-group-selection-availability-step.component';
import {
  StudentGroupSelectionSuccessActionStepComponent
} from './student-group-selection-success-action-step/student-group-selection-success-action-step.component';
import { AuthStateService } from 'SharedModules.Library';
import { ToastMessages, ToastService } from 'SharedModules.Library';
import { Router, RouterModule } from '@angular/router';
import { ParentOverviewGroupCreateFormComponent } from '@platform.app/features/roles/parent/parent-profile/parent-overview-groups/parent-overview-groups-create/components/parent-overview-group-create-form/parent-overview-group-create-form.component';
import { HttpErrorResponse } from '@angular/common/http';

@Component({
  selector: 'app-student-group-selection-dialog',
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    RouterModule,
    ButtonModule,
    TooltipModule,
    AccordionModule,
    CarouselModule,
    CustomDialogPopupComponent,
    FormFieldValidationMessageComponent,
    StudentGroupSelectionSuggestionTextStepComponent,
    StudentGroupSelectionAvailabilityStepComponent,
    StudentGroupSelectionMembersStepComponent,
    StudentGroupSelectionSuccessActionStepComponent,
    ActionableAlertComponent,
    ParentOverviewGroupCreateFormComponent,
  ],
  templateUrl: './student-group-selection-dialog.component.html',
  styleUrl: './student-group-selection-dialog.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [
    CarouselService,
    StudentGroupService,
  ]
})
export class StudentGroupSelectionDialogComponent implements OnInit, OnDestroy {
  // Constants
  private readonly DIALOG_DEFAULT_STYLE = { width: '650px' };
  private readonly DIALOG_DEFAULT_CLASS = 'p-dialog p-dialog-md purple-dialog-header flex justify-content-center';

  // Dialog state signals
  readonly EditGroupState = GroupDialogState;
  dialogVisible = signal(true);
  dialogStyleClass = signal(this.DIALOG_DEFAULT_CLASS);
  dialogStyle = signal(this.DIALOG_DEFAULT_STYLE);
  dialogHeader = signal('Create a Student Group');

  @ViewChild(ParentOverviewGroupCreateFormComponent)
  formComponent!: ParentOverviewGroupCreateFormComponent;

  // Group state signals
  editMode = signal(false);
  step = signal(1);
  studentGroupItem = signal({} as IStudentGroupDto);
  editMembersMode = signal(false);
  editGroupState = signal(this.EditGroupState.CreateGroupSuggestionStep);
  canEditGroup = signal(false);
  deleteGroupContent = signal(false);
  newGroupMemberContent = signal(false);

  // Selection state signals
  selectedTeachingLanguage$ = signal<ITeachingLanguageDto | null>(null);
  resetSelectionSignal = signal(false);
  ruleMessageDisplay = signal([] as StoudentGroupRuleMessage[]);
  availabilityStepValidity = signal(false);
  availabilityDetailsFormValidity = signal(false);

  // Injected services
  readonly services = {
    general: inject(GeneralService),
    auth: inject(AuthStateService),
    user: inject(UserService),
    api: inject(HandleApiResponseService),
    studentGroup: inject(StudentGroupService),
    dataState: inject(DataApiStateService),
    toast: inject(ToastService),
    eventBus: inject(EventBusService),
    carousel: inject(CarouselService),
    router: inject(Router)
  };

  // Computed signals
  dialogStyle$ = computed(() => this.dialogStyle());
  user = computed(() => this.services.auth.getUserClaims());
  userBasicInfo = computed(() => this.services.auth.getUserBasicInfo());
  dialogHeader$ = computed(() => this.computeDialogHeader());
  canGoBack$ = computed(() => this.computeCanGoBack());
  studentGroups$ = computed(() => this.services.dataState.parentStudentsGroups.state());
  createStudentGroup$ = computed(() => this.services.dataState.createStudentGroup.state() || []);
  students$ = computed(() => this.services.dataState.parentStudents.state());

  // Other properties
  private readonly destroy: DestroyRef = inject(DestroyRef);
  private subscriptions: Subscription[] = [];
  #userToSignal = this.services.auth.userDecodedJWTData$;
  readonly Severity = Severity;
  private readonly injector = inject(Injector);


  parameters: any;
  times: { time: string, flag: any, checked: boolean, day: string }[] = [];
  selectedFlags: number | undefined | any = undefined;
  preselectedStudents = [] as unknown[];
  studentGroups = [] as IStudentGroupDto[];

  constructor(@Inject('dialogParameters') parameters: any) {
    this.parameters = parameters;
  }

  ngOnInit(): void {
    this.initializeComponent();
    this.setupSubscriptions();
    // this.getStudentGroup('347c8407-833f-45ae-00c6-08dda7e0af7a');
    // this.editGroupState.set(this.EditGroupState.DeleteGroup);
    // console.log('console', this.studentGroupItem());
    // this.deleteGroupStepSelected();
    // this.editMode.set(true);
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach(sub => sub.unsubscribe());
    this.services.general.setErrorDataSignal('');
    // HTTP calls and observable subscriptions are automatically cleaned up by takeUntilDestroyed
  }

  // Computed getters
  get isEditModeAndDeleteGroupMode(): boolean {
    return this.editMode() && this.editGroupState() === this.EditGroupState.DeleteGroup;
  }

  get isEditModeOrAfterEdit(): boolean {
    return (this.editMode() && this.editGroupState() === this.EditGroupState.None) ||
      this.editGroupState() === this.EditGroupState.AfterEditSuccess ||
      this.editGroupState() === this.EditGroupState.EditGroup;
  }

  get isEditAvailability(): boolean {
    return this.editMode() && this.editGroupState() === this.EditGroupState.EditGroup;
  }

  // Event handlers
  onGroupStateChanged(state: GroupDialogState): void {
    this.editGroupState.set(state);
    this.editMode.set(state !== GroupDialogState.CreateGroup);

    if (state === GroupDialogState.AfterEditSuccess) {
      this.dialogVisible.set(false);
    }
  }

  onGroupFormValidityChanged(valid: boolean): void {
    console.log('aa', valid);
    this.availabilityDetailsFormValidity.set(valid);
  }

  onGroupItemChanged(item: any): void {
    this.studentGroupItem.set(item);
  }

  onSuggestionTextButtonSelected(_event: any): void {
    this.step.set(2);
    this.editGroupState.set(this.EditGroupState.CreateGroup);
  }

  onAvailabilityStepSubmitted(): void {
      this.formComponent.onSubmit();
    if (this.isEditAvailability) {
      // this.saveGroupAvailability(this.studentGroupItem().availabilityId!);
    } else {
      // this.createNewGroup();
    }
  }

  onAvailabilityStepValidityChanged(event: boolean): void {
    this.availabilityStepValidity.set(event);
  }

  onAvailabilityStepValuesChanged(_event: any): void {
    // console.log(this.services.studentGroup.getStudentGroupRequest());
  }

  membersActionCompleted(_event: any): void {
    this.editGroupState.set(this.EditGroupState.None);
  }

  deleteGroupStepSelected(): void {
    this.deleteGroupContent.set(true);
    this.editGroupState.set(this.EditGroupState.DeleteGroup);
  }

  deleteGroupStepActionSelected(action: string): void {
    if (action === 'yes') {
      this.deleteGroupContent.set(true);
      this.deleteStudentGroup();
    } else if (action === 'no') {
      this.deleteGroupContent.set(false);
    }
  }

  backToViewGroup(): void {
    if (this.isEditModeAndDeleteGroupMode) {
      this.resetEditGroupState();
    } else if (this.isEditModeOrAfterEdit) {
      this.editGroupState.set(this.EditGroupState.AfterEditSuccess);
    } else {
      this.editGroupState.set(this.EditGroupState.ViewGroup);
      this.resetContentFlags();
    }
    this.services.general.setErrorDataSignal('');
  }

  onAvailabilityDetailsFormValid($event: boolean) {
    this.availabilityDetailsFormValidity.set($event);
  }

  onGoBackToViewGroups(): void {
    this.dialogVisible.set(false);
    this.services.router.navigate(['/dashboard/parent/groups/list']);
  }

  // Private methods
  private initializeComponent(): void {
    this.setInitialState();
    this.initEvents();

    // this.services.eventBus.emit(new EmitEvent(Events.StateLoadParentStudents, new DefaultGetStudentsRequest()));
  }

  private setInitialState(): void {
    this.editMode.set(this.parameters.editMode);
    this.studentGroupItem.set(this.parameters.studentGroupItem);
    if (this.parameters.state) {
      this.editGroupState.set(this.parameters.state);
    }
    this.dialogStyleClass.set(this.DIALOG_DEFAULT_CLASS);
    this.dialogStyle.set(this.DIALOG_DEFAULT_STYLE);

    if (this.editMode()) {
      this.editGroupState.set(this.EditGroupState.EditGroup);
    }

    if (this.studentGroupItem()) {
      this.preselectedStudents = this.studentGroupItem().studentInfo!;
    }

    if (this.parameters.groupId) {
      this.getStudentGroup(this.parameters.groupId);
    }

    // if (this.isEditModeOrAfterEdit) {
    //   this.getStudentGroup(this.studentGroupItem().id);
    // }
  }

  private initEvents(): void {
    // this.services.eventBus.emit(new EmitEvent(Events.StateLoadTeachingLanguages, undefined));
    this.subscribeToEvents();
  }

  private resetEditGroupState(): void {
    this.editGroupState.set(this.EditGroupState.ViewGroup);
    this.resetContentFlags();
  }

  private resetContentFlags(): void {
    this.editMembersMode.set(false);
    this.deleteGroupContent.set(false);
    this.newGroupMemberContent.set(false);
  }

  private computeDialogHeader(): string {

    const headerMap = {
      [this.EditGroupState.None]: 'Edit Group',
      [this.EditGroupState.CreateGroup]: 'Create your Group Class',
      [this.EditGroupState.DeleteGroup]: 'Delete Group',
      [this.EditGroupState.EditGroup]: 'Edit Group Overview',
      [this.EditGroupState.AfterCreateSuccess]: 'Group successfully created',
      [this.EditGroupState.AfterEditSuccess]: 'Edit Student Group',
      [this.EditGroupState.CreateGroupSuggestionStep]: 'Create Your Group Class in 3 Easy Steps!',
      [this.EditGroupState.ViewGroup]: 'Group Overview',

    };

    return headerMap[this.editGroupState() as keyof typeof headerMap] || 'Create a Student Group';
  }

  private computeCanGoBack(): boolean {
    return (
        this.editGroupState() !== this.EditGroupState.None && 
      this.editGroupState() !== this.EditGroupState.ViewGroup &&
        this.editGroupState() !== this.EditGroupState.AfterEditSuccess);
  }

  // API calls

  private deleteStudentGroup(): void {
    this.services.api.getApiData<IGetStudentGroupResponse>(
      { url: StudentGroupRoutes.deleteStudentGroup, method: 'DELETE' },
      {
        parentId: this.user()?.id,
        groupId: this.studentGroupItem().id
      }, false
    ).pipe(takeUntilDestroyed(this.destroy)).subscribe({
      next: this.handleDeleteGroupSuccess,
      error: this.handleApiError
    });
  }

  private getStudentGroup(id: string): void {
    this.services.api.getApiData<IGetStudentGroupResponse>(
      { url: StudentGroupRoutes.getStudentGroup, method: 'GET' },
      { GroupId: id }, false
    ).pipe(takeUntilDestroyed(this.destroy)).subscribe({
      next: this.handleGetGroupSuccess,
      error: this.handleApiError
    });
  }

  // Success handlers=

  private handleDeleteGroupSuccess = (_response: IGetStudentGroupResponse): void => {
    this.services.toast.show(ToastMessages.StudentsGroupDelete.success);
    this.dialogVisible.set(false);
    this.services.eventBus.emit(new EmitEvent(Events.StateLoadParentStudentsGroups, undefined as any));
    this.services.eventBus.emit(new EmitEvent(Events.StateLoadParentDashboard, {
      role: this.services.auth.getUserClaims().role as IUserRole,
    }));
  }

  private handleGetGroupSuccess = (response: IGetStudentGroupResponse): void => {
    this.studentGroupItem.set(response.studentGroup);
  }


  // Error handlers
  private handleApiError = (error: HttpErrorResponse | IApiResponseBase): void => {
    console.error('API Error:', error);
    this.services.general.handleRouteError(error as IApiResponseBase, StudentGroupRoutes.postCreateStudentGroup);
  }

  // Subscription setup
  private setupSubscriptions(): void {
    this.subscribeToStudentGroups();
  }

  private subscribeToEvents(): void {
    const subscriptions = [
      this.services.eventBus.on(Events.StudentGroupAdded, this.handleStudentGroupAdded),
      this.services.eventBus.on(Events.StudentGroupRemoved, this.handleStudentGroupRemoved),
      this.services.eventBus.on(Events.StudentGroupNewMemberAdded, this.handleStudentGroupNewMemberAdded)
    ];
    this.subscriptions.push(...subscriptions);
  }

  private subscribeToStudentGroups(): void {
    toObservable(this.studentGroups$, { injector: this.injector })
      .pipe(takeUntilDestroyed(this.destroy))
      .subscribe({
        next: (stateData: State<IGetStudentGroupResponse>) => {
          if (stateData.data?.studentGroups && this.editMode()) {
            // Handle student groups data if needed
          }
        }
      });
    toObservable(this.editGroupState, { injector: this.injector })
      .pipe(takeUntilDestroyed(this.destroy))
      .subscribe({
        next: (state: GroupDialogState) => {
          if (state === this.EditGroupState.EditGroup) {
            this.getStudentGroup(this.studentGroupItem().id);
            // Handle student groups data if needed
          }
        }
      });
  }

  // Event handlers
  private handleStudentGroupAdded = (payload: any): void => {
    console.log(`Student Selected: ${payload.name}`);
  }

  private handleStudentGroupRemoved = (payload: any): void => {
    console.log(Events.StudentGroupRemoved, `: ${payload}`);
  }

  private handleStudentGroupNewMemberAdded = (payload: any): void => {
    console.log(Events.StudentGroupNewMemberAdded, `: ${payload}`);
  }

  // Helper methods
  private createEditRequest(availabilityId: string): IEditAvailabilityRequest {
    return this.services.studentGroup.getEditStudentGroupRequest();
  }

  onGroupCreated(event: ICreateStudentGroupResponse): void {
    this.editGroupState.set(this.EditGroupState.AfterCreateSuccess);
    this.getStudentGroup(event.studentGroup.id);
  }

  // ViewGroup State Methods

  /**
   * Get display members (limit to 3 for compact UI)
   */
  getDisplayMembers(): any[] {
    const members = this.studentGroupItem().studentInfo || [];
    return members.slice(0, 3); // Show max 3 members for compact view
  }

  /**
   * Get group statistics for compact progress display
   */


  /**
   * Get next upcoming lesson for compact display
   */
  getNextLesson(): { date: Date; time: string } | null {
    // Mock data - replace with actual API data when available
    const nextWeek = new Date();
    nextWeek.setDate(nextWeek.getDate() + 5);

    return {
      date: nextWeek,
      time: '2:00 PM'
    };
  }

  /**
   * Navigate to lessons view
   */
  navigateToLessons(): void {
    this.dialogVisible.set(false);
    this.services.router.navigate(['/dashboard/parent/lessons'], {
      queryParams: { groupId: this.studentGroupItem().id }
    });
  }

  /**
   * Navigate to ViewGroup state
   */
  navigateToViewGroup(): void {
    this.editGroupState.set(this.EditGroupState.ViewGroup);
  }

  /**
   * Handle Edit Group button click from ViewGroup mode
   * Transitions to edit mode for group availability/schedule
   */
  onEditGroupClicked(): void {
    this.editMode.set(true);
    this.editGroupState.set(this.EditGroupState.EditGroup);
  }

  /**
   * Handle Delete Group button click from ViewGroup mode
   * Transitions to delete confirmation mode
   */
  onDeleteGroupClicked(): void {
    this.editMode.set(true);
    this.deleteGroupStepSelected();
  }
}
