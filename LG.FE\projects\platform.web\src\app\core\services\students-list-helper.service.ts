import { Injectable, inject } from '@angular/core';
import { Params } from '@angular/router';
import {
  IGetStudentsRequest,
  IGetStudentsResponse,
  ISearchStudentDto,
  IGenderEnum,
  nameOf
} from 'SharedModules.Library';

/**
 * Helper service for students list component
 * Provides utilities for request mapping, defaults, and query parameter handling
 * Following the same pattern as TeacherListComponentHelperService
 */
@Injectable({
  providedIn: 'root'
})
export class StudentsListHelperService {
  
  // Default configuration constants
  public static readonly DEFAULT_PAGE_SIZE = 10;
  public static readonly DEFAULT_PAGE_NUMBER = 1;
  public static readonly DEFAULT_SORT_COLUMN = 'firstName';
  public static readonly DEFAULT_SORT_DIRECTION = 'asc';
  public static readonly DEFAULT_STUDENT_AGES_MIN = 0;
  public static readonly DEFAULT_STUDENT_AGES_MAX = 99;

  constructor() {}

  /**
   * Creates a default IGetStudentsRequest object with standard values for backend pagination
   * @returns IGetStudentsRequest with default values optimized for pagination
   */
  createDefaultStudentsRequestForParent(): IGetStudentsRequest {
    return {
      pageNumber: StudentsListHelperService.DEFAULT_PAGE_NUMBER,
      pageSize: StudentsListHelperService.DEFAULT_PAGE_SIZE,
      sortColumn: null,
      sortDirection: null,
      searchTerm: null,
      gender: IGenderEnum.None,
      registeredFrom: null,
      registeredTo: null,
      teachingLanguage: null,
      teachingLanguageLevel: null,
      speakingLanguage: null,
      isNativeSpeakingLanguage: null,
      includeBlocked: null,
      accountStatus: null,
      studentAgesMin: StudentsListHelperService.DEFAULT_STUDENT_AGES_MIN,
      studentAgesMax: StudentsListHelperService.DEFAULT_STUDENT_AGES_MAX,
      teacherId: null,
      parentId: null
    };
  }

  /**
   * Maps URL query parameters to an IGetStudentsRequest object
   * @param params Route parameters from ActivatedRoute
   * @param defaultRequest Optional default request to use as base
   * @returns IGetStudentsRequest populated with values from URL parameters
   */
  mapQueryParamsToStudentsRequest(params: Params, defaultRequest?: IGetStudentsRequest): IGetStudentsRequest {
    // Start with clean defaults or use provided default
    const request: IGetStudentsRequest = defaultRequest || this.createDefaultStudentsRequestForParent();

    // Use nameOf to get type-safe property names
    const paramsMap = nameOf<IGetStudentsRequest>();

    // Map pagination parameters
    if (params[paramsMap.pageNumber] !== undefined) {
      request.pageNumber = Math.max(1, +params[paramsMap.pageNumber] || 1);
    }
    if (params[paramsMap.pageSize] !== undefined) {
      request.pageSize = Math.max(1, +params[paramsMap.pageSize] || StudentsListHelperService.DEFAULT_PAGE_SIZE);
    }

    // Map sorting parameters
    if (params[paramsMap.sortColumn!] !== undefined && params[paramsMap.sortColumn!] !== 'null') {
      request.sortColumn = params[paramsMap.sortColumn!];
    }
    if (params[paramsMap.sortDirection!] !== undefined && params[paramsMap.sortDirection!] !== 'null') {
      request.sortDirection = params[paramsMap.sortDirection!];
    }

    // Map search parameter
    if (params[paramsMap.searchTerm!] !== undefined && params[paramsMap.searchTerm!] !== 'null') {
      request.searchTerm = params[paramsMap.searchTerm!];
    }

    // Map filter parameters
    if (params[paramsMap.gender] !== undefined && params[paramsMap.gender] !== 'null') {
      request.gender = +params[paramsMap.gender];
    }

    // Map age range parameters
    if (params[paramsMap.studentAgesMin] !== undefined) {
      request.studentAgesMin = +params[paramsMap.studentAgesMin];
    }
    if (params[paramsMap.studentAgesMax] !== undefined) {
      request.studentAgesMax = +params[paramsMap.studentAgesMax];
    }

    // Map date range parameters
    if (params[paramsMap.registeredFrom!] !== undefined && params[paramsMap.registeredFrom!] !== 'null') {
      request.registeredFrom = new Date(params[paramsMap.registeredFrom!]);
    }
    if (params[paramsMap.registeredTo!] !== undefined && params[paramsMap.registeredTo!] !== 'null') {
      request.registeredTo = new Date(params[paramsMap.registeredTo!]);
    }

    // Map language parameters
    if (params[paramsMap.teachingLanguage!] !== undefined && params[paramsMap.teachingLanguage!] !== 'null') {
      request.teachingLanguage = params[paramsMap.teachingLanguage!];
    }
    if (params[paramsMap.speakingLanguage!] !== undefined && params[paramsMap.speakingLanguage!] !== 'null') {
      request.speakingLanguage = params[paramsMap.speakingLanguage!];
    }

    // Map boolean parameters
    if (params[paramsMap.includeBlocked!] !== undefined) {
      request.includeBlocked = params[paramsMap.includeBlocked!] === 'true';
    }
    if (params[paramsMap.isNativeSpeakingLanguage!] !== undefined) {
      request.isNativeSpeakingLanguage = params[paramsMap.isNativeSpeakingLanguage!] === 'true';
    }

    // Map enum parameters
    if (params[paramsMap.teachingLanguageLevel!] !== undefined && params[paramsMap.teachingLanguageLevel!] !== 'null') {
      request.teachingLanguageLevel = +params[paramsMap.teachingLanguageLevel!];
    }
    if (params[paramsMap.accountStatus!] !== undefined && params[paramsMap.accountStatus!] !== 'null') {
      request.accountStatus = +params[paramsMap.accountStatus!];
    }

    // Map ID parameters
    if (params[paramsMap.teacherId!] !== undefined && params[paramsMap.teacherId!] !== 'null') {
      request.teacherId = params[paramsMap.teacherId!];
    }
    if (params[paramsMap.parentId!] !== undefined && params[paramsMap.parentId!] !== 'null') {
      request.parentId = params[paramsMap.parentId!];
    }

    return request;
  }

  /**
   * Gets the default sort column
   */
  getDefaultSortColumn(): string {
    return StudentsListHelperService.DEFAULT_SORT_COLUMN;
  }

  /**
   * Gets the default sort direction
   */
  getDefaultSortDirection(): string {
    return StudentsListHelperService.DEFAULT_SORT_DIRECTION;
  }

  /**
   * Gets the default page size
   */
  getDefaultPageSize(): number {
    return StudentsListHelperService.DEFAULT_PAGE_SIZE;
  }
}
