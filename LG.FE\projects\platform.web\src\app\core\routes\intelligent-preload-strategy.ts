import { PreloadingStrategy, Route } from '@angular/router';
import { Injectable, inject } from '@angular/core';
import { Observable, EMPTY, timer, of, fromEvent, merge } from 'rxjs';
import { switchMap, catchError, debounceTime, distinctUntilChanged, filter } from 'rxjs/operators';
import { AuthStateService } from 'SharedModules.Library';

/**
 * Advanced intelligent preloading strategy with ML-based prediction and user behavior analysis
 */
@Injectable({ providedIn: 'root' })
export class IntelligentPreloadingStrategy implements PreloadingStrategy {
  private authService = inject(AuthStateService);
  
  // Core tracking
  private preloadedRoutes = new Set<string>();
  private preloadQueue: PreloadQueueItem[] = [];
  private isPreloading = false;
  private userBehavior = new UserBehaviorTracker();
  private networkMonitor = new NetworkMonitor();
  private routePredictor = new RoutePredictor();
  
  // Performance metrics
  private preloadStats = {
    successful: 0,
    failed: 0,
    totalTime: 0,
    cacheHits: 0
  };

  constructor() {
    this.initializeStrategy();
  }

  preload(route: Route, load: () => Observable<any>): Observable<any> {
    const routePath = route.path || '';
    
    // Skip if already preloaded
    if (this.preloadedRoutes.has(routePath)) {
      this.preloadStats.cacheHits++;
      return EMPTY;
    }

    // Skip auth routes and excluded paths
    if (this.shouldSkipRoute(routePath)) {
      return EMPTY;
    }

    // Check network and device conditions
    if (!this.networkMonitor.shouldPreload()) {
      return EMPTY;
    }

    const priority = this.calculateAdvancedPriority(route);
    const prediction = this.routePredictor.getPredictionScore(routePath);
    
    // Combine priority with ML prediction
    const finalScore = this.combinePriorityAndPrediction(priority, prediction);
    
    // Immediate preload for very high scores
    if (finalScore >= 9) {
      return this.preloadWithAnalytics(route, load, routePath, 'immediate');
    }

    // Queue for intelligent processing
    this.addToIntelligentQueue(route, load, finalScore, prediction);
    this.processIntelligentQueue();
    
    return EMPTY;
  }

  private initializeStrategy(): void {
    // Track user interactions for behavior analysis
    this.trackUserInteractions();
    
    // Monitor network changes
    this.networkMonitor.startMonitoring();
    
    // Initialize route predictor with historical data
    this.routePredictor.initialize();
    
    // Preload based on user patterns every 30 seconds
    timer(30000, 30000).subscribe(() => {
      this.proactivePreload();
    });
  }

  private calculateAdvancedPriority(route: Route): number {
    let priority = 0;
    const routePath = route.path || '';
    const userRole = this.authService.getUserRole();
    const currentTime = new Date().getHours();
    
    // Base priority from route configuration
    if (route.data?.['preload'] === true) priority += 5;
    if (route.data?.['priority']) priority += route.data['priority'];
    
    // Role-based priority
    priority += this.getRolePriority(routePath, userRole);
    
    // Time-based priority (business hours boost)
    if (currentTime >= 8 && currentTime <= 18) {
      if (routePath.includes('dashboard') || routePath.includes('lessons')) {
        priority += 2;
      }
    }
    
    // User behavior priority
    priority += this.userBehavior.getRoutePriority(routePath);
    
    // Frequency-based priority
    priority += this.getFrequencyPriority(routePath);
    
    return Math.min(priority, 10); // Cap at 10
  }

  private getRolePriority(routePath: string, userRole: string): number {
    const rolePriorities: Record<string, Record<string, number>> = {
      'PARENT': {
        'dashboard': 4,
        'student': 3,
        'buy-package': 2,
        'lessons': 3
      },
      'STUDENT': {
        'dashboard': 4,
        'lessons': 4,
        'library': 2,
        'progress': 2
      },
      'TEACHER': {
        'dashboard': 4,
        'lessons': 4,
        'students': 3,
        'calendar': 3
      }
    };

    const priorities = rolePriorities[userRole] || {};
    for (const [path, priority] of Object.entries(priorities)) {
      if (routePath.includes(path)) {
        return priority;
      }
    }
    return 0;
  }

  private getFrequencyPriority(routePath: string): number {
    const frequentRoutes = [
      'dashboard', 'overview', 'lessons', 'calendar', 
      'notifications', 'student', 'parent'
    ];
    
    return frequentRoutes.some(freq => routePath.includes(freq)) ? 2 : 0;
  }

  private combinePriorityAndPrediction(priority: number, prediction: number): number {
    // Weighted combination: 60% priority, 40% ML prediction
    return (priority * 0.6) + (prediction * 0.4);
  }

  private shouldSkipRoute(path: string): boolean {
    const skipPaths = [
      'auth', 'login', 'register', 'signin', 'checkout', 
      'payment', 'error', '404', 'maintenance'
    ];
    return skipPaths.some(skip => path.includes(skip));
  }

  private addToIntelligentQueue(
    route: Route, 
    load: () => Observable<any>, 
    score: number, 
    prediction: number
  ): void {
    const queueItem: PreloadQueueItem = {
      route,
      load,
      score,
      prediction,
      timestamp: Date.now(),
      retries: 0
    };
    
    this.preloadQueue.push(queueItem);
    this.preloadQueue.sort((a, b) => b.score - a.score);
    
    // Limit queue size
    if (this.preloadQueue.length > 20) {
      this.preloadQueue = this.preloadQueue.slice(0, 20);
    }
  }

  private processIntelligentQueue(): void {
    if (this.isPreloading || this.preloadQueue.length === 0) return;
    
    this.isPreloading = true;
    
    const processNext = () => {
      if (this.preloadQueue.length === 0) {
        this.isPreloading = false;
        return;
      }
      
      // Check if we should continue based on current conditions
      if (!this.networkMonitor.shouldPreload()) {
        this.isPreloading = false;
        return;
      }
      
      const item = this.preloadQueue.shift()!;
      const routePath = item.route.path || '';
      
      this.preloadWithAnalytics(item.route, item.load, routePath, 'queued')
        .subscribe({
          complete: () => {
            // Adaptive delay based on network speed and CPU usage
            const delay = this.calculateAdaptiveDelay();
            setTimeout(processNext, delay);
          },
          error: () => {
            // Retry logic for failed preloads
            if (item.retries < 2) {
              item.retries++;
              this.preloadQueue.unshift(item);
            }
            setTimeout(processNext, 1000);
          }
        });
    };
    
    // Start processing with initial delay
    setTimeout(processNext, this.calculateInitialDelay());
  }

  private preloadWithAnalytics(
    route: Route, 
    load: () => Observable<any>, 
    routePath: string,
    type: 'immediate' | 'queued' | 'proactive'
  ): Observable<any> {
    const startTime = performance.now();
    
    return load().pipe(
      catchError(error => {
        this.preloadStats.failed++;
        console.warn(`❌ Preload failed (${type}): ${routePath}`, error);
        return EMPTY;
      })
    ).pipe(
      switchMap(result => {
        const loadTime = performance.now() - startTime;
        this.preloadStats.successful++;
        this.preloadStats.totalTime += loadTime;
        
        this.preloadedRoutes.add(routePath);
        this.userBehavior.recordPreload(routePath, loadTime, type);
        
        console.log(`✅ Preloaded (${type}): ${routePath} in ${loadTime.toFixed(2)}ms`);
        return of(result);
      })
    );
  }

  private trackUserInteractions(): void {
    // Track mouse movements for hover predictions
    fromEvent(document, 'mouseover').pipe(
      debounceTime(100),
      filter(event => {
        const target = event.target as HTMLElement;
        return target.tagName === 'A' || target.closest('a') !== null;
      })
    ).subscribe(event => {
      const link = (event.target as HTMLElement).closest('a');
      if (link?.href) {
        this.userBehavior.recordHover(link.href);
      }
    });

    // Track scroll patterns
    fromEvent(window, 'scroll').pipe(
      debounceTime(200)
    ).subscribe(() => {
      this.userBehavior.recordScroll();
    });
  }

  private proactivePreload(): void {
    const predictions = this.routePredictor.getTopPredictions(3);
    
    predictions.forEach(prediction => {
      if (!this.preloadedRoutes.has(prediction.route) && 
          prediction.confidence > 0.7) {
        // Find route configuration and trigger preload
        this.triggerProactivePreload(prediction.route);
      }
    });
  }

  private triggerProactivePreload(routePath: string): void {
    // Implementation would depend on your routing structure
    console.log(`🔮 Proactive preload triggered for: ${routePath}`);
  }

  private calculateAdaptiveDelay(): number {
    const baseDelay = 200;
    const networkSpeed = this.networkMonitor.getEffectiveSpeed();
    const cpuUsage = this.estimateCpuUsage();
    
    // Adjust delay based on conditions
    let multiplier = 1;
    if (networkSpeed === 'slow') multiplier *= 2;
    if (cpuUsage > 0.8) multiplier *= 1.5;
    
    return baseDelay * multiplier;
  }

  private calculateInitialDelay(): number {
    // Delay initial processing to let critical navigation complete
    return this.networkMonitor.getEffectiveSpeed() === 'fast' ? 500 : 1000;
  }

  private estimateCpuUsage(): number {
    // Simple CPU usage estimation based on frame timing
    const start = performance.now();
    for (let i = 0; i < 10000; i++) {
      Math.random();
    }
    const duration = performance.now() - start;
    
    // Normalize to 0-1 scale (higher = more CPU usage)
    return Math.min(duration / 10, 1);
  }

  /**
   * Get comprehensive preloading statistics
   */
  getAdvancedStats(): PreloadingStats {
    return {
      ...this.preloadStats,
      queueSize: this.preloadQueue.length,
      preloadedCount: this.preloadedRoutes.size,
      averageLoadTime: this.preloadStats.totalTime / Math.max(this.preloadStats.successful, 1),
      successRate: this.preloadStats.successful / Math.max(this.preloadStats.successful + this.preloadStats.failed, 1),
      userBehaviorInsights: this.userBehavior.getInsights(),
      networkConditions: this.networkMonitor.getCurrentConditions(),
      topPredictions: this.routePredictor.getTopPredictions(5)
    };
  }

  /**
   * Manual preload trigger for hover events
   */
  triggerHoverPreload(routePath: string): void {
    this.userBehavior.recordHover(routePath);
    // Implementation would trigger immediate preload if conditions are met
  }

  /**
   * Clear all preloaded routes and reset statistics
   */
  reset(): void {
    this.preloadedRoutes.clear();
    this.preloadQueue = [];
    this.preloadStats = { successful: 0, failed: 0, totalTime: 0, cacheHits: 0 };
    this.userBehavior.reset();
  }
}

// Supporting interfaces and classes
interface PreloadQueueItem {
  route: Route;
  load: () => Observable<any>;
  score: number;
  prediction: number;
  timestamp: number;
  retries: number;
}

interface PreloadingStats {
  successful: number;
  failed: number;
  totalTime: number;
  cacheHits: number;
  queueSize: number;
  preloadedCount: number;
  averageLoadTime: number;
  successRate: number;
  userBehaviorInsights: any;
  networkConditions: any;
  topPredictions: any[];
}

/**
 * Tracks user behavior patterns to improve preloading predictions
 */
class UserBehaviorTracker {
  private routeVisits = new Map<string, RouteVisitData>();
  private hoverEvents = new Map<string, number>();
  private scrollPatterns: ScrollPattern[] = [];
  private sessionStartTime = Date.now();

  recordVisit(route: string, loadTime: number): void {
    const existing = this.routeVisits.get(route) || {
      count: 0,
      totalTime: 0,
      lastVisit: 0,
      averageTime: 0,
      timeOfDay: []
    };

    existing.count++;
    existing.totalTime += loadTime;
    existing.lastVisit = Date.now();
    existing.averageTime = existing.totalTime / existing.count;
    existing.timeOfDay.push(new Date().getHours());

    this.routeVisits.set(route, existing);
    this.persistBehaviorData();
  }

  recordHover(route: string): void {
    const current = this.hoverEvents.get(route) || 0;
    this.hoverEvents.set(route, current + 1);
  }

  recordScroll(): void {
    this.scrollPatterns.push({
      timestamp: Date.now(),
      scrollY: window.scrollY,
      direction: this.getScrollDirection()
    });

    // Keep only recent scroll data
    if (this.scrollPatterns.length > 100) {
      this.scrollPatterns = this.scrollPatterns.slice(-50);
    }
  }

  recordPreload(route: string, loadTime: number, _type: string): void {
    // Track preload effectiveness
    const key = `preload_${route}`;
    const existing = this.routeVisits.get(key) || {
      count: 0,
      totalTime: 0,
      lastVisit: 0,
      averageTime: 0,
      timeOfDay: []
    };

    existing.count++;
    existing.totalTime += loadTime;
    existing.averageTime = existing.totalTime / existing.count;

    this.routeVisits.set(key, existing);
  }

  getRoutePriority(route: string): number {
    const visitData = this.routeVisits.get(route);
    if (!visitData) return 0;

    let priority = 0;

    // Frequency boost
    if (visitData.count > 5) priority += 2;
    else if (visitData.count > 2) priority += 1;

    // Recent visit boost
    const timeSinceLastVisit = Date.now() - visitData.lastVisit;
    if (timeSinceLastVisit < 3600000) priority += 2; // 1 hour
    else if (timeSinceLastVisit < 86400000) priority += 1; // 24 hours

    // Time of day pattern boost
    const currentHour = new Date().getHours();
    const hourFrequency = visitData.timeOfDay.filter(h => h === currentHour).length;
    if (hourFrequency > 0) priority += Math.min(hourFrequency, 2);

    // Hover interest boost
    const hoverCount = this.hoverEvents.get(route) || 0;
    if (hoverCount > 0) priority += Math.min(hoverCount, 2);

    return priority;
  }

  getInsights(): UserBehaviorInsights {
    const mostVisited = Array.from(this.routeVisits.entries())
      .sort(([,a], [,b]) => b.count - a.count)
      .slice(0, 5);

    const mostHovered = Array.from(this.hoverEvents.entries())
      .sort(([,a], [,b]) => b - a)
      .slice(0, 3);

    return {
      sessionDuration: Date.now() - this.sessionStartTime,
      totalRouteVisits: Array.from(this.routeVisits.values()).reduce((sum, data) => sum + data.count, 0),
      mostVisitedRoutes: mostVisited,
      mostHoveredRoutes: mostHovered,
      scrollActivity: this.scrollPatterns.length,
      averageScrollsPerMinute: this.scrollPatterns.length / ((Date.now() - this.sessionStartTime) / 60000)
    };
  }

  private getScrollDirection(): 'up' | 'down' | 'none' {
    if (this.scrollPatterns.length < 2) return 'none';

    const current = window.scrollY;
    const previous = this.scrollPatterns[this.scrollPatterns.length - 1].scrollY;

    if (current > previous) return 'down';
    if (current < previous) return 'up';
    return 'none';
  }

  private persistBehaviorData(): void {
    try {
      const data = {
        routeVisits: Array.from(this.routeVisits.entries()),
        hoverEvents: Array.from(this.hoverEvents.entries()),
        timestamp: Date.now()
      };
      localStorage.setItem('userBehaviorData', JSON.stringify(data));
    } catch (error) {
      console.warn('Failed to persist behavior data:', error);
    }
  }

  reset(): void {
    this.routeVisits.clear();
    this.hoverEvents.clear();
    this.scrollPatterns = [];
    localStorage.removeItem('userBehaviorData');
  }
}

interface RouteVisitData {
  count: number;
  totalTime: number;
  lastVisit: number;
  averageTime: number;
  timeOfDay: number[];
}

interface ScrollPattern {
  timestamp: number;
  scrollY: number;
  direction: 'up' | 'down' | 'none';
}

interface UserBehaviorInsights {
  sessionDuration: number;
  totalRouteVisits: number;
  mostVisitedRoutes: [string, RouteVisitData][];
  mostHoveredRoutes: [string, number][];
  scrollActivity: number;
  averageScrollsPerMinute: number;
}

/**
 * Monitors network conditions and device capabilities
 */
class NetworkMonitor {
  private connection: any;
  private conditions: NetworkConditions;

  constructor() {
    this.connection = (navigator as any).connection ||
                     (navigator as any).mozConnection ||
                     (navigator as any).webkitConnection;

    this.conditions = {
      effectiveType: 'unknown',
      downlink: 0,
      rtt: 0,
      saveData: false,
      isOnline: navigator.onLine
    };
  }

  startMonitoring(): void {
    this.updateConditions();

    // Listen for network changes
    if (this.connection) {
      this.connection.addEventListener('change', () => {
        this.updateConditions();
      });
    }

    // Listen for online/offline events
    window.addEventListener('online', () => {
      this.conditions.isOnline = true;
    });

    window.addEventListener('offline', () => {
      this.conditions.isOnline = false;
    });

    // Periodic condition updates
    setInterval(() => {
      this.updateConditions();
    }, 30000);
  }

  shouldPreload(): boolean {
    if (!this.conditions.isOnline) return false;
    if (this.conditions.saveData) return false;

    // Don't preload on very slow connections
    if (this.conditions.effectiveType === 'slow-2g' ||
        this.conditions.effectiveType === '2g') {
      return false;
    }

    // Check if device is low on battery
    if (this.isLowBattery()) return false;

    // Check if device is under memory pressure
    if (this.isMemoryConstrained()) return false;

    return true;
  }

  getEffectiveSpeed(): 'fast' | 'medium' | 'slow' {
    if (!this.connection) return 'medium';

    const effectiveType = this.conditions.effectiveType;

    if (effectiveType === '4g' || effectiveType === '5g') return 'fast';
    if (effectiveType === '3g') return 'medium';
    return 'slow';
  }

  getCurrentConditions(): NetworkConditions {
    return { ...this.conditions };
  }

  private updateConditions(): void {
    if (this.connection) {
      this.conditions = {
        effectiveType: this.connection.effectiveType || 'unknown',
        downlink: this.connection.downlink || 0,
        rtt: this.connection.rtt || 0,
        saveData: this.connection.saveData || false,
        isOnline: navigator.onLine
      };
    }
  }

  private isLowBattery(): boolean {
    const battery = (navigator as any).battery;
    if (!battery) return false;

    return battery.level < 0.2 && !battery.charging;
  }

  private isMemoryConstrained(): boolean {
    const memory = (performance as any).memory;
    if (!memory) return false;

    const usedMemory = memory.usedJSHeapSize;
    const totalMemory = memory.totalJSHeapSize;
    const memoryUsage = usedMemory / totalMemory;

    return memoryUsage > 0.85; // 85% memory usage threshold
  }
}

interface NetworkConditions {
  effectiveType: string;
  downlink: number;
  rtt: number;
  saveData: boolean;
  isOnline: boolean;
}

/**
 * ML-based route prediction using user patterns and historical data
 */
class RoutePredictor {
  private routeTransitions = new Map<string, Map<string, number>>();
  private routeSequences: RouteSequence[] = [];
  private currentRoute = '';
  private sessionRoutes: string[] = [];

  initialize(): void {
    this.loadHistoricalData();
    this.startSessionTracking();
  }

  recordRouteTransition(from: string, to: string): void {
    if (!this.routeTransitions.has(from)) {
      this.routeTransitions.set(from, new Map());
    }

    const transitions = this.routeTransitions.get(from)!;
    const currentCount = transitions.get(to) || 0;
    transitions.set(to, currentCount + 1);

    this.sessionRoutes.push(to);
    this.currentRoute = to;

    // Update sequence patterns
    this.updateSequencePatterns();

    this.persistPredictionData();
  }

  getPredictionScore(route: string): number {
    if (!this.currentRoute) return 0;

    let score = 0;

    // Direct transition probability
    const transitions = this.routeTransitions.get(this.currentRoute);
    if (transitions) {
      const transitionCount = transitions.get(route) || 0;
      const totalTransitions = Array.from(transitions.values()).reduce((sum, count) => sum + count, 0);
      score += (transitionCount / Math.max(totalTransitions, 1)) * 5;
    }

    // Sequence pattern matching
    score += this.getSequenceScore(route) * 3;

    // Time-based patterns
    score += this.getTimeBasedScore(route) * 2;

    return Math.min(score, 10);
  }

  getTopPredictions(limit: number): RoutePrediction[] {
    if (!this.currentRoute) return [];

    const predictions: RoutePrediction[] = [];
    const transitions = this.routeTransitions.get(this.currentRoute);

    if (transitions) {
      for (const [route, _count] of transitions.entries()) {
        const score = this.getPredictionScore(route);
        const confidence = score / 10;

        predictions.push({
          route,
          confidence,
          score,
          reason: this.getPredictionReason(route, score)
        });
      }
    }

    return predictions
      .sort((a, b) => b.confidence - a.confidence)
      .slice(0, limit);
  }

  private updateSequencePatterns(): void {
    if (this.sessionRoutes.length < 3) return;

    const recentSequence = this.sessionRoutes.slice(-3);
    const sequenceKey = recentSequence.join('->');

    const existing = this.routeSequences.find(seq => seq.pattern === sequenceKey);
    if (existing) {
      existing.frequency++;
    } else {
      this.routeSequences.push({
        pattern: sequenceKey,
        frequency: 1,
        lastSeen: Date.now()
      });
    }

    // Keep only recent and frequent patterns
    this.routeSequences = this.routeSequences
      .filter(seq => seq.frequency > 1 || Date.now() - seq.lastSeen < 86400000)
      .slice(-100);
  }

  private getSequenceScore(route: string): number {
    if (this.sessionRoutes.length < 2) return 0;

    const recentRoutes = this.sessionRoutes.slice(-2);
    const testSequence = [...recentRoutes, route].join('->');

    const matchingSequence = this.routeSequences.find(seq =>
      seq.pattern.endsWith(testSequence.split('->').slice(-2).join('->'))
    );

    return matchingSequence ? Math.min(matchingSequence.frequency / 10, 1) : 0;
  }

  private getTimeBasedScore(route: string): number {
    // Simple time-based scoring - could be enhanced with more sophisticated ML
    const hour = new Date().getHours();

    // Business hours boost for work-related routes
    if (hour >= 9 && hour <= 17) {
      if (route.includes('dashboard') || route.includes('lessons')) {
        return 0.5;
      }
    }

    return 0;
  }

  private getPredictionReason(_route: string, score: number): string {
    if (score > 7) return 'Very likely based on user patterns';
    if (score > 5) return 'Likely based on recent behavior';
    if (score > 3) return 'Possible based on historical data';
    return 'Low confidence prediction';
  }

  private startSessionTracking(): void {
    // Track route changes in the current session
    // This would integrate with Angular Router events
  }

  private loadHistoricalData(): void {
    try {
      const data = localStorage.getItem('routePredictionData');
      if (data) {
        const parsed = JSON.parse(data);
        this.routeTransitions = new Map(parsed.transitions || []);
        this.routeSequences = parsed.sequences || [];
      }
    } catch (error) {
      console.warn('Failed to load prediction data:', error);
    }
  }

  private persistPredictionData(): void {
    try {
      const data = {
        transitions: Array.from(this.routeTransitions.entries()),
        sequences: this.routeSequences,
        timestamp: Date.now()
      };
      localStorage.setItem('routePredictionData', JSON.stringify(data));
    } catch (error) {
      console.warn('Failed to persist prediction data:', error);
    }
  }
}

interface RouteSequence {
  pattern: string;
  frequency: number;
  lastSeen: number;
}

interface RoutePrediction {
  route: string;
  confidence: number;
  score: number;
  reason: string;
}
