// Alternative Language Display Solutions CSS

// Solution 2: Dropdown Language Display
.language-proficiency-section-dropdown {
  .language-dropdown-container {
    position: relative;
    display: inline-block;

    .language-dropdown-trigger {
      display: flex;
      align-items: center;
      background: rgba(255, 255, 255, 0.9);
      border: 1px solid #e5e7eb;
      border-radius: 16px;
      padding: 0.375rem 0.75rem;
      cursor: pointer;
      transition: all 0.2s ease;
      gap: 0.5rem;
      min-width: fit-content;

      &:hover, &.expanded {
        background: rgba(99, 102, 241, 0.05);
        border-color: #6366f1;
      }

      .primary-language {
        display: flex;
        align-items: center;
        gap: 0.25rem;
        font-size: 0.75rem;

        .language-count {
          font-weight: 600;
          color: #6366f1;
        }

        .languages-text {
          color: #6b7280;
        }
      }

      .dropdown-icon {
        font-size: 0.625rem;
        color: #9ca3af;
        transition: transform 0.2s ease;

        &.rotated {
          transform: rotate(180deg);
        }
      }
    }

    .language-dropdown-content {
      position: absolute;
      top: 100%;
      left: 0;
      right: 0;
      background: white;
      border: 1px solid #e5e7eb;
      border-radius: 12px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      z-index: 1000;
      margin-top: 0.25rem;
      padding: 0.5rem;
      display: flex;
      flex-direction: column;
      gap: 0.375rem;
      max-height: 200px;
      overflow-y: auto;

      .dropdown-language-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0.375rem 0.5rem;
        border-radius: 8px;
        transition: background-color 0.2s ease;

        &:hover {
          background: rgba(99, 102, 241, 0.05);
        }

        .language-name {
          font-size: 0.75rem;
          font-weight: 500;
          color: #374151;
        }

        .level-indicator {
          font-size: 0.625rem;
          font-weight: 600;
          padding: 0.125rem 0.375rem;
          border-radius: 6px;
          color: white;
        }
      }
    }
  }
}

// Solution 3: Stacked/Layered Language Display
.language-proficiency-section-stacked {
  .stacked-languages-container {
    display: inline-block;
    position: relative;

    .language-stack {
      display: flex;
      align-items: center;
      position: relative;
      height: 2rem;

      .stacked-language-pill {
        display: inline-flex;
        align-items: center;
        background: rgba(255, 255, 255, 0.95);
        border: 2px solid white;
        border-radius: 14px;
        padding: 0.25rem 0.5rem;
        position: absolute;
        top: 0;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        transition: all 0.2s ease;

        &:hover {
          transform: translateY(-2px) !important;
          box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
          z-index: 20 !important;
        }

        .language-name {
          font-size: 0.6875rem;
          font-weight: 500;
          margin-right: 0.25rem;
          max-width: 3rem;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        .level-indicator {
          font-size: 0.5625rem;
          font-weight: 600;
          padding: 0.125rem 0.25rem;
          border-radius: 6px;
          color: white;
          min-width: 1.125rem;
          text-align: center;
        }
      }

      .more-languages-indicator {
        position: absolute;
        top: 0;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
        border: 2px solid white;
        border-radius: 50%;
        width: 1.75rem;
        height: 1.75rem;
        color: white;
        font-size: 0.625rem;
        font-weight: 600;
        cursor: pointer;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        transition: all 0.2s ease;
        z-index: 5;

        &:hover {
          transform: translateY(-2px) scale(1.05);
          box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
        }

        .more-count {
          line-height: 1;
        }
      }
    }
  }
}

// Solution 4: Horizontal Scrollable Display
.language-proficiency-section-scrollable {
  .scrollable-languages-container {
    display: flex;
    align-items: center;
    max-width: 100%;
    position: relative;

    .languages-scroll-area {
      display: flex;
      gap: 0.375rem;
      overflow-x: auto;
      scrollbar-width: none;
      -ms-overflow-style: none;
      padding: 0.125rem 0;
      max-width: calc(100% - 1.5rem);

      &::-webkit-scrollbar {
        display: none;
      }

      .professional-language-pill.mini {
        flex-shrink: 0;
        padding: 0.25rem 0.5rem;
        border-radius: 12px;

        .language-name {
          font-size: 0.6875rem;
          max-width: 3rem;
        }

        .level-indicator {
          font-size: 0.5625rem;
          min-width: 1rem;
          height: 0.875rem;
        }
      }
    }

    .scroll-indicator {
      position: absolute;
      right: 0;
      top: 50%;
      transform: translateY(-50%);
      background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.9) 50%);
      padding-left: 1rem;
      color: #9ca3af;
      font-size: 0.75rem;
      pointer-events: none;
    }
  }
}

// Solution 5: Unified Language Badge
.language-proficiency-section-unified {
  .unified-language-badge {
    display: inline-flex;
    align-items: center;
    background: rgba(255, 255, 255, 0.9);
    border: 1px solid #e5e7eb;
    border-radius: 16px;
    padding: 0.375rem 0.75rem;
    gap: 0.5rem;
    transition: all 0.2s ease;
    cursor: pointer;
    max-width: 100%;

    &:hover {
      background: rgba(255, 255, 255, 1);
      box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
    }

    .pi-globe {
      color: #6366f1;
      font-size: 0.875rem;
      flex-shrink: 0;
    }

    .badge-text {
      font-size: 0.75rem;
      font-weight: 500;
      color: #374151;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      flex: 1;
      min-width: 0;
    }

    .level-indicators {
      display: flex;
      align-items: center;
      gap: 0.1875rem;
      flex-shrink: 0;

      .mini-level-dot {
        width: 0.5rem;
        height: 0.5rem;
        border-radius: 50%;
        flex-shrink: 0;

        &.dot-level-a1 { background: #f59e0b; }
        &.dot-level-a2 { background: #ea580c; }
        &.dot-level-b1 { background: #3b82f6; }
        &.dot-level-b2 { background: #2563eb; }
        &.dot-level-c1 { background: #a855f7; }
        &.dot-level-c2 { background: #8b5cf6; }
        &.dot-level-none { background: #9ca3af; }
      }

      .more-dots {
        font-size: 0.625rem;
        color: #9ca3af;
        font-weight: 600;
      }
    }

    &.no-languages {
      background: linear-gradient(135deg, #f9fafb 0%, #f3f4f6 100%);
      border-color: #d1d5db;

      .pi-globe {
        color: #9ca3af;
      }

      .badge-text {
        color: #6b7280;
      }
    }

    &.single {
      &.level-a1 { background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%); border-color: #f59e0b; }
      &.level-a2 { background: linear-gradient(135deg, #fed7aa 0%, #fdba74 100%); border-color: #ea580c; }
      &.level-b1 { background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%); border-color: #3b82f6; }
      &.level-b2 { background: linear-gradient(135deg, #bfdbfe 0%, #93c5fd 100%); border-color: #2563eb; }
      &.level-c1 { background: linear-gradient(135deg, #f3e8ff 0%, #e9d5ff 100%); border-color: #a855f7; }
      &.level-c2 { background: linear-gradient(135deg, #ede9fe 0%, #ddd6fe 100%); border-color: #8b5cf6; }
    }

    &.multiple {
      background: linear-gradient(135deg, rgba(99, 102, 241, 0.1) 0%, rgba(139, 92, 246, 0.1) 100%);
      border-color: rgba(99, 102, 241, 0.2);

      &:hover {
        background: linear-gradient(135deg, rgba(99, 102, 241, 0.15) 0%, rgba(139, 92, 246, 0.15) 100%);
        border-color: rgba(99, 102, 241, 0.3);
      }
    }
  }
}
