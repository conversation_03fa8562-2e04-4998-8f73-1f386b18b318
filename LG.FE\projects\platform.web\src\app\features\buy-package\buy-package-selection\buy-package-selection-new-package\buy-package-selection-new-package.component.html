<!-- Modern Package Selection Wizard -->
<div class="modern-package-wizard">

  <!-- Progress Indicator -->
  <div class="wizard-progress">
    <div class="progress-steps">
      <div class="progress-step" [class.active]="currentStep() >= 1" [class.completed]="currentStep() > 1">
        <div class="step-circle">
          <i class="pi pi-users" *ngIf="currentStep() <= 1"></i>
          <i class="pi pi-check" *ngIf="currentStep() > 1"></i>
        </div>
        <span class="step-label">Setup</span>
      </div>
      <div class="progress-line" [class.completed]="currentStep() > 1"></div>
      <div class="progress-step" [class.active]="currentStep() >= 2" [class.completed]="currentStep() > 2">
        <div class="step-circle">
          <i class="pi pi-globe" *ngIf="currentStep() <= 2"></i>
          <i class="pi pi-check" *ngIf="currentStep() > 2"></i>
        </div>
        <span class="step-label">Language</span>
      </div>
      <div class="progress-line" [class.completed]="currentStep() > 2"></div>
      <div class="progress-step" [class.active]="currentStep() >= 3" [class.completed]="currentStep() > 3">
        <div class="step-circle">
          <i class="pi pi-shopping-bag" *ngIf="currentStep() <= 3"></i>
          <i class="pi pi-check" *ngIf="currentStep() > 3"></i>
        </div>
        <span class="step-label">Package</span>
      </div>
    </div>
  </div>

  <!-- Main Content Area -->
  <div class="wizard-content">

    @let noStudentsFound = students$() && students$().data === null && !students$().loading && students$().initialized;

    @if(noStudentsFound) {
    <div class="empty-state-card">
      <app-empty-data-image-text [emptyDataText]="'You need to register a student before purchasing a package.'"
        [showAction]="true" [showActionButtonText]="'Register New Student'"
        (onActionSelected)="this.registerService.goToRegisterNewStudent()">
      </app-empty-data-image-text>
    </div>
    }

    @if(!students$() || students$().loading) {
    <div class="loading-state">
      <i class="pi pi-spin pi-spinner"></i>
      <span>Loading your learning options...</span>
    </div>
    } @else if (students$().data !== null) {

    <!-- Two-Column Learning Setup Section -->
    <div class="two-column-setup-section">
      <div class="setup-header">
        <h2 class="section-title">
          <i class="pi pi-sparkles"></i>
          Setup Your Learning Experience
        </h2>
        <p class="section-subtitle">Choose how you'd like to learn and get started</p>
      </div>

      <div class="two-column-grid">
        <!-- Left Column: Learning Style Selection -->
        <div class="left-column">
          <div class="setup-card learning-style-card">
            <h3 class="card-title">
              <i class="pi pi-users"></i>
              Learning Style
            </h3>
            <div class="learning-type-options">
              <div class="type-option" (click)="setLearningType('individual')"
                [class.selected]="learningType() === 'individual'">
                <div class="option-icon">
                  <i class="pi pi-user"></i>
                </div>
                <div class="option-content">
                  <h4>Individual</h4>
                  <p>1:1 personalized lessons</p>
                </div>
                <div class="option-check">
                  <i class="pi pi-check"></i>
                </div>
              </div>

              <div class="type-option" (click)="setLearningType('group')" [class.selected]="learningType() === 'group'">
                <div class="option-icon">
                  <i class="pi pi-users"></i>
                </div>
                <div class="option-content">
                  <h4>Group</h4>
                  <p>Learn together with friends</p>
                </div>
                <div class="option-check">
                  <i class="pi pi-check"></i>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Right Column: Unified Student/Group + Language Selection -->
        <div class="right-column">
          @if (learningType()) {
          <div class="setup-card unified-selection-card">
            <!-- Student/Group Selection Section -->
            <div class="selection-section">
              @if (learningType() === 'individual') {
              <div class="section-header">
                <h3 class="section-title">
                  <i class="pi pi-user"></i>
                  Select Student
                </h3>
              </div>
              <div class="selection-content">
                <app-prime-students-selection [items]="students$().data?.pageData"
                  (itemClicked)="onSelectedStudent($event)" [selectedStudent]="selectedStudent()"
                  styleClass="modern-student-selector" [resetSelectionSignal]="resetStudentsSelectionSignal()">
                </app-prime-students-selection>
              </div>
              }

              @if (learningType() === 'group') {
              <div class="section-header">
                <h3 class="section-title">
                  <i class="pi pi-users"></i>
                  Select Group
                </h3>
              </div>
              <div class="selection-content flex flex-column h-full w-full align-items-center justify-content-between">

                <app-prime-student-group-selection 
  [enablePagination]="true"
  [autoLoadInitialData]="true"
  [pageSize]="2"
  (groupItemClicked)="onSelectedGroup($event)"
  [resetSelectionSignal]="resetGroupSelectionSignal()"
  styleClass="modern-group-selector">
</app-prime-student-group-selection>

                  <!-- <app-prime-student-group-selection [items]="studentGroups.data?.studentGroups"
                  (groupItemClicked)="onSelectedGroup($event)" [resetSelectionSignal]="resetGroupSelectionSignal()"
                  styleClass="modern-group-selector">
                </app-prime-student-group-selection> -->
                @let studentGroups = studentGroups$();
                @if (studentGroups.data !== null && studentGroups.data?.studentGroups?.length > 0) {
              
                } @else {
                <div class="empty-groups-state">
                  <div class="empty-icon">
                    <i class="pi pi-users"></i>
                  </div>
                  <div class="empty-content">
                    <span class="empty-title">No groups yet</span>
                    <span class="empty-subtitle">Create your first group above</span>
                  </div>
                </div>
                }
                <button class="create-group-btn mt-auto" (click)="openNewGroupDialog()">
                  <div class="btn-icon">
                    <i class="pi pi-plus"></i>
                  </div>
                  <span class="btn-text">Create Group</span>
                  <div class="btn-glow"></div>
                </button>
              </div>
              }

              <!-- Language Selection Section -->
              @if (numberOfStudents() > 0 && learningType() === 'individual') {
              <div class="section-divider"></div>
              <div class="section-header mt-3">
                <h3 class="section-title">
                  <i class="pi pi-users"></i>
                  Select Language
                </h3>
              </div>
              <div class="language-section">

                <div class="language-selector">
                  <app-prime-dropdown [options]="languages" (selectedChange)="onSelectedLanguage($event)"
                    [selectedValue]="this.teachingLanguageId()" placeholder="Select language" optionLabel="name"
                    styleClass="modern-dropdown w-full">
                  </app-prime-dropdown>
                </div>
              </div>
              }
            </div>

          </div>
          } @else {
          <div class="setup-card placeholder-card">
            <div class="placeholder-content">
              <i class="pi pi-arrow-left"></i>
              <h3>Choose Your Learning Style</h3>
              <p>Select Individual or Group learning to continue</p>
            </div>
          </div>
          }
        </div>
      </div>

      <!-- Status Messages -->
      @if (numberOfStudents() === 0) {
      <div class="status-message info">
        <i class="pi pi-info-circle"></i>
        <span>Please select a student or group to continue</span>
      </div>
      }

      @if (showStudentAlreadyInGroupBanner()) {
      <div class="status-message warning">
        <i class="pi pi-exclamation-triangle"></i>
        <span>This student is already in a group. Please select a different student or group.</span>
      </div>
      }

      <!-- Free Trial Banners -->
      @if (showFreeTrialBanner() && numberOfStudents() > 1) {
      <app-buy-package-complete-trial-info-box [teachingLanguageName]="selectedTeachingLanguage().name"
        [selectedStudentsGroup]="selectedStudentsGroup()" textType="Group">
      </app-buy-package-complete-trial-info-box>
      }

      @if (showFreeTrialBanner() && numberOfStudents() === 1) {
      <app-buy-package-complete-trial-info-box [selectedStudent]="selectedStudent()"
        [teachingLanguageName]="selectedTeachingLanguage().name">
      </app-buy-package-complete-trial-info-box>
      }
    </div>
    }
  </div>


  <!-- Unified Duration & Package Selection Section -->
  @if (numberOfStudents() > 0 && !generalService.isObjectEmpty(selectedTeachingLanguage())) {
  <div class="unified-package-section">
    <div class="package-header">
      <h2 class="section-title">
        <i class="pi pi-shopping-bag"></i>
        Choose Your Perfect Package
      </h2>
      <p class="section-subtitle">Select the lesson duration and package that fits your learning goals</p>
    </div>

    <!-- Aligned Duration & Package Container -->
    <div class="aligned-selection-container">
      <!-- Duration Selection -->
      <div class="duration-selection">
        <h3 class="subsection-title">
          <i class="pi pi-clock"></i>
          Lesson Duration
        </h3>
        <div class="grid">
          <div class="col-12 lg:col-4 md:col-4 sm:col-12">
            <div class="duration-item-wrapper">
              <app-buy-package-duration-box [duration]="15" [(selectedDuration)]="duration"
                (selectedDurationChange)="onSelectedDuration($event)" subText="Perfect for ages 2-4">
              </app-buy-package-duration-box>
            </div>
          </div>
          <div class="col-12 lg:col-4 md:col-4 sm:col-12">
            <div class="duration-item-wrapper">
              <app-buy-package-duration-box [duration]="30" [(selectedDuration)]="duration"
                (selectedDurationChange)="onSelectedDuration($event)" subText="Ideal for ages 5-8">
              </app-buy-package-duration-box>
            </div>
          </div>
          <div class="col-12 lg:col-4 md:col-4 sm:col-12">
            <div class="duration-item-wrapper">
              <app-buy-package-duration-box [duration]="60" [(selectedDuration)]="duration"
                (selectedDurationChange)="onSelectedDuration($event)" subText="Great for ages 9-17">
              </app-buy-package-duration-box>
            </div>
          </div>
        </div>
      </div>

      <!-- Package Selection -->
      <div class="packages-selection">
        <h3 class="subsection-title">
          <i class="pi pi-gift"></i>
          Lesson Packages
        </h3>
        <div class="grid">
          @for (item of filteredPackagePrices(); track item; let index = $index) {
          <div class="col-12 md:col-6 lg:col-6 xl:col-4 sm:col-12">
            <div class="package-item-wrapper">
              <app-buy-package-pricing-box [package]="item" [numberOfStudents]="numberOfStudents()" [index]="index"
                [duration]="duration" [showSuccessTrigger]="shouldShowSuccessMessage(item)"
                [showLoadingTrigger]="shouldShowLoadingMessage(item)" (addToCart)="onPricingBoxSelected(item)"
                (extensionChange)="onExtensionSelected($event)">
              </app-buy-package-pricing-box>
            </div>
          </div>
          }
        </div>
      </div>
    </div>
  </div>
  }

  @if (numberOfStudents() > 0 && generalService.isObjectEmpty(selectedTeachingLanguage())) {
  <div class="language-selection-prompt">
    <div class="prompt-card">
      <div class="prompt-icon">
        <i class="pi pi-globe"></i>
      </div>
      <div class="prompt-content">
        <h3 class="prompt-title">Choose Your Language first</h3>
        <p class="prompt-text">Select a language above to unlock amazing packages</p>
      </div>
      <div class="prompt-arrow">
        <i class="pi pi-arrow-up"></i>
      </div>
    </div>
  </div>
  }

</div>

<!--  *ngIf="filteredPackagePrices().length > 0" -->
<!-- <div class="mt-3 w-full">
        <div class="mt-3 shadow-2 surface-card border-round p-2 sm:p-4 flex-1 text-center mb-2 md:mb-0">
            <div class="bottom-button w-full">
                @if (generalService.isObjectEmpty(selectedPackage())) {
                <app-form-field-validation-message [severity]="Severity.Info" styleClass="w-full" messageClass=" w-full"
                    [textStyleClass]="'text-xs w-full sm:mt-3'"
                    [text]="'Please select a package first'"></app-form-field-validation-message>
                }
                @else {


                <h2
                    class="hidden sm:flex align-items-center justify-content-start gap-2 m-0 primary-purple-color text-lg mb-2">
                    <span
                        class="hidden sm:flex surface-0 text-white bg-indigo-500 border-indigo-500 border-2 border-circle  align-items-center justify-content-center border-circle"
                        style="min-width: 2rem; min-height: 2rem;"> 4 </span>
                    <span> Confirm Your Order</span>
                </h2>

                <div class="flex flex-column gap-4 p-1 px-2 sm:p-4 surface-50 border-4 shadow-2">
                    <div
                        class="flex justify-content-between align-items-center border-bottom-1 surface-border sm:pb-2 gap-5">
                        <div class="flex flex-column align-items-start gap-0">
                            <h3 class="m-0 font-semibold text-sm sm:text-xl text-900">{{
                                selectedPackage().teachingLanguageName }}
                                {{generalService.getILanguageLevelsEnumText(selectedPackage().levelEnum)}}</h3>
                            <p class="mt-0 sm:mt-2 mb-0 text-sm sm:text-base text-600">{{
                                selectedPackage().numberOfLessons }} Lessons </p>
                        </div>
                        <div class="flex align-items-end justify-content-center flex-column gap-1">
                            <span class="text-xs sm:text-base text-900">
                                {{ numberOfStudents() }} Students</span>
                            <span class="font-semibold text-xs sm:text-base text-600">{{
                                selectedPackage().durationInMinutes }}
                                Minutes /
                                Lesson</span>
                        </div>
                    </div>
                </div>
                }
                <button pButton pRipple icon="pi pi-cart-plus" class="w-full sm:my-4 px-5 py-3 submit-btn 
    p-button p-button-lg text-2xl" (click)="addToCartEvent()" label="Add to Cart"
                    [disabled]="generalService.isObjectEmpty(selectedPackage())"></button>

            </div>
        </div>
    </div> -->