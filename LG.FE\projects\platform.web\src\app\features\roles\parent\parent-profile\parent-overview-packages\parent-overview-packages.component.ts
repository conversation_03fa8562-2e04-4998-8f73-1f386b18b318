import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, DestroyRef, inject, Injector, type OnInit } from '@angular/core';
import { ButtonModule } from 'primeng/button';
import { PaginatorModule } from 'primeng/paginator';
import { ApiLoadingStateService, AuthStateService, DataApiStateService, EventBusService, GeneralService, HandleApiResponseService, IGetPackagesForParentRequest, IGetPackagesForParentResponse, IPackage, ToastService } from 'SharedModules.Library';
import { PackageMiniInfoCardComponent } from '@platform.src/app/shared/dashboard/package-mini-info-card/package-mini-info-card.component';
import { InnerHeaderCardComponent } from '@platform.src/app/shared/layout/inner-header-card/inner-header-card.component';
import { Router } from '@angular/router';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';

@Component({
  selector: 'app-parent-overview-packages',
  imports: [
    CommonModule,
    ButtonModule,
    PaginatorModule,
    InnerHeaderCardComponent,
    PackageMiniInfoCardComponent,
  ],
  templateUrl: './parent-overview-packages.component.html',
  styleUrl: './parent-overview-packages.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class ParentOverviewPackagesComponent implements OnInit {
  generalService = inject(GeneralService);
  dummyPackages: any[] = this.generalService.dummyPackages;

  // Injected services
  readonly services = {
    general: inject(GeneralService),
    auth: inject(AuthStateService),
    api: inject(HandleApiResponseService),
    dataState: inject(DataApiStateService),
    toast: inject(ToastService),
    eventBus: inject(EventBusService),
    apiLoadingStateService: inject(ApiLoadingStateService),
    router: inject(Router)
  };

  private readonly destroyRef = inject(DestroyRef);
  private readonly injector = inject(Injector);
  breadcrumbs = [
    { label: 'Packages', url: '' }
  ];
  ngOnInit(): void {

    this.loadGetPackagesForParent();
  }


  loadGetPackagesForParent() {
    const params: IGetPackagesForParentRequest = {
      parentId: this.services.auth.getUserClaims().id,
      filters: {
        includeActive: true,
        includeInactive: true,
        includeCompleted: true,
        includeExpired: true,
        includePaid: true,
        includeFree: true,
        includeFreeTrial: true,
      }
    }
    this.services.api.getApiData<IGetPackagesForParentResponse>(
      { url: IPackage.getPackagesForParent, method: 'GET' },
      params
    ).pipe(takeUntilDestroyed(this.destroyRef)).subscribe({
      next: (response) => {
        if (response) {
          console.log('Packages loaded successfully', response);
        }
      },
      error: (error) => {
        this.services.toast.showError('Failed to load packages');
        console.error('Error loading packages:', error);
      }
    });
  }
}
