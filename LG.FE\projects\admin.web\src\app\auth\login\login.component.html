<div class="login-container">
    <div class="login-wrapper surface-ground flex flex-column align-items-center justify-content-center min-h-screen">
        <div class="flex flex-column align-items-center justify-content-center">
            <div class="login-box surface-card p-6 shadow-2 border-round w-full lg:w-30rem">
                <div class="text-center mb-4">
                    <img src="shared-assets/images/shared/lingogalaxy-logo.svg" alt="Logo" class="mb-4 w-6rem">
                    <div class="text-900 text-3xl font-medium mb-2">Welcome Back!</div>
                    <span class="text-600">Sign in to continue to admin panel</span>
                </div>

                <form [formGroup]="loginForm" (ngSubmit)="login()" class="flex flex-column gap-3">
                    <div class="flex flex-column gap-0">
                        <label for="email" class="text-900 font-medium">Email</label>


                        <p-iconfield class="w-full">
                            <input 
                                id="email" 
                                type="email" 
                                pInputText 
                                formControlName="email"
                                [ngClass]="{'ng-invalid ng-dirty': loginForm.get('email')?.invalid && loginForm.get('email')?.touched}"
                                styleClass="w-full p-3" 
                                class="w-full"
                                placeholder="Enter your email">
                        </p-iconfield>
                        @if (loginForm.get('email')?.invalid && loginForm.get('email')?.touched) {
                            <app-form-field-validation-message text="Please enter a valid email address" messageClass="mb-3"
                            styleClass="w-full"></app-form-field-validation-message>
                        }
                    
                    </div>

                    <div class="flex flex-column gap-0">
                        <label for="password" class="text-900 font-medium">Password</label>

                        
                        <p-iconfield class="w-full">
                            <input 
                            id="password" 
                            [type]="passwordVisible() ? 'text' : 'password'"
                            pInputText 
                            formControlName="password"
                            [ngClass]="{'ng-invalid ng-dirty': loginForm.get('password')?.invalid && loginForm.get('password')?.touched}"
                            class="w-full" 
                            placeholder="Enter your password">

                            <p-inputicon [class]="passwordVisible() ? 'pi pi-eye-slash' : 'pi pi-eye'" (click)="togglePasswordVisibility()" />
                   
                        </p-iconfield>
                        @if (loginForm.get('password')?.invalid && loginForm.get('password')?.touched) {
                            <app-form-field-validation-message text="Password is required" messageClass="mb-3"
                            styleClass="w-full"></app-form-field-validation-message>
                        }
                   
                    </div>


                    <button 
                        pButton 
                        pRipple 
                        type="submit" 
                        [loading]="isLoading()" 
                        [disabled]="loginForm.invalid || isLoading()" 
                        label="Sign In" 
                        class="p-3 font-bold">
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>