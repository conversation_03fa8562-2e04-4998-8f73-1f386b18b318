import { Component, inject, ViewChildren, Query<PERSON>ist, ViewChild, ViewContainerRef, ComponentRef, Injector, Type, computed } from '@angular/core';

interface DialogOpenData {
  component: Type<unknown>;
  parameters?: Record<string, unknown>;
}
import { RouterOutlet, Router, NavigationEnd } from '@angular/router';
import { ButtonModule } from 'primeng/button';
import { Drawer } from 'primeng/drawer'; // Import Drawer for ViewChildren
import { AuthStateService, CommonService, EventBusService, GeneralService, MyPreset, StateApiCallsComponent, TopbarComponent } from 'SharedModules.Library';
import { ToastComponent, ToastService } from 'SharedModules.Library';
import { filter } from 'rxjs/operators';
import { PrimeNG } from 'primeng/config';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-root',
  imports: [
    RouterOutlet,
    CommonModule,
    ButtonModule,
    ToastComponent,
    StateApiCallsComponent,
    TopbarComponent,
  ],
  templateUrl: './app.component.html',
  styleUrl: './app.component.css',
})
export class AppComponent {
  title = 'Admin.Web';
  toastService = inject(ToastService);
  commonService = inject(CommonService);
  private primeConfig = inject(PrimeNG);
  private router = inject(Router); // Inject Router

  generalService = inject(GeneralService);
  authService = inject(AuthStateService);
  eventBusService = inject(EventBusService);
  @ViewChild('dynamicComponentContainer', {
    read: ViewContainerRef,
    static: true
  }) dynamicComponentContainer: ViewContainerRef | undefined;
  // If p-drawer is directly in AppComponent's template
  @ViewChildren(Drawer) drawers!: QueryList<Drawer>;
  loader = computed(() => {
    return this.generalService.routerLoading();
  });
  divLoader = computed(() => {
    return this.generalService.divLoading();
  });
  
  user$ = computed(() => this.authService.getUserClaims())
  constructor() {
    this.primeConfig.theme.set({
      preset: MyPreset,
      options: {
        //TODO: add real dark mode selector
        darkModeSelector: '.fake-dark-selector', // trying to also force a non-usage of the dark mode
        cssLayer: {
          name: 'primeng',
          order: 'primeng'
        }
      }
    })

    // Subscribe to router events
    this.router.events
      .pipe(filter((event) => event instanceof NavigationEnd))
      .subscribe(() => {
        this.commonService.closeAllDrawers();
      });
  }

  ngOnInit() {
    // Any initialization logic

    this.generalService.openDialog$.subscribe(object => {
      if (!object) {
        return;
      }
      console.log(object);
      // Logic to handle opening the dialog with the received parameters
      const dialogData = object as DialogOpenData;
      this.loadDialogComponent(dialogData.component, dialogData.parameters);
    });
  }


  loadDialogComponent(
    component?: Type<unknown>,
    parameters?: Record<string, unknown>
  ): ComponentRef<unknown> {
    // Clear previous dynamic component, if any
    this.dynamicComponentContainer!.clear();

    // Create the component and attach it to the view
    const componentRef = component ? this.dynamicComponentContainer!.createComponent(component, {
      injector: Injector.create({
        providers: [
          { provide: 'dialogParameters', useValue: parameters }
        ]
      })
    }) : ComponentRef<unknown>;

    return componentRef as ComponentRef<unknown>;
  }

  // Method to close all drawers
  private closeAllDrawers() {
    if (this.drawers) {
      console.log('Closing all drawers', this.drawers)
      this.drawers.forEach((drawer) => {
        drawer.visible = false; // Set visibility to false
      });
    }
  }
}