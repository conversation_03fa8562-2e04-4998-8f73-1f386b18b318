import { CommonModule } from '@angular/common';
import { Component, DestroyRef, EventEmitter, inject, Inject, Input, Output, signal } from '@angular/core';
import { ButtonModule } from 'primeng/button';
import { DialogModule } from 'primeng/dialog';
import { ApiLoadingStateService, AuthStateService, CustomDialogPopupComponent, EmitEvent, EventBusService, Events, HandleApiResponseService, ToastService } from 'SharedModules.Library';

export interface StudentSwitchData {
  userId: string;
  firstName: string;
  lastName: string;
}

export interface AccountSwitchResult {
  confirmed: boolean;
  studentData?: StudentSwitchData;
}

@Component({
  imports: [CommonModule, ButtonModule, DialogModule, CustomDialogPopupComponent],
  selector: 'app-account-switch-dialog',
  templateUrl: './account-switch-dialog.component.html',
  styleUrls: ['./account-switch-dialog.component.scss']
})
export class AccountSwitchDialogComponent {
  @Input() visible: boolean = false;
  @Input() studentData!: StudentSwitchData;

  @Output() visibleChange = new EventEmitter<boolean>();
  @Output() switchConfirmed = new EventEmitter<AccountSwitchResult>();
  @Output() switchCancelled = new EventEmitter<void>();
  eventBusService = inject(EventBusService);
  toastService = inject(ToastService);
  apiService = inject(HandleApiResponseService);
  authService = inject(AuthStateService);
  apiLoadingStateService = inject(ApiLoadingStateService);
  private readonly destroy: DestroyRef = inject(DestroyRef);
  parameters: any;

  // Galaxy transition state
  isTransitioning = signal(false);
  private lastClickTime = 0;
  private readonly CLICK_DEBOUNCE_MS = 500; // Prevent clicks within 500ms


  constructor(@Inject('dialogParameters') parameters: any) {
    this.studentData = (parameters);
    console.log(this.studentData);
    this.visible = true;
  }

  onDialogHide() {
    this.visible = false;
    this.visibleChange.emit(false);
  }

  onCancel() {
    this.visible = false;
    this.visibleChange.emit(false);
    this.switchCancelled.emit();
  }

  onConfirm() {
    // Debounce rapid clicks
    const now = Date.now();
    if (now - this.lastClickTime < this.CLICK_DEBOUNCE_MS) {
      return;
    }
    this.lastClickTime = now;

    // Prevent multiple clicks during processing
    if (this.apiLoadingStateService.getIsLoading() || this.isTransitioning()) {
      return;
    }

    // Start galaxy transition animation
    this.isTransitioning.set(true);

    // Generate unique request ID for this specific action
    const switchRequestId = `account-switch-${this.studentData.userId}-${Date.now()}`;
    this.apiLoadingStateService.beginRequest(switchRequestId);

    // Delay the actual account switch to allow animation to complete
    setTimeout(() => {
      try {
        this.visible = false;
        this.visibleChange.emit(false);
        this.eventBusService.emit(new EmitEvent(Events.StateLoadStartImpersonate,
          {
            impersonateStudentId: this.studentData.userId,
            parentRefreshToken: this.authService.getRefreshToken()
          }
        ));
      } finally {
        // End the specific request tracking after a delay to ensure UI feedback
        setTimeout(() => {
          this.apiLoadingStateService.endRequest(switchRequestId);
          this.isTransitioning.set(false);
        }, 100);
      }
    }); // 1.2 second delay for animation
  }
}
