<div class="relative userform_info__container" [appLoader]="generalService.divLoading()">
    <!-- <div class="w-full">
        <div class="flex justify-content-between align-items-center border-bottom-1 pb-2 surface-border">
            <span class="text-900 font-medium text-lg lg:text-xl">{{ mainTitle() }}</span>
            @if (showTitleButton()) {
            <p-button size="small" (click)="onTitleButtonClick()" label="Back" icon="pi pi-chevron-left" styleClass="px-2 py-1"
                [outlined]="true" />
            }
        </div>
    </div> -->



    <div>

        @if (displayMode()) {


        <form [formGroup]="userForm" (ngSubmit)="onSubmit()" class="w-full">
            <div class=" mb-3">
                <div class="flex align-items-center">
                    <span
                        class="inline-flex align-items-center justify-content-center bg-primary-100 border-round mr-3 p-2">
                        <i class="pi pi-user text-primary text-xl"></i>
                    </span>
                    <span class="font-medium text-lg text-primary-900">Personal Information</span>
                </div>

                <div class="flex gap-5 flex-column-reverse md:flex-row">
                    <div class="flex-auto p-fluid">
                        <div class="grid formgrid p-fluid mb-3">
                            <ng-container *ngFor="let field of profileConfig[this.finalRole()]">
                                <ng-container [ngSwitch]="field.fieldName">
                                    <ng-container *ngSwitchCase="'emailAddress'">
                                        <div class="field mb-3 col-12">
                                            <app-prime-reactive-form-input label="Primary Email" [showLabelAbove]="true"
                                            [labelClass]="'block font-medium mb-2 primary-purple-color text-900 text-left text-md w-full'"
                                                formControlName="emailAddress" [parentForm]="userForm" type="email"
                                                placeholder="Your Email" [required]="field.required"
                                                [disabled]="field.disabled!" pAutoFocus [inputClass]="'w-full'" />

                                            <div *ngIf="submissionMessage()" class="submission-message">
                                                <h3>{{ submissionMessage() }}</h3>
                                            </div>

                                            <app-prime-reactive-form-array-input label="Additional Email Addresses"
                                                id="email-addresses-section" formArrayName="NewEmailAddresses"
                                                [parentForm]="userForm" [addButtonLabelText]="'Add New Email Address'"
                                                [maxItems]="2 - getUserInfoEmailsExceptPrimary().length"
                                                infoText="Additional email addresses can be used to receive notifications and updates. Each email must be verified before use."
                                                type="email" placeholder="Enter additional email address"
                                                [required]="false" [inputClass]="'w-full'" displayField="email"
                                                iconConditionField="isConfirmed" [allHidden]="false"
                                                wrapperClass="form-group w-full surface-card shadow-2 border-round mt-3 px-3 py-3 bg-indigo-50 border-1 border-indigo-500">
                                                <div top>
                                                    @if (getUserInfoEmailsExceptPrimary().length > 0) {
                                                    <div class="mb-0">
                                                        <div class="grid">
                                                            @for (section of getUserInfoEmailsExceptPrimary(); track
                                                            section.email; let i = $index) {
                                                            @if (!section.isPrimary) {
                                                            <div class="col-12">
                                                                <div class="card surface-card p-3 shadow-2 border-round my-2 transition-all transition-duration-300 hover:shadow-4"
                                                                    [ngClass]="{
                                                                    'bg-green-50 border-left-3 border-green-500': section.isConfirmed,
                                                                    'bg-yellow-50 border-left-3 border-yellow-500': !section.isConfirmed
                                                                 }">
                                                                    <div class="flex flex-column gap-2">
                                                                        <div
                                                                            class="flex align-items-center justify-content-between">
                                                                            <div class="flex align-items-center gap-2">
                                                                                <i
                                                                                    class="pi pi-envelope text-primary text-xl"></i>
                                                                                <span class="font-medium text-900"
                                                                                    [ngClass]="{
                                                                                'text-green-700': section.isConfirmed,
                                                                                'text-yellow-700': !section.isConfirmed
                                                                            }">
                                                                                    {{section.email}}
                                                                                </span>
                                                                            </div>

                                                                            @if (!section.isConfirmed) {
                                                                            <span
                                                                                class="inline-flex align-items-center px-3 py-2 border-round-xl font-bold text-sm bg-yellow-100 text-yellow-800 border-1 border-yellow-300">
                                                                                <i
                                                                                    class="pi pi-exclamation-circle mr-2 text-yellow-600"></i>
                                                                                ACTION NEEDED
                                                                            </span>
                                                                            } @else {
                                                                            <span
                                                                                class="inline-flex align-items-center px-3 py-2 border-round-xl font-bold text-sm bg-green-100 text-green-800 border-1 border-green-300">
                                                                                <i
                                                                                    class="pi pi-check-circle mr-2 text-green-600"></i>
                                                                                VERIFIED
                                                                            </span>
                                                                            }
                                                                        </div>

                                                                        <div class="border-top-1 my-2" [ngClass]="{
                                                                        'border-green-200': section.isConfirmed,
                                                                        'border-yellow-200': !section.isConfirmed
                                                                    }"></div>

                                                                        <div
                                                                            class="flex align-items-center justify-content-between">
                                                                            @if (section.isConfirmed) {
                                                                            <div class="flex align-items-center gap-2">
                                                                                <span
                                                                                    class="bg-green-100 border-round-xl p-2 px-3 flex align-items-center shadow-1">
                                                                                    <i
                                                                                        class="pi pi-shield text-green-600 mr-2 text-sm"></i>
                                                                                    <span
                                                                                        class="text-green-800 font-medium">Email
                                                                                        Confirmed</span>
                                                                                </span>
                                                                            </div>
                                                                            } @else {
                                                                            <p-button label="Verify Now"
                                                                                icon="pi pi-envelope"
                                                                                styleClass="p-button-warning p-button-sm"
                                                                                (click)="openConfirmEmailDialog(section.email!)"
                                                                                [disabled]="section.isConfirmed">
                                                                            </p-button>
                                                                            }

                                                                            <p-button icon="pi pi-trash"
                                                                                styleClass="p-button-rounded p-button-danger p-button-sm"
                                                                                pTooltip="Remove this email address"
                                                                                tooltipPosition="top"
                                                                                (click)="removeEmail($index)">
                                                                            </p-button>
                                                                        </div>

                                                                        @if (!section.isConfirmed) {
                                                                        <div
                                                                            class="mt-2 p-2 border-round bg-yellow-50 border-1 border-yellow-200">
                                                                            <div class="flex align-items-start gap-2">
                                                                                <i
                                                                                    class="pi pi-info-circle text-yellow-600 mt-1"></i>
                                                                                <span class="text-yellow-800 text-sm">
                                                                                    You need to verify this email
                                                                                    address before
                                                                                    you can use it.
                                                                                    Click "Verify Now" to receive a
                                                                                    verification
                                                                                    link.
                                                                                </span>
                                                                            </div>
                                                                        </div>
                                                                        }
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            }
                                                            }
                                                        </div>
                                                    </div>
                                                    }
                                                </div>
                                                <div bottom class="border-green-500">
                                                    <div class="flex align-items-center justify-content-between mb-2 bg-green-50 border-1 border-green-500 p-1 
                                                border-4">
                                                        <span class="m-0 text-sm flex align-items-center gap-2"><i
                                                                class="pi pi-check-circle text-green-500"></i>
                                                            Email is verified
                                                        </span>
                                                    </div>
                                                </div>
                                            </app-prime-reactive-form-array-input>
                                        </div>
                                    </ng-container>
                                    <ng-container *ngSwitchCase="'firstName'">
                                        <div class="field mb-3 col-12 md:col-6">
                                            <app-prime-reactive-form-input label="First Name" [showLabelAbove]="true"
                                                formControlName="firstName" [parentForm]="userForm" type="text"
                                                placeholder="Your First Name" [required]="field.required" pAutoFocus
                                                [inputClass]="'w-full'" data-name="First Name" />
                                            <app-form-field-validation-message messageClass="text-red-500 mb-0"
                                                [control]="userForm.controls['firstName']" propertyName="first Name" />
                                        </div>
                                    </ng-container>

                                    <ng-container *ngSwitchCase="'lastName'">
                                        <div class="field mb-3 col-12 md:col-6">
                                            <app-prime-reactive-form-input label="Last Name" [showLabelAbove]="true"
                                                formControlName="lastName" [parentForm]="userForm" type="text"
                                                placeholder="Your Last Name" [required]="field.required" pAutoFocus
                                                [inputClass]="'w-full'" data-name="Last Name" />
                                            <app-form-field-validation-message messageClass="text-red-500 mb-0"
                                                [control]="userForm.controls['lastName']" propertyName="Last Name" />
                                        </div>
                                    </ng-container>

                                    <ng-container *ngSwitchCase="'dateOfBirth'">
                                        <div class="field mb-3 col-12 md:col-6">
                                            <lib-date-of-birth-picker [label]="'Date of Birth'"
                                            [labelClass]="'block font-medium mb-2 primary-purple-color text-900 text-left text-md w-full'"
                                                [required]="field.required" [defaultDateYearsOffset]="30"
                                                [maxAge]="this.finalRole() === IUserRole.STUDENT ? 17 : 100"
                                                [minAge]="this.finalRole() === IUserRole.STUDENT ? 2 : 18"
                                                [formControlName]="'dateOfBirth'" [parentFormGroup]="userForm"
                                                data-name="dateOfBirth" [showDefaultDate]="false"
                                                [dateFormatToSubmit]="dateFormatToSubmit" [placeholder]="'dd/mm/yy'"
                                                (dateSelected)="onSelectedDateOfBirth($event)"></lib-date-of-birth-picker>
                                            <app-form-field-validation-message messageClass="text-red-500 mb-0"
                                                [control]="userForm.controls['dateOfBirth']"
                                                propertyName="Date of Birth" />
                                        </div>
                                    </ng-container>

                                 
                                    <ng-container *ngSwitchCase="'timeZoneIana'">
                                        <div class="field mb-3 col-12 md:col-6">
                                            <label
                                                class="block font-medium mb-2 primary-purple-color text-900 text-left text-md w-full"
                                                for="timeZoneIana"
                                                [innerHTML]="generalService.getDisplayLabel('Timezone',field.required )">
                                            </label>
                                            <app-prime-timezone-dropdown [parentForm]="userForm"
                                                [useGeoLocation]="false" formControlName="timeZoneIana"
                                                styleClass="full-width w-full" data-name="Timezone" />
                                            <app-form-field-validation-message messageClass="text-red-500 mb-0"
                                                [control]="userForm.controls['timeZoneIana']" propertyName="timeZone" />
                                        </div>
                                    </ng-container>
                                <ng-container *ngSwitchCase="'dialCode'">
                                    <div class="field mb-3 col-12 md:col-6">
                                        <label
                                            class="block font-medium mb-2 primary-purple-color text-900 text-left text-md w-full"
                                            for="mobileNumber"
                                            [innerHTML]="generalService.getDisplayLabel('Phone Number',field.required )">
                                        </label>
                                        <app-country-phone-input [parentForm]="userForm" [useGeoLocation]="false"
                                            wrapperDivClass="card flex w-full justify-content-between mb-0"
                                            (countryCodeChange)="onCountryCodeChange($event)"
                                            (dialCodeChange)="onDialCodeChange($event)" />
                                        <app-form-field-validation-message messageClass="text-red-500 mb-0"
                                            [control]="userForm.controls['mobileNumber']" propertyName="mobileNumber">
                                        </app-form-field-validation-message>
                                    </div>
                                </ng-container>

                                    <ng-container *ngSwitchCase="'zoom'">
                                        <div class="field mb-3 col-12 md:col-6">
                                            <app-prime-reactive-form-input label="Zoom Account Email or ID"
                                                [showLabelAbove]="true" formControlName="zoom" [parentForm]="userForm"
                                                type="text" placeholder="Enter your Zoom account email or ID"
                                                [required]="field.required" data-name="zoom"
                                                [tooltip]="'Enter the email address you use to sign in to Zoom. This helps us connect your lessons to your Zoom account.'"
                                                pAutoFocus [inputClass]="'w-full'" />
                                            <app-form-field-validation-message messageClass="text-red-500 mb-0"
                                                [control]="userForm.controls['zoom']" propertyName="zoom" />
                                        </div>
                                    </ng-container>

                                    <ng-container *ngSwitchCase="'msTeamsEmailAddress'">
                                        <div class="field mb-3 col-12 md:col-6">
                                            <app-prime-reactive-form-input label="Microsoft Teams Email Address "
                                                [showLabelAbove]="true" formControlName="msTeamsEmailAddress"
                                                [parentForm]="userForm" type="text" placeholder="msTeamsEmailAddress"
                                                [required]="field.required" data-name="msTeamsEmailAddress" pAutoFocus
                                                [inputClass]="'w-full'" />
                                            <app-form-field-validation-message messageClass="text-red-500 mb-0"
                                                [control]="userForm.controls['msTeamsEmailAddress']"
                                                propertyName="msTeamsEmailAddress" />
                                        </div>
                                    </ng-container>

                                    <ng-container *ngSwitchCase="'gender'">
                                        <div class="field mb-3 col-12 md:col-6">
                                            <label
                                                class="block font-medium mb-2 primary-purple-color text-900 text-left text-md w-full"
                                                for="Gender"
                                                [innerHTML]="generalService.getDisplayLabel('Gender',field.required )">
                                            </label>
                                            <div class="flex align-items-center justify-content-between mb-0">
                                                <div class="flex align-items-center justify-content-start gap-4 w-full">
                                                    <div
                                                        class="flex flex-wrap justify-content-start gap-4 primary-purple-color">
                                                        <div class="flex align-items-center">
                                                            <p-radioButton [value]="IGenderEnum.Male"
                                                                formControlName="gender" inputId="gender1" name="gender"
                                                                styleClass="typeRadio"></p-radioButton>
                                                            <label class="ml-2" for="gender1">Boy</label>
                                                        </div>

                                                        <div class="flex align-items-center">
                                                            <p-radioButton [value]="IGenderEnum.Female"
                                                                formControlName="gender" inputId="gender2" name="gender"
                                                                styleClass="typeRadio"></p-radioButton>
                                                            <label class="ml-2" for="gender2">Girl</label>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <app-form-field-validation-message messageClass="text-red-500 mb-0"
                                                [control]="userForm.controls['gender']" propertyName="Gender" />
                                        </div>
                                    </ng-container>
                                </ng-container>
                            </ng-container>
                        </div>
                    </div>
                </div>

            </div>

            <div class="flex align-items-center">
                <span
                    class="inline-flex align-items-center justify-content-center bg-primary-100 border-round mr-3 p-2">
                    <i class="pi pi-home text-primary text-xl"></i>
                </span>
                <span class="font-medium text-lg text-primary-900">Address Information</span>
            </div>

            <div class="flex gap-5 flex-column-reverse md:flex-row">
                <div class="flex-auto p-fluid">
                    <div class="grid formgrid p-fluid mb-3">
                        <ng-container *ngFor="let field of profileConfig[this.finalRole()]">
                            <ng-container [ngSwitch]="field.fieldName">
                                <ng-container *ngSwitchCase="'addressLine1'">
                                    <div class="field mb-3 col-12 md:col-6">
                                        <app-prime-reactive-form-input label="Address 1 " [showLabelAbove]="true"
                                            formControlName="addressLine1" [parentForm]="userForm" type="text"
                                            placeholder="Your Address Line 1" [required]="field.required"
                                            data-name="Address Line 1" pAutoFocus [inputClass]="'w-full'" />
                                        <app-form-field-validation-message messageClass="text-red-500 mb-0"
                                            [control]="userForm.controls['addressLine1']"
                                            propertyName="Address Line 1" />
                                    </div>
                                </ng-container>

                                <ng-container *ngSwitchCase="'addressLine2'">
                                    <div class="field mb-3 col-12 md:col-6">
                                        <app-prime-reactive-form-input label="Address 2 " [showLabelAbove]="true"
                                            formControlName="addressLine2" [parentForm]="userForm" type="text"
                                            placeholder="Your Address Line 2" [required]="field.required" pAutoFocus
                                            [inputClass]="'w-full'" />
                                        <app-form-field-validation-message messageClass="text-red-500 mb-0"
                                            [control]="userForm.controls['addressLine2']"
                                            propertyName="Address Line 2" />
                                    </div>
                                </ng-container>

                                <ng-container *ngSwitchCase="'state'">
                                    <div class="field mb-3 col-12 md:col-6">
                                        <app-prime-reactive-form-input label="State " [showLabelAbove]="true"
                                            [formControlName]="field.fieldName" [parentForm]="userForm" type="text"
                                            placeholder="Your State/County"
                                            [required]="(userForm.get('country')?.value) ?
                                    (userForm.get('country')?.value.name && generalService.isStateRequired(userForm.get('country')?.value.name)) : false"
                                            data-name="State" pAutoFocus [inputClass]="'w-full'" />
                                        <app-form-field-validation-message messageClass="text-red-500 mb-0"
                                            [control]="userForm.controls[field.fieldName]" propertyName="State" />
                                    </div>
                                </ng-container>

                                <ng-container *ngSwitchCase="'city'">
                                    <div class="field mb-3 col-12 md:col-6">
                                        <app-prime-reactive-form-input label="City " [showLabelAbove]="true"
                                            formControlName="city" [parentForm]="userForm" type="text"
                                            placeholder="Your City" [required]="field.required" data-name="City"
                                            pAutoFocus [inputClass]="'w-full'" />
                                        <app-form-field-validation-message messageClass="text-red-500 mb-0"
                                            [control]="userForm.controls[field.fieldName]" propertyName="City" />
                                    </div>
                                </ng-container>

                                <ng-container *ngSwitchCase="'postCode'">
                                    <div class="field mb-3 col-12 md:col-6">
                                        <app-prime-reactive-form-input label="Post Code" [showLabelAbove]="true"
                                            [formControlName]="field.fieldName" [parentForm]="userForm" type="text"
                                            placeholder="Your Post Code" [required]="field.required" pAutoFocus
                                            [inputClass]="'w-full'" data-name="Post Code" />
                                        <app-form-field-validation-message messageClass="text-red-500 mb-0"
                                            [control]="userForm.controls[field.fieldName]" propertyName="Post Code" />
                                    </div>
                                </ng-container>
                                   <ng-container *ngSwitchCase="'country'">
                                        <div class="field mb-3 col-12 md:col-6">
                                            <label
                                                class="block font-medium mt-3 mb-2 primary-purple-color text-900 text-left text-md w-full"
                                                for="country"
                                                [innerHTML]="generalService.getDisplayLabel('Country',field.required )">
                                            </label>
                                            <app-prime-countries-dropdown [parentForm]="userForm"
                                                formControlName="country" data-name="Country"
                                                styleClass="w-full text-left" />
                                            <app-form-field-validation-message messageClass="text-red-500 mb-0"
                                                [control]="userForm.controls['country']" propertyName="Country" />
                                        </div>
                                    </ng-container>

                            </ng-container>
                        </ng-container>
                    </div>
                </div>
            </div>

            @if (this.finalRole() === IUserRole.TEACHER) {
            <div class="field mb-0 col-12 mt-5">
                <p-accordion [value]="0" styleClass="w-full" id="speakingLanguages">
                    <p-accordion-panel [value]="0">
                        <p-accordion-header>
                            <div class="flex align-items-center justify-content-between w-full sm:mr-1">
                                <div class="flex flex-column gap-1s">
                                    <div>
                                        <span class="text-indigo-900 font-medium text-lg">Speaking Languages
                                            <i class="pi pi-info-circle text-indigo-500"
                                                pTooltip="Add the languages you speak and indicate if they are native"
                                                tooltipPosition="top"></i>
                                            <span class="text-red-500">*</span>
                                        </span>
                                    </div>
                                    <div>
                                        <span class="text-xs">Select languages you speak and indicate if
                                            they are native</span>
                                    </div>
                                </div>
                                <p-badge [value]="speakingLanguages.length.toString()"
                                    styleClass="bg-indigo-200 text-indigo-900" />
                            </div>
                            <ng-template #toggleicon let-active="active">
                                @if (active) {
                                <i class="pi pi-chevron-up"></i>
                                } @else {
                                <i class="pi pi-chevron-down"></i>
                                }
                            </ng-template>
                        </p-accordion-header>
                        <p-accordion-content>
                            <div class="field mb-0 col-12">
                                <div class="surface-card shadow-0 border-round bg-indigo-50 border-0">
                                    <div class="grid">
                                        <ng-container formArrayName="speakingLanguages">
                                            <ng-container
                                                *ngFor="let language of speakingLanguages.controls; let i=index">
                                                <div class="col-12" [formGroupName]="i">
                                                    <div class="card surface-card p-3 shadow-1 border-round my-2">
                                                        <div class="flex flex-column gap-3 ">
                                                            <div class="grid grid-nogutter">
                                                                <div class="col-12 md:col-5 p-2">
                                                                    <label
                                                                        class="block font-medium mb-2 primary-purple-color text-900 text-left text-md w-full">
                                                                        Language
                                                                        @if
                                                                        (isTeachingLanguage(language.get('language')?.value!))
                                                                        {
                                                                        <span
                                                                            class="bg-cyan-500 text-white px-2 py-1 border-round text-xs ml-2">Teaching</span>
                                                                        }
                                                                    </label>
                                                                    @if (nativeLanguages$() &&
                                                                    nativeLanguages$().data !== null &&
                                                                    nativeLanguages$().data?.languages.length >
                                                                    0) {
                                                                    <p-select [appendTo]="'body'" [filter]="true"
                                                                        [ngClass]="{
                                                                            'pointer-events-none': isTeachingLanguage(language.get('language')?.value!)
                                                                        }"
                                                                        [disabled]="isTeachingLanguage(language.get('language')?.value!)"
                                                                        [options]="getFilteredLanguageOptions(i)"
                                                                        formControlName="language"
                                                                        placeholder="Select a language"
                                                                        styleClass="w-full">
                                                                    </p-select>
                                                                    }
                                                                    <app-form-field-validation-message
                                                                        messageClass="text-red-500 text-xs mt-1"
                                                                        [control]="language.get('language')"
                                                                        propertyName="Language" />
                                                                </div>
                                                                <div class="col-12 md:col-3 p-2">
                                                                    <label
                                                                        class="block font-medium mb-2 primary-purple-color text-900 text-left text-md w-full"
                                                                        for="languageLevel">
                                                                        Level
                                                                    </label>
                                                                    <lib-language-level-selector viewMode="dropdown"
                                                                        [isOptional]="false"
                                                                        [value]="language.get('languageLevel')?.value!"
                                                                        (valueChange)="onNativeLanguageLevelChange($event, i)">
                                                                    </lib-language-level-selector>
                                                                    <app-form-field-validation-message
                                                                        messageClass="text-red-500 text-xs mt-1"
                                                                        [control]="language.get('languageLevel')"
                                                                        propertyName="Language Level" />
                                                                </div>
                                                                <div class="col-12 md:col-2 p-2 flex flex-column">
                                                                    <label
                                                                        class="block font-medium mb-2 primary-purple-color text-900 text-left text-md w-full">
                                                                        Native
                                                                    </label>
                                                                    <div class="flex align-items-center h-3rem">
                                                                        <p-inputSwitch inputId="native-toggle-{{i}}"
                                                                            formControlName="isNative" />
                                                                        <span class="ml-2 text-sm text-600">
                                                                            {{language.get('isNative')?.value ?
                                                                            'Yes' : 'No'}}
                                                                        </span>
                                                                    </div>
                                                                </div>
                                                                <div class="col-12 md:col-2 p-2 flex flex-column">
                                                                    <label
                                                                        class="block font-medium mb-2 text-900 opacity-0">Actions</label>
                                                                    <div class="flex align-items-center h-3rem">
                                                                        <p-button icon="pi pi-trash" [outlined]="true"
                                                                            styleClass="p-button-sm p-button-danger"
                                                                            pTooltip="Remove Language"
                                                                            tooltipPosition="top"
                                                                            [disabled]="isTeachingLanguage(language.get('language')?.value!)"
                                                                            (click)="removeLanguage(i)" />
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </ng-container>
                                        </ng-container>
                                        <div class="field mb-0 col-12">
                                            <app-form-field-validation-message messageClass="text-red-500 mb-2"
                                                [control]="userForm.controls['speakingLanguages']"
                                                propertyName="speakingLanguages" />
                                        </div>
                                    </div>
                                    <div class="flex justify-content-end mt-3">
                                        <p-button label="Add Language" icon="pi pi-plus" [outlined]="true"
                                            styleClass="p-button-sm" (click)="addLanguage()"
                                            [disabled]="speakingLanguages.length >= 10" />
                                    </div>
                                    <div *ngIf="speakingLanguages.errors?.['minlength'] && speakingLanguages.touched"
                                        class="text-red-500 mt-2">
                                        At least one language is required
                                    </div>
                                </div>
                            </div>
                        </p-accordion-content>
                    </p-accordion-panel>
                </p-accordion>
            </div>
            }

            <div id="invalid-controls">
                <app-form-field-validation-message *ngIf="invalidFields().length > 0" [severity]="Severity.Warning"
                    styleClass="mb-2" messageClass="mb-2"
                    [text]="'Please complete all mandatory fields in the form.'"></app-form-field-validation-message>

                <div *ngIf="invalidFields().length > 0" class="invalid-fields-message">
                    <p class="mb-0">The following fields are missing or invalid:</p>
                    <ul class="mt-1 list-none p-0 mx-0">
                        <li *ngFor="let field of invalidFields()" class="flex align-items-center py-1">
                            <span class="border-round bg-red-500 mr-3 flex-shrink-0"
                                style="width: 0.525rem; height: 0.525rem;"></span>
                            <span class="text-sm font-medium text-90">{{ field }}</span>
                        </li>
                    </ul>
                    <hr>
                </div>
            </div>

            <div class="flex flex-column align-items-start justify-content-center">
                <p-button type="submit" icon="pi pi-check-circle" iconPos="left" label="Save Changes"
                [loading]="apiLoadingStateService.getIsLoading()"
                    styleClass="submit-btn mt-2"></p-button>
            </div>

        </form>

        }
        <!-- ends this.displayMode.se -->
    </div>

    <p-confirmdialog />