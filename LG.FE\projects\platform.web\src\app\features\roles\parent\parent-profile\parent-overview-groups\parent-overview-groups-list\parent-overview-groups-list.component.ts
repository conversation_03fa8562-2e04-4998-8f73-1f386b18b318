import { ChangeDetectionStrategy, Component } from '@angular/core';
import { OverviewGroupsComponent } from '@platform.app/features/dashboard/components/overview/overview-groups/overview-groups.component';

@Component({
  selector: 'app-parent-overview-groups-list',
  imports: [
    OverviewGroupsComponent
  ],
  templateUrl: './parent-overview-groups-list.component.html',
  styleUrl: './parent-overview-groups-list.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ParentOverviewGroupsListComponent {
  // No need to call API here - OverviewGroupsComponent handles it
}
