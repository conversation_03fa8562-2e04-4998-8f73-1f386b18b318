import { CommonModule } from '@angular/common';
import {
  Component,
  Input,
  Output,
  EventEmitter,
  signal,
  computed,
  ChangeDetectionStrategy,
  TemplateRef,
  ContentChild,
  OnInit,
  DestroyRef,
  inject,
  effect,
  WritableSignal
} from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { ButtonModule } from 'primeng/button';

/**
 * Represents a filter tag with its display information and removal logic
 */
export interface IAppliedFilterTag {
  /** Unique identifier for the filter */
  id: string;
  /** Display label for the filter */
  label: string;
  /** Optional icon class (PrimeIcons) */
  icon?: string;
  /** Filter type for styling and categorization */
  type: FilterTagType;
  /** Data to pass back when removing the filter */
  removeData?: any;
  /** Whether this filter can be removed */
  removable?: boolean;
  /** Custom CSS classes */
  customClasses?: string;
}

/**
 * Event emitted when a filter tag is removed
 */
export interface IFilterTagRemoveEvent {
  /** The filter tag that was removed */
  filter: IAppliedFilterTag;
  /** Original event if available */
  event?: MouseEvent;
}

/**
 * Configuration for the applied filters component
 */
export interface IAppliedFiltersConfig {
  /** Show clear all button */
  showClearAll?: boolean;
  /** Clear all button label */
  clearAllLabel?: string;
  /** Clear all button icon */
  clearAllIcon?: string;
  /** Header text */
  headerText?: string;
  /** Header icon */
  headerIcon?: string;
  /** Maximum number of tags to show before collapsing */
  maxVisibleTags?: number;
  /** Enable responsive behavior */
  responsive?: boolean;
}

/**
 * Filter tag types for styling and categorization
 */
export type FilterTagType = 
  | 'search' 
  | 'sort' 
  | 'date' 
  | 'select' 
  | 'multiselect' 
  | 'range' 
  | 'boolean' 
  | 'custom';

/**
 * Template context for custom filter tag templates
 */
export interface IFilterTagTemplateContext {
  $implicit: IAppliedFilterTag;
  filter: IAppliedFilterTag;
  remove: (event?: MouseEvent) => void;
}

@Component({
  selector: 'app-applied-filters-tags',
  standalone: true,
  imports: [CommonModule, ButtonModule],
  templateUrl: './applied-filters-tags.component.html',
  styleUrl: './applied-filters-tags.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class AppliedFiltersTagsComponent implements OnInit {
  private destroyRef = inject(DestroyRef);

  // Internal signals for reactive state management
  private _filters = signal<IAppliedFilterTag[]>([]);
  private _config = signal<IAppliedFiltersConfig>({
    showClearAll: true,
    clearAllLabel: 'Clear All',
    clearAllIcon: 'pi pi-filter-slash',
    headerText: 'Applied Filters :',
    headerIcon: 'pi pi-filter',
    maxVisibleTags: undefined,
    responsive: true
  });
  private _showWhenEmpty = signal<boolean>(true);

  /**
   * Array of filter tags to display
   */
  @Input()
  set filters(value: IAppliedFilterTag[]) {
    this._filters.set(value || []);
  }
  get filters(): IAppliedFilterTag[] {
    return this._filters();
  }

  /**
   * Configuration for the component
   */
  @Input()
  set config(value: IAppliedFiltersConfig) {
    this._config.set({ ...this._config(), ...value });
  }
  get config(): IAppliedFiltersConfig {
    return this._config();
  }

  /**
   * Whether to show the component when no filters are applied
   */
  @Input()
  set showWhenEmpty(value: boolean) {
    this._showWhenEmpty.set(value);
  }
  get showWhenEmpty(): boolean {
    return this._showWhenEmpty();
  }

  /**
   * Custom template for filter tags
   */
  @ContentChild('filterTagTemplate') filterTagTemplate?: TemplateRef<IFilterTagTemplateContext>;

  /**
   * Emitted when a single filter is removed
   */
  @Output() filterRemoved = new EventEmitter<IFilterTagRemoveEvent>();

  /**
   * Emitted when clear all is clicked
   */
  @Output() clearAllClicked = new EventEmitter<MouseEvent>();

  /**
   * Computed property to determine if component should be visible
   */
  readonly isVisible = computed(() => {
    const hasFilters = this._filters().length > 0;
    return hasFilters || this._showWhenEmpty();
  });

  /**
   * Computed property for visible filters (considering max visible limit)
   */
  readonly visibleFilters = computed(() => {
    const allFilters = this._filters();
    const maxVisible = this._config().maxVisibleTags;

    if (maxVisible && allFilters.length > maxVisible) {
      return allFilters.slice(0, maxVisible);
    }

    return allFilters;
  });

  /**
   * Computed property for hidden filters count
   */
  readonly hiddenFiltersCount = computed(() => {
    const allFilters = this._filters();
    const maxVisible = this._config().maxVisibleTags;

    if (maxVisible && allFilters.length > maxVisible) {
      return allFilters.length - maxVisible;
    }

    return 0;
  });

  /**
   * Computed property to check if clear all should be shown
   */
  readonly showClearAll = computed(() => {
    return this._config().showClearAll && this._filters().length > 0;
  });

  constructor() {

    // Effect to react to configuration changes
    effect(() => {
      const config = this._config();
      console.debug('Filter component configuration updated', config);
    }, { allowSignalWrites: false });
  }

  ngOnInit(): void {
    // Component initialization complete
    console.debug('Applied Filters Tags Component initialized');
  }

  /**
   * Handles removal of a single filter tag
   */
  onFilterRemove(filter: IAppliedFilterTag, event?: MouseEvent): void {
    if (event) {
      event.stopPropagation();
    }

    if (filter.removable !== false) {
      this.filterRemoved.emit({
        filter,
        event
      });
    }
  }

  /**
   * Handles clear all button click
   */
  onClearAll(event: MouseEvent): void {
    event.stopPropagation();
    this.clearAllClicked.emit(event);
  }

  /**
   * Track by function for filter tags
   */
  trackByFilterId(_index: number, filter: IAppliedFilterTag): string {
    return filter.id;
  }

  /**
   * Reactive method to add a filter programmatically
   */
  addFilter(filter: IAppliedFilterTag): void {
    const currentFilters = this._filters();
    const existingIndex = currentFilters.findIndex(f => f.id === filter.id);

    if (existingIndex === -1) {
      this._filters.set([...currentFilters, filter]);
    } else {
      // Update existing filter
      const updatedFilters = [...currentFilters];
      updatedFilters[existingIndex] = filter;
      this._filters.set(updatedFilters);
    }
  }

  /**
   * Reactive method to remove a filter programmatically
   */
  removeFilterById(filterId: string): void {
    const currentFilters = this._filters();
    const updatedFilters = currentFilters.filter(f => f.id !== filterId);
    this._filters.set(updatedFilters);
  }

  /**
   * Reactive method to clear all filters programmatically
   */
  clearAllFilters(): void {
    this._filters.set([]);
  }

  /**
   * Reactive method to update configuration
   */
  updateConfig(partialConfig: Partial<IAppliedFiltersConfig>): void {
    this._config.update(current => ({ ...current, ...partialConfig }));
  }

  /**
   * Get current filters as a signal (read-only)
   */
  getFiltersSignal() {
    return this._filters.asReadonly();
  }

  /**
   * Get current config as a signal (read-only)
   */
  getConfigSignal() {
    return this._config.asReadonly();
  }
}
