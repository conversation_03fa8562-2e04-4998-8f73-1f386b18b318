import { CommonModule } from '@angular/common';
import {
  ChangeDetectionStrategy,
  Component,
  computed,
  DestroyRef,
  EventEmitter,
  inject,
  Inject,
  Injector,
  input,
  Input,
  linkedSignal,
  model,
  Output,
  signal
} from '@angular/core';
import { takeUntilDestroyed, toObservable } from '@angular/core/rxjs-interop';
import {
  IStudentGroupDto,
  ITeachingLanguageDto,
  ILanguageLevelsEnum,
  ICreateStudentGroupResponse,
  IEditStudentGroupResponse,
  StudentGroupRoutes,
  IApiResponseBase,
  IGetStudentGroupResponse,
  ICreateStudentGroupRequest,
  IEditStudentGroupRequest,
  IStudentLevelEnum,
  IGetStudentDashboardResponse,
  IStudentGroupStatusEnum,
  IStudentTeachingLanguageDto,
  IGetProfileInfoResponse,
  IBasicProfileInfoDto,
  ISearchStudentDto,
  GroupDialogState
} from 'SharedModules.Library';
import { Subscription, Observable } from 'rxjs';
import { untilDestroyed } from 'SharedModules.Library';
import { Severity } from 'SharedModules.Library';

// Service Imports
import { CarouselService } from '@platform.src/app/core/services/carousel.service';
import { DataApiStateService, State } from 'SharedModules.Library';
import { EventBusService, EmitEvent, Events } from 'SharedModules.Library';
import { GeneralService } from 'SharedModules.Library';
import { StudentGroupService, StoudentGroupRuleMessage } from '@platform.src/app/core/services/student-group.service';
import { UserService } from '@platform.src/app/core/services/user.service';

// Component Imports
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { AccordionModule } from 'primeng/accordion';
import { ButtonModule } from 'primeng/button';
import { CarouselModule } from 'primeng/carousel';
import { DropdownModule } from 'primeng/dropdown';

import { ImageBoxGroupSelectComponent } from '../../../image-box-group-select/image-box-group-select.component';
import { FormFieldValidationMessageComponent } from 'SharedModules.Library';
import { ToastMessages, ToastService } from 'SharedModules.Library';
import { HandleApiResponseService } from 'SharedModules.Library';
import { AuthStateService } from 'SharedModules.Library';
import { PrimeStudentsSelectionComponent } from '@platform.app/shared/components/prime/prime-students-selection/prime-students-selection.component';

@Component({
  selector: 'app-student-group-selection-members-step',
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    DropdownModule,
    ButtonModule,
    AccordionModule,
    CarouselModule,
    ImageBoxGroupSelectComponent,
    FormFieldValidationMessageComponent,
    PrimeStudentsSelectionComponent
  ],
  templateUrl: './student-group-selection-members-step.component.html',
  styleUrl: './student-group-selection-members-step.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class StudentGroupSelectionMembersStepComponent {
  // Outputs
  @Output() groupStateChanged: EventEmitter<GroupDialogState> = new EventEmitter();
  @Output() groupItemChanged: EventEmitter<unknown> = new EventEmitter();
  @Output() onMembersStepSubmitted: EventEmitter<Partial<ICreateStudentGroupRequest>> = new EventEmitter();

  // Constants and Inputs
  readonly EditGroupState = GroupDialogState;
  editMode = input(false);
  editGroupState = input(this.EditGroupState.None);

  // Signals
  step = signal(1);
  studentGroupItem = signal({} as IStudentGroupDto);
  editMembersMode = signal(false);
  canEditGroup = signal(false);
  canGoBack = signal(false);
  selectedStudents = signal([] as ISearchStudentDto[]);
  selectedTeachingLanguage$ = signal<ITeachingLanguageDto | null>(null);
  selectedLanguageLevel$ = signal<any | null>(null);
  resetSelectionSignal = signal(false);
  ruleMessageDisplay = signal([] as StoudentGroupRuleMessage[]);

  // Services
  generalService = inject(GeneralService);
  authService = inject(AuthStateService);
  userService = inject(UserService);
  apiService = inject(HandleApiResponseService);
  studentGroupService = inject(StudentGroupService);
  dataStateService = inject(DataApiStateService);
  toastService = inject(ToastService);
  eventBusService = inject(EventBusService);
  carouselService = inject(CarouselService);
  destroy: DestroyRef = inject(DestroyRef);
  injector = inject(Injector);

  // Properties
  Severity = Severity;
  selectedTeachingLanguage = {} as ITeachingLanguageDto;
  preselectedStudents = [] as IBasicProfileInfoDto[];
  filteredStudents = [] as any[];
  preselectedLevel = [];
  studentGroups = [] as IStudentGroupDto[];
  studentsToAdd: string[] = [];
  studentsToRemove: string[] = [];
  subscriptions: Subscription[] = [];
  parameters: any;
  untilDestroyed = untilDestroyed();
  #userToSignal = this.authService.userDecodedJWTData$;

  // Computed Properties
  user = computed(() => {
    console.log(this.#userToSignal());
    return this.#userToSignal();
  });

  // Corrected to access the 'students' array directly from pageData
  studentsa$ = linkedSignal(() => this.dataStateService.parentStudents.state().data?.pageData as ISearchStudentDto[] || []);
  teachingLanguages$ = computed(() => this.computeTeachingLanguages());
  studentLevels$ = computed(() => this.computeStudentLevels());
  studentGroups$ = computed(() => this.dataStateService.parentStudentsGroups.state() as State<IStudentGroupDto[]>);
  createStudentGroup$ = computed(() => this.dataStateService.createStudentGroup.state() as State<ICreateStudentGroupResponse> || {} as State<ICreateStudentGroupResponse>);
  shouldDisableCreateButton = computed(() => this.computeCreateButtonState());
  shouldDisableEditSaveChangesButton = computed(() => this.computeEditSaveButtonState());

  constructor(@Inject('dialogParameters') parameters: any) {
    this.parameters = parameters;
  }

  // Lifecycle Methods
  ngOnInit(): void {
    this.initializeComponent();
    this.setupSubscriptions();
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach(sub => sub.unsubscribe());
    this.generalService.setErrorDataSignal('');
  }

  // Public Event Handlers
  onLanguageSelected(event: ITeachingLanguageDto): void {
    this.selectedTeachingLanguage = event;
    this.selectedTeachingLanguage$.set(event);
    this.resetSelectedStudents();
    this.filteredStudents = this.getFilteredStudents();
    // IMPORTANT: Reset selected language level when language changes
    // This forces studentLevels$ to re-evaluate and update the dropdown.
    this.selectedLanguageLevel$.set(null);
  }

  onLevelSelected(event: any): void {
    this.selectedLanguageLevel$.set(event);
    this.resetSelectedStudents();
    this.filteredStudents = this.getFilteredStudents();
  }

  onStudentSelected(event: ISearchStudentDto | ISearchStudentDto[]): void {
    this.handleStudentSelection(event as ISearchStudentDto[]);
  }

  // Public Methods
  createNewGroup(): void {
    if (this.shouldDisableCreateButton()) return;
    this.createStudentGroup();
  }

  editGroupSave(): void {
    this.saveEditedGroup();
  }

  getFilteredStudents(): unknown[] {
    const students = this.studentsa$(); // Use the corrected studentsa$ signal
    const selectedLanguage = this.selectedTeachingLanguage;
    const selectedLevel = this.selectedLanguageLevel$();

    if (!selectedLanguage || selectedLevel === null) {
      // If no language or level is selected, return all students as not disabled initially
      return students.map((student: ISearchStudentDto) => ({ ...student, disabled: false }));
    }

    return students
      .map((student: ISearchStudentDto) => this.mapAndFilterStudent(student))
      .sort(this.studentGroupService.sortStudents)
      .map((student: ISearchStudentDto) => this.removeMatchProperty(student));
  }

  areAllStudentLevelsDisabled(): boolean {
    return this.studentLevels$().every(level => level.disabled === true);
  }

  // Private Methods
  private initializeComponent(): void {
    this.studentGroupItem.set(this.parameters.studentGroupItem);
    if (this.editMode()) {
      this.initializeEditMode();
      // Validate students is called here, after setting up the initial state in edit mode
      // It will use the initially selected students from the group item.
      this.validateStudents();
    }
    this.initEvents();
  }

  private setupSubscriptions(): void {
    this.subscribeToStudentGroups();
    this.subscribeToTeachingLanguages();
    this.subscribeToStudents();
    this.subscribeToEditGroupState();
  }

  private computeStudents() {
    const students = this.studentsa$(); // Use the corrected studentsa$ signal
    const selectedLanguage = this.selectedTeachingLanguage$();
    const selectedLevel = this.selectedLanguageLevel$();

    if (!selectedLanguage || selectedLevel === null) {
      return students.map((student: ISearchStudentDto) => ({ ...student, disabled: false }));
    }

    const result = students
      .map((student: ISearchStudentDto) => this.mapStudentWithMatch(student))
      .sort((a: ISearchStudentDto, b: ISearchStudentDto) => this.sortStudentsByMatch(a, b))
      .map((student: ISearchStudentDto) => this.removeMatchProperty(student))

    return result;
  }

  private computeTeachingLanguages() {
    const teachingLanguages = this.dataStateService.teachingLanguages.state()?.data?.teachingLanguages || [];
    const students = this.studentsa$(); // Use the corrected studentsa$ signal
    const studentsLanguageIds = new Set(students.flatMap((student: ISearchStudentDto) =>
      student.studentTeachingLanguageDto!.map((lang: IStudentTeachingLanguageDto) => lang.teachingLanguageId)
    ));

    return teachingLanguages.sort((a: ITeachingLanguageDto, b: ITeachingLanguageDto) => {
      const aHasStudents = studentsLanguageIds.has(a.id!);
      const bHasStudents = studentsLanguageIds.has(b.id!);
      return (aHasStudents === bHasStudents) ? 0 : (aHasStudents ? -1 : 1);
    });
  }

  private computeStudentLevels() {
    const allPossibleLevels = Object.keys(ILanguageLevelsEnum)
      .filter(key => isNaN(Number(key))) // Filters out the numeric values, keeps string keys
      .map(key => ({
        name: key,
        code: ILanguageLevelsEnum[key as keyof typeof ILanguageLevelsEnum] // This 'code' will be the numeric value
      }));

    const students = this.studentsa$();
    const selectedLanguage = this.selectedTeachingLanguage$();

    if (!students || students.length === 0) {
      console.log('No students loaded or students array is empty, disabling all levels.');
      return allPossibleLevels.map(level => ({ ...level, disabled: true }));
    }

    const availableLevelsForSelection = new Set<ILanguageLevelsEnum>();

    students.forEach(student => {
      student.studentTeachingLanguageDto?.forEach(langDto => {
        // Ensure selectedLanguage.id and langDto.teachingLanguageId are comparable types (e.g., both strings or both numbers)
        const languageIdMatches = !selectedLanguage || String(langDto.teachingLanguageId) === String(selectedLanguage.id);

        if (languageIdMatches && langDto.languageLevel !== undefined && langDto.languageLevel !== null) {
          // Attempt to convert the languageLevel from DTO to the ILanguageLevelsEnum numeric value
          // Assuming langDto.languageLevel comes as a string like "Beginner", "Intermediate"
          // and ILanguageLevelsEnum is a numeric enum (Beginner = 0, Intermediate = 1)
          const enumValue = ILanguageLevelsEnum[langDto.languageLevel];

          if (typeof enumValue === 'number') { // Ensure it's a valid enum number
            availableLevelsForSelection.add(enumValue);
          } else {
            // Fallback if the DTO level string doesn't match an enum key exactly
            // You might log a warning here if this happens frequently.
            console.warn(`Could not map DTO languageLevel '${langDto.languageLevel}' to ILanguageLevelsEnum.`);
          }
        }
      });
    });

    const levelsWithDisabledStatus = allPossibleLevels.map(level => ({
      ...level,
      disabled: !availableLevelsForSelection.has(level.code)
    }));

    // Log the final status for debugging
    console.log('Final Student Levels with disabled status:', levelsWithDisabledStatus);

    return this.studentGroupService.sortLanguageLevels(levelsWithDisabledStatus, students);
  }

  private computeCreateButtonState(): boolean {
    const selectedStudents = this.selectedStudents();
    const selectedLanguage = this.selectedTeachingLanguage$();
    return this.ruleMessageDisplay().length > 0 ||
      selectedStudents.length === 0 ||
      !selectedLanguage;
  }

  private computeEditSaveButtonState(): boolean {
    return this.ruleMessageDisplay().length > 0 ||
      (this.studentGroupService.studentsToAdd.length === 0 &&
        this.studentGroupService.studentsToRemove.length === 0);
  }

  // The refactored method
  private handleStudentSelection(event: ISearchStudentDto[]): void {
    // 1. Clear previous validation messages
    this.resetRuleMessages();
    console.log('onStudentSelected event:', event);

    // 2. Update the internal state of selected students
    this.updateSelectedStudents(event);

    // 3. Inform the StudentGroupService about the selection (important for internal tracking like studentsToAdd/ToRemove)
    if (this.editMode() && this.studentGroupItem()) {
      // Retrieve the actual ISearchStudentDto objects for the preselected students
      const initialStudentsInGroup: ISearchStudentDto[] = (this.studentGroupItem().studentInfo || [])
        .map(bp => {
          // Find the full ISearchStudentDto from the available students using userId
          return this.studentsa$().find(student => student.userId === bp.userId);
        })
        .filter((student): student is ISearchStudentDto => student !== undefined); // Ensure it's not undefined/null

      this.studentGroupService.onStudentSelected(event, initialStudentsInGroup);
    } else {
      // For new groups, no preselected students
      this.studentGroupService.onStudentSelected(event, []);
    }

    // 4. Validate the current selection and update UI messages
    this.validateStudents();

    // 5. Clear any general error data (assuming this is for other error states)
    this.generalService.setErrorDataSignal('');

    console.log('Updated selectedStudents:', this.selectedStudents());
  }

  private createStudentGroup(): void {
    this.studentGroupService.updateCreateStudentGroupRequest({
      parentId: this.user()!.id,
      studentsToAdd: this.getStudentIds(),
      teachingLanguageId: this.selectedTeachingLanguage$()!.id,
    });
    this.onMembersStepSubmitted.emit();
  }

  private saveEditedGroup(): void {
    this.apiService.getApiData<IEditStudentGroupResponse>(
      { url: StudentGroupRoutes.patchEditStudentGroup, method: 'PATCH' },
      {
        parentId: this.user()?.id,
        groupId: this.studentGroupItem().id,
        studentLevel: IStudentLevelEnum.NoExperience,
        moreDetails: "",
        studentsToAdd: this.studentGroupService.studentsToAdd,
        studentsToRemove: this.studentGroupService.studentsToRemove,
      } as IEditStudentGroupRequest
    )
      .pipe(this.untilDestroyed())
      .subscribe({
        next: this.handleEditGroupSuccess,
        error: this.handleCreateStudentGroupError
      });
  }

  private subscribeToStudentGroups(): void {
    toObservable(this.studentGroups$, { injector: this.injector })
      .pipe(takeUntilDestroyed(this.destroy))
      .subscribe({
        next: (stateData: any) => this.handleStudentGroupsUpdate(stateData)
      });
  }

  private subscribeToEditGroupState(): void {
    toObservable(this.editGroupState, { injector: this.injector })
      .pipe(takeUntilDestroyed(this.destroy))
      .subscribe({
        next: (editGroupState: GroupDialogState) => {
          if (editGroupState === GroupDialogState.AfterEditSuccess) {
            this.groupItemChanged.emit(this.studentGroupItem());
          }
        }
      });
  }

  private subscribeToTeachingLanguages(): void {
    toObservable(this.teachingLanguages$, { injector: this.injector })
      .pipe(takeUntilDestroyed(this.destroy))
      .subscribe({
        next: (teachingLanguages: any) => {
          if (teachingLanguages.length > 0 && this.editMode()) {
            this.handleTeachingLanguagesInEditMode(teachingLanguages);
          }
        }
      });
  }

  private subscribeToStudents(): void {
    toObservable(this.studentsa$, { injector: this.injector })
      .pipe(takeUntilDestroyed(this.destroy))
      .subscribe({
        next: (students: ISearchStudentDto[]) => {
          // Re-evaluate student levels and filtered students when the student list changes
          // The `studentLevels$` computed property will naturally re-run because `studentsa$` changed.
          this.filteredStudents = this.getFilteredStudents(); // Ensure filtered students is up-to-date
        }
      });
  }

  private initializeEditMode(): void {
    this.step.set(2);
    const studentGroupItem = this.parameters.studentGroupItem;
    console.log("🚀 ~ initializeEditMode ~ studentGroupItem:", studentGroupItem);

    this.setEditModeLanguage(studentGroupItem);
    this.setEditModeLevel(studentGroupItem);
    this.setEditModeStudents(studentGroupItem);
    this.filteredStudents = this.getFilteredStudents();
  }

  private validateStudents(): void {
    const selectedStudents = this.selectedStudents();
    // Explicitly map ISearchStudentDto[] to IBasicProfileInfoDto[]
    const basicProfileInfoStudents: IBasicProfileInfoDto[] = selectedStudents.map(student => ({
      userId: student.userId, // Map ISearchStudentDto.id to IBasicProfileInfoDto.userId
      loginUsername: student.loginUsername,
      firstName: student.firstName,
      lastName: student.lastName,
      discriminator: '', // Provide a default or derive if needed
      timeZoneIana: student.timeZoneIana,
      timeZoneDisplayName: student.timeZoneDisplayName,
      hasCompletedOnBoardingTutorial: false, // Provide a default or derive
      hasSetupPassword: student.hasSetupPassword,
    }));

    const newGroup = {
      id: "-1", // Temporary ID for validation, or use existing group ID if in edit mode
      groupName: this.selectedTeachingLanguage$()?.name!,
      groupLevel: this.selectedLanguageLevel$()?.code,
      studentInfo: basicProfileInfoStudents, // Use the mapped array here
      groupStatus: IStudentGroupStatusEnum.Active,
    } as IStudentGroupDto; // Now this cast should be safer

    const ruleMessages = this.studentGroupService.validateSelectedStudents(
      this.studentGroups,
      newGroup,
      this.editMode()
    );
    this.ruleMessageDisplay.set(ruleMessages);
  }

  private mapAndFilterStudent(student: ISearchStudentDto): any {
    const hasMatchingLanguage = student.studentTeachingLanguageDto!.some((lang: any) =>
      lang.teachingLanguageId === this.selectedTeachingLanguage.id &&
      lang.languageLevel === this.selectedLanguageLevel$().code
    );

    const isSelected = this.selectedStudents().some(
      s => s.userId === student.userId
    );

    return {
      ...student,
      disabled: !hasMatchingLanguage,
      selected: isSelected && hasMatchingLanguage,
      match: hasMatchingLanguage
    };
  }

  private mapStudentWithMatch(student: ISearchStudentDto) {
    const hasMatchingLanguage = student.studentTeachingLanguageDto!.some((lang: any) =>
      lang.teachingLanguageId === this.selectedTeachingLanguage$()!.id &&
      lang.languageLevel === this.selectedLanguageLevel$()!.code // Corrected typo here previously
    );

    return {
      ...student,
      disabled: !hasMatchingLanguage,
      match: hasMatchingLanguage
    };
  }

  private sortStudentsByMatch(a: any, b: any): number {
    if (a.match && !b.match) return -1;
    if (!a.match && b.match) return 1;
    return 0;
  }

  private removeMatchProperty(student: any): any {
    const { match, ...rest } = student;
    return rest;
  }

  private handleStudentGroupsUpdate(stateData: State<IGetStudentGroupResponse>): void {
    console.log(stateData);
    if (stateData.data?.studentGroups) {
      if (this.editMode()) {
        this.studentGroupItem.set(
          this.studentGroupService.findGroupById(stateData.data.studentGroups, this.studentGroupItem().id) as IStudentGroupDto
        );
      }
      this.studentGroups = stateData.data.studentGroups;
    }
  }

  private handleTeachingLanguagesInEditMode(teachingLanguages: ITeachingLanguageDto[]): void {
    const languageObject = this.studentGroupService.getTeachingLanguageObjectFromName(
      teachingLanguages,
      this.studentGroupItem().groupName!
    );
    this.selectedTeachingLanguage = languageObject as ITeachingLanguageDto;
    this.selectedTeachingLanguage$.set(languageObject as ITeachingLanguageDto); // Ensure signal is updated too
    this.filteredStudents = this.getFilteredStudents();
  }

  private setEditModeLanguage(studentGroupItem: IStudentGroupDto): void {
    const languageObject = this.studentGroupService.getTeachingLanguageObjectFromName(
      this.teachingLanguages$(),
      studentGroupItem.groupName!
    );
    // Use onLanguageSelected to ensure all related logic (like resetting levels) is triggered
    this.onLanguageSelected(languageObject as ITeachingLanguageDto);
  }

  private setEditModeLevel(studentGroupItem: IStudentGroupDto): void {
    // Use onLevelSelected to ensure all related logic is triggered
    this.onLevelSelected({
      name: this.generalService.getILanguageLevelsEnumText(studentGroupItem.groupLevel),
      code: studentGroupItem.groupLevel,
      disabled: false
    });
  }

  private setEditModeStudents(studentGroupItem: IStudentGroupDto): void {
    this.preselectedStudents = studentGroupItem.studentInfo || [];
    console.log("🚀 ~ initializeEditMode ~ preselectedStudents:", this.preselectedStudents);

    if (this.preselectedStudents.length > 0) {
      // Correctly map IBasicProfileInfoDto to ISearchStudentDto for selectedStudents signal
      const selectedStudents = this.preselectedStudents
        .map(preselected => {
          // Find the full ISearchStudentDto from studentsa$ based on userId
          // Ensure studentsa$ has loaded its data before trying to find students
          return this.studentsa$().find(
            (student: ISearchStudentDto) => student.userId === preselected.userId
          ) || null;
        })
        .filter(student => student !== null) as ISearchStudentDto[];
      this.selectedStudents.set(selectedStudents);
      console.log("🚀 ~ initializeEditMode ~ selectedStudents:", this.selectedStudents());
    } else {
      this.selectedStudents.set([]);
    }
  }

  private findMatchingStudent(preselected: IBasicProfileInfoDto) {
    return this.studentsa$().find(
      (student: ISearchStudentDto) => student.userId === preselected.userId // Use userId for comparison
    ) || null;
  }

  private handleCreateGroupSuccess = (): void => {
    this.groupStateChanged.emit(this.EditGroupState.AfterCreateSuccess);
    this.toastService.show(ToastMessages.StudentsGroupAdd.success);
    this.eventBusService.emit(new EmitEvent(Events.StudentGroupAdded, { name: 'Group 1' }));
  }

  private handleEditGroupSuccess = (): void => {
    this.resetAfterEdit();
    this.eventBusService.emit(new EmitEvent(Events.StudentGroupEdited, undefined));
    this.toastService.show(ToastMessages.StudensGroupEdit.success);
    this.groupItemChanged.emit(this.studentGroupItem());
    this.groupStateChanged.emit(this.EditGroupState.AfterEditSuccess);
  }

  private handleCreateStudentGroupError = (error: IApiResponseBase): void => {
    console.error(error);
    if (error.statusCode === 400) {
      // Handle 400 error
    }
    this.generalService.handleRouteError(error, StudentGroupRoutes.postCreateStudentGroup);
  }

  private getStudentIds(): string[] {
    return this.selectedStudents().map(student => student.userId);
  }

  private updateSelectedStudents(students: ISearchStudentDto[]): void {
    this.selectedStudents.set(students.filter(student => student && student?.userId));
    console.log('Set selectedStudents to:', this.selectedStudents());
  }

  private resetSelectedStudents(): void {
    this.selectedStudents.set([]);
    this.preselectedStudents = [];
    this.resetSelectionSignal.set(!this.resetSelectionSignal());
  }

  private initEvents(): void {
    this.eventBusService.emit(new EmitEvent(Events.StateLoadTeachingLanguages, undefined));
    this.subscribeToEvents();
  }

  private resetRuleMessages(): void {
    this.ruleMessageDisplay.set([]);
  }

  private resetAfterEdit(): void {
    this.studentGroupService.studentsToAdd = [];
    this.studentGroupService.studentsToRemove = [];
    this.resetSelectedStudents();
  }

  private subscribeToEvents(): void {
    const subscriptions = [
      this.eventBusService.on(Events.StudentGroupAdded, this.handleStudentGroupAdded),
      this.eventBusService.on(Events.StudentGroupRemoved, this.handleStudentGroupRemoved),
      this.eventBusService.on(Events.StudentGroupNewMemberAdded, this.handleStudentGroupNewMemberAdded)
    ];
    this.subscriptions.push(...subscriptions);
  }

  private handleStudentGroupAdded = (payload: any): void => {
    console.log(`Customer Selected: ${payload.name}`);
  }

  private handleStudentGroupRemoved = (payload: any): void => {
    console.log(Events.StudentGroupRemoved, `: ${payload}`);
  }

  private handleStudentGroupNewMemberAdded = (payload: any): void => {
    console.log(Events.StudentGroupNewMemberAdded, `: ${payload}`);
  }
}