import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, inject, Input, model, signal, type OnInit } from '@angular/core';
import { Router, RouterModule } from '@angular/router';
import { ButtonModule } from 'primeng/button';
import { InputTextModule } from 'primeng/inputtext';
// import { IdentityRoutes, TeachingLanguagesRoutes } from '@GeneratedTsFiles/index';
// import { AuthStateService } from '../../../../../LG.FE.PLATFORM/projects/lg-fe-shared/src/lib/services/auth-state.service';
import { HttpClient, provideHttpClient } from '@angular/common/http';
// import { HandleApiService } from 'src/app/core/services/handle-api.service';
import { FormBuilder, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { environment } from '../../../environments/environment';
import { AuthStateService, EmitEvent, EnvironmentService, EventBusService, Events, FormFieldValidationMessageComponent, HandleApiResponseService, ILoginRequest, ILoginResponse, ILoginTypeEnum } from 'SharedModules.Library';
import { IdentityRoutes } from 'SharedModules.Library';
import { CheckboxModule } from 'primeng/checkbox';
import { RippleModule } from 'primeng/ripple';
import { IconFieldModule } from 'primeng/iconfield';
import { InputIconModule } from 'primeng/inputicon';
@Component({
  selector: 'app-login',
  imports: [
    CommonModule,
    ButtonModule,
    InputTextModule,
    RouterModule,
    FormsModule,
    ReactiveFormsModule,
    CheckboxModule,
    RippleModule,
    InputIconModule,
    IconFieldModule,
    FormFieldValidationMessageComponent,
  ],
  providers: [],
  templateUrl: './login.component.html',
  styleUrl: './login.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class LoginComponent implements OnInit {
  router = inject(Router);
  authStateService = inject(AuthStateService);
  handleApiService = inject(HandleApiResponseService);
  eventBusService = inject(EventBusService);
  env = environment.apiUrl;
  http = inject(HttpClient);
  loginForm: FormGroup = new FormGroup({});
  fb = inject(FormBuilder);
  isLoading = signal(false);
  rememberMe = model(false);
  passwordVisible = signal(false);

  constructor(private enviromentService: EnvironmentService) { }

  ngOnInit(): void {
    console.log(this.enviromentService.apiUrl);
    this.loginForm = this.fb.group({
      email: ['', [Validators.required, Validators.email]],
      password: ['', [Validators.required]]
    });
  }

  login() {
    if (this.loginForm.valid) {
      this.isLoading.set(true);
      const formData = this.loginForm.value;

      const loginRequest: ILoginRequest = {
        emailAddress: formData.email,
        password: formData.password,
        googleIdToken: '',
        loginType: ILoginTypeEnum.UsernameAndPassword
      };
      this.handleApiService.getApiData<ILoginResponse>(
        {
          url: IdentityRoutes.postLogin,
          method: 'POST',
        },
        loginRequest
      ).subscribe({
        next: (response) => {
          this.authStateService.handleUserDataAndDecodeJWT(response);
          this.router.navigate(['/dashboard/overview']);
        },
        error: (error) => {
          console.error('Login failed:', error);
          this.isLoading.set(false);
        }
      });
    }
  }

  togglePasswordVisibility() {
    this.passwordVisible.update(v => !v);
  }

}
