import { inject } from "@angular/core";
import { CanActivateFn, ActivatedRouteSnapshot, RouterStateSnapshot, Router, CanActivateChildFn, CanActivate, UrlTree } from "@angular/router";
import { map, catchError, Observable, take } from "rxjs";
import { AuthStateService } from "SharedModules.Library";
export const AuthGuard: CanActivateFn = (
  route: ActivatedRouteSnapshot,
  state: RouterStateSnapshot
):
  Observable<boolean | UrlTree>
  | Promise<boolean | UrlTree>
  | boolean
  | UrlTree => {

  const router = inject(Router);
  const authService = inject(AuthStateService);
  const isAuth = authService.getUserClaims();

  console.log(isAuth);
  return isAuth
    ? true
    : inject(Router).createUrlTree(['/auth/login']);

};
