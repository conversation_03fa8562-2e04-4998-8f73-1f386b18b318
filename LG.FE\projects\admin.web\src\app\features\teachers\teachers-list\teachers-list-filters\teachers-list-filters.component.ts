import { CommonModule } from '@angular/common';
import {
  Component,
  Input,
  Output,
  EventEmitter,
  signal,
  computed,
  ChangeDetectionStrategy,
  OnInit,
  OnChanges,
  DestroyRef,
  inject
} from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { FormsModule } from '@angular/forms';
import { DropdownModule } from 'primeng/dropdown';
import { MultiSelectModule } from 'primeng/multiselect';
import { CheckboxModule } from 'primeng/checkbox';
import { ButtonModule } from 'primeng/button';
import { SliderModule } from 'primeng/slider';
import { DatePickerModule } from 'primeng/datepicker';
import {
  IGetTeachersRequest,
  ITeachingLanguageDto,
  IAvailabilityStatusOptionsEnum,
  ITeacherStudentAgesExperienceEnum,
  ITeacherStudentAgesPreferenceEnum,
  nameOf,
  EnumDropdownOptionsService,
  GeneralService
} from 'SharedModules.Library';
import { TeacherListComponentHelperService } from '../../../../shared/services/teacher-list-component-helper.service';
import { DateRangeFilterService } from '../../../../shared/services/date-range-filter.service';

/**
 * Interface for filter change events
 */
export interface ITeachersFilterChangeEvent {
  filterName: keyof IGetTeachersRequest;
  value: any;
  resetPage?: boolean;
}

/**
 * Interface for filter action events
 */
export interface ITeachersFilterActionEvent {
  action: 'search' | 'reset';
  filters: IGetTeachersRequest;
}

/**
 * Interface for filter state data passed from parent
 */
export interface ITeachersFilterState {
  queryParams: IGetTeachersRequest;
  studentAgesRange: number[];
  selectedAvailabilityStatuses: IAvailabilityStatusOptionsEnum[];
  selectedTeachingAgesExperience: ITeacherStudentAgesExperienceEnum[];
  selectedTeacherStudentAgesPreference: ITeacherStudentAgesPreferenceEnum[];
  teachingLanguages: ITeachingLanguageDto[];
  nativeLanguages: string[];
  isFilterOpen: boolean;
}

/**
 * Interface for filter configuration
 */
export interface ITeachersFilterConfig {
  showToggleButton?: boolean;
  defaultOpen?: boolean;
  enableAutoSearch?: boolean;
  searchDebounceMs?: number;
}

@Component({
  selector: 'app-teachers-list-filters',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    DropdownModule,
    MultiSelectModule,
    CheckboxModule,
    ButtonModule,
    SliderModule,
    DatePickerModule
  ],
  templateUrl: './teachers-list-filters.component.html',
  styleUrl: './teachers-list-filters.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class TeachersListFiltersComponent implements OnInit, OnChanges {
  private destroyRef = inject(DestroyRef);
  enumDropdownOptionsService = inject(EnumDropdownOptionsService);
  private generalService = inject(GeneralService);
  private dateRangeFilterService = inject(DateRangeFilterService);

  /**
   * Filter state data from parent component
   */
  @Input() filterState: ITeachersFilterState = {
    queryParams: {} as IGetTeachersRequest,
    studentAgesRange: [2, 17],
    selectedAvailabilityStatuses: [],
    selectedTeachingAgesExperience: [],
    selectedTeacherStudentAgesPreference: [],
    teachingLanguages: [],
    nativeLanguages: [],
    isFilterOpen: true
  };

  /**
   * Filter configuration
   */
  @Input() config: ITeachersFilterConfig = {
    showToggleButton: true,
    defaultOpen: true,
    enableAutoSearch: false,
    searchDebounceMs: 400
  };

  /**
   * Emitted when a filter value changes
   */
  @Output() filterChanged = new EventEmitter<ITeachersFilterChangeEvent>();

  /**
   * Emitted when filter actions are triggered (search/reset)
   */
  @Output() filterAction = new EventEmitter<ITeachersFilterActionEvent>();

  /**
   * Emitted when filter panel is toggled
   */
  @Output() filterToggled = new EventEmitter<boolean>();

  // === CLEAN TEMPORARY FILTER STATE ===
  private _tempFilters = signal<Partial<IGetTeachersRequest>>({});
  private _isFilterOpen = signal<boolean>(true);

  // Computed properties that merge temp + current state
  readonly isFilterOpen = computed(() => this._isFilterOpen());
  readonly currentFilters = computed(() => ({ ...this.filterState.queryParams, ...this._tempFilters() }));

  // Derived computed properties from merged state
  readonly studentAgesRange = computed(() => [
    this.currentFilters().studentAgesMin || 2,
    this.currentFilters().studentAgesMax || 17
  ]);

  readonly selectedAvailabilityStatuses = computed(() =>
    this.generalService.convertFlagsToArray(
      this.currentFilters().availabilityStatus || 0,
      this.enumDropdownOptionsService.availabilityStatusEnumFlagsOptions
    )
  );

  readonly selectedTeachingAgesExperience = computed(() =>
    this.generalService.convertFlagsToArray(
      this.currentFilters().teachingAgesExperience || 0,
      this.enumDropdownOptionsService.teachingAgesExperienceEnumFlagsOptions
    )
  );

  readonly selectedTeacherStudentAgesPreference = computed(() =>
    this.generalService.convertFlagsToArray(
      this.currentFilters().teacherStudentAgesPreference || 0,
      this.enumDropdownOptionsService.teacherStudentAgesPreferenceEnumFlagsOptions
    )
  );

  // Field names for type safety
  readonly getTeachersRequestFieldNames = nameOf<IGetTeachersRequest>();

  ngOnInit(): void {
    console.debug('Teachers List Filters Component initialized');

    // Initialize internal state with input state
    this.syncInternalState();
  }

  ngOnChanges(): void {
    // Sync internal state when input changes
    this.syncInternalState();
  }

  private syncInternalState(): void {
    this._isFilterOpen.set(this.filterState.isFilterOpen);
    // Clear temp filters to show current state
    this._tempFilters.set({});
  }



  /**
   * Handles all filter changes - stores in temporary state
   */
  onFilterChange(filterName: keyof IGetTeachersRequest, value: any): void {
    this._tempFilters.update(current => ({ ...current, [filterName]: value }));
  }

  /**
   * Handles multi-select changes for availability statuses
   */
  onAvailabilityStatusChange(value: IAvailabilityStatusOptionsEnum[]): void {
    const flagValue = this.generalService.convertArrayToFlags(value);
    this._tempFilters.update(current => ({ ...current, availabilityStatus: flagValue }));
  }

  /**
   * Handles multi-select changes for teaching ages experience
   */
  onTeachingAgesExperienceChange(value: ITeacherStudentAgesExperienceEnum[]): void {
    const flagValue = this.generalService.convertArrayToFlags(value);
    this._tempFilters.update(current => ({ ...current, teachingAgesExperience: flagValue }));
  }

  /**
   * Handles multi-select changes for student ages preference
   */
  onStudentAgesPreferenceChange(value: ITeacherStudentAgesPreferenceEnum[]): void {
    const flagValue = this.generalService.convertArrayToFlags(value);
    this._tempFilters.update(current => ({ ...current, teacherStudentAgesPreference: flagValue }));
  }

  /**
   * Handles student ages range changes
   */
  onStudentAgesRangeChange(value: number[]): void {
    this._tempFilters.update(current => ({
      ...current,
      studentAgesMin: value[0],
      studentAgesMax: value[1]
    }));
  }

  /**
   * Handles approved date from changes with timezone conversion
   */
  onApprovedDateFromChange(date: Date | string | null): void {
    const convertedDate = this.dateRangeFilterService.convertDateFromToUtc(date);
    this._tempFilters.update(current => ({
      ...current,
      approvedDateFrom: convertedDate ? new Date(convertedDate) : null
    }));
  }

  /**
   * Handles approved date to changes with timezone conversion
   */
  onApprovedDateToChange(date: Date | string | null): void {
    const convertedDate = this.dateRangeFilterService.convertDateToToUtc(date);
    this._tempFilters.update(current => ({
      ...current,
      approvedDateTo: convertedDate ? new Date(convertedDate) : null
    }));
  }

  /**
   * Handles approved date range changes (both from and to) with timezone conversion
   */
  onApprovedDateRangeChange(dateFrom: Date | string | null, dateTo: Date | string | null): void {
    const convertedRange = this.dateRangeFilterService.convertDateRangeToUtc({
      dateFrom,
      dateTo
    });

    this._tempFilters.update(current => ({
      ...current,
      approvedDateFrom: convertedRange.dateFrom ? new Date(convertedRange.dateFrom) : null,
      approvedDateTo: convertedRange.dateTo ? new Date(convertedRange.dateTo) : null
    }));
  }

  /**
   * Gets the current filter state for external use (e.g., by drawer component)
   */
  getCurrentFilters(): IGetTeachersRequest {
    return { ...this.currentFilters(), pageNumber: 1 };
  }

  /**
   * Resets all filter values to defaults
   */
  resetFilters(): void {
    this._tempFilters.set({
      availabilityStatus: null,
      teachingAgesExperience: null,
      teacherStudentAgesPreference: null,
      studentAgesMin: 2,
      studentAgesMax: 17,
      gender: 0,
      teachingLanguage: null,
      speakingLanguage: null,
      includeBlocked: false,
      approvedDateFrom: null,
      approvedDateTo: null,
      searchTerm: null,
      pageNumber: 1,
      pageSize: 10,
      sortColumn: TeacherListComponentHelperService.DEFAULT_SORT_COLUMN,
      sortDirection: TeacherListComponentHelperService.DEFAULT_SORT_DIRECTION
    });
  }

  /**
   * Emits search action with current filter state
   */
  emitSearchAction(): void {
    this.filterAction.emit({
      action: 'search',
      filters: this.getCurrentFilters()
    });
  }

  /**
   * Emits reset action
   */
  emitResetAction(): void {
    this.resetFilters();
    this.filterAction.emit({
      action: 'reset',
      filters: {} as IGetTeachersRequest
    });
  }

  /**
   * Checks if there are pending filter changes that haven't been applied
   */
  hasPendingChanges(): boolean {
    return Object.keys(this._tempFilters()).length > 0;
  }

  /**
   * Discards temporary changes and reverts to current filter state
   */
  discardTempChanges(): void {
    this._tempFilters.set({});
  }

}
