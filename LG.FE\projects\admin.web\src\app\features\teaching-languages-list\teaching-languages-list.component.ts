import { ChangeDetectionStrategy, Component, computed, EnvironmentInjector, inject, model, signal, type OnInit } from '@angular/core';
import { forkJoin, Observable, Subject } from 'rxjs';
import { HttpClient } from '@angular/common/http';
import { FormBuilder, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { Column, CommonService, CustomDialogPopupComponent, HandleApiResponseService, ICreateTeachingLanguageRequest, IEditTeachingLanguageResponse, IGetTeachingLanguageRequest, IGetTeachingLanguageResponse, ITeachingLanguageDto, ToastService } from 'SharedModules.Library';
import { TeachingLanguagesRoutes } from 'SharedModules.Library';
import { DialogModule } from 'primeng/dialog';
import { ButtonModule } from 'primeng/button';
import { InputTextModule } from 'primeng/inputtext';
import { TableModule, TableRowReorderEvent } from 'primeng/table';
import { TagModule } from 'primeng/tag';
import { InputSwitchModule } from 'primeng/inputswitch';
import { ToastModule } from 'primeng/toast';
import { MessageService } from 'primeng/api';
import { INDEXED_COLUMNS } from './teaching-languages-columns';
import { IconFieldModule } from 'primeng/iconfield';
import { InputIconModule } from 'primeng/inputicon';

@Component({
  selector: 'app-teaching-languages-list',
  standalone: true,
  imports: [
    CommonModule,
    DialogModule,
    ButtonModule,
    FormsModule,
    ReactiveFormsModule,
    InputTextModule,
    TableModule,
    TagModule,
    InputSwitchModule,
    ToastModule,
    IconFieldModule,
    InputIconModule,
    CustomDialogPopupComponent,
  ],
  providers: [MessageService],
  templateUrl: './teaching-languages-list.component.html',
  styleUrl: './teaching-languages-list.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class TeachingLanguagesListComponent implements OnInit {
  commonService = inject(CommonService);
  private handleApiService = inject(HandleApiResponseService);
  private formBuilder = inject(FormBuilder);
  private messageService = inject(MessageService);
  private toastService = inject(ToastService);

  // Signals and State
  sections = signal<ITeachingLanguageDto[]>([]);
  sections$ = computed(() => this.sections());
  selectedTeachingLanguage = signal<ITeachingLanguageDto | undefined>(undefined);
  loading = computed(() => this.commonService.isLoading$());
  private initialOrder = signal<ITeachingLanguageDto[]>([]);

  // UI State
  hasUnsavedChanges = signal(false);
  addDialogVisible = model(false);
  editDialogVisible = model(false);
  cols = INDEXED_COLUMNS;

  // Forms
  addForm: FormGroup;
  editForm: FormGroup;

  orderChangesDialogVisible = model(false);
  orderChanges = signal<{ original: ITeachingLanguageDto; current: ITeachingLanguageDto }[]>([]);


  constructor() {
    this.addForm = this.formBuilder.group({
      name: ['', [Validators.required]],
      isActive: [true, [Validators.required]],
      order: [0] // Initialize with a default, but it will be overwritten in addSection
    });

    this.editForm = this.formBuilder.group({
      id: ['', [Validators.required]],
      name: ['', [Validators.required]],
      isActive: [false, [Validators.required]],
      order: [0, [Validators.required]]
    });
  }

  ngOnInit(): void {
    this.loadLanguages();
  }

  // UI Actions
  addSection(): void {
    const currentLanguages = this.sections();
    let newOrder = 0;
    if (currentLanguages.length > 0) {
      // Find the maximum existing order and add 1
      newOrder = Math.max(...currentLanguages.map(lang => lang.order!)) + 1;
    }

    this.addForm.reset({ 
      name: '',       // Clear the name
      isActive: true, // Default to active
      order: newOrder // Set the new calculated order
    });
    this.addDialogVisible.set(true);
  }

  onEditClick(language: ITeachingLanguageDto): void {
    this.selectedTeachingLanguage.set(language);
    this.editForm.patchValue(language);
    this.editDialogVisible.set(true);
  }

  onStatusChange(language: ITeachingLanguageDto): void {
    this.editLanguage(language, true);
    this.messageService.add({
      severity: 'success',
      summary: 'Status Updated',
      detail: `${language.name} is now ${language.isActive ? 'active' : 'inactive'}`
    });
  }

  // Add these signals at the component level
  sortField = signal<string | null>(null);
  sortOrder = signal<number>(1);

  onRowReorder(event: TableRowReorderEvent) {
    if (event.dragIndex === null || event.dropIndex === null) {
      console.error('Invalid dragIndex or dropIndex', event);
      return;
    }

    const languages = [...this.sections()];

    // Validate indices
    if (
      event.dragIndex! < 0 ||
      event.dragIndex! >= languages.length ||
      event.dropIndex! < 0 ||
      event.dropIndex! > languages.length
    ) {
      console.error('Indices out of bounds', {
        dragIndex: event.dragIndex,
        dropIndex: event.dropIndex,
        length: languages.length,
      });
      return;
    }

    // Get the table's current sort field and order
    const sortField = this.sortField() || 'order';
    const sortOrder = this.sortOrder() || 1;

    // Create a sorted copy to match table's visual order
    const sortedLanguages = [...languages].sort((a, b) => {
      const aValue = a[sortField as keyof ITeachingLanguageDto];
      const bValue = b[sortField as keyof ITeachingLanguageDto];
      if (typeof aValue === 'number' && typeof bValue === 'number') {
        return (aValue - bValue) * sortOrder;
      }
      return String(aValue).localeCompare(String(bValue)) * sortOrder;
    });

    // Get the moved item from the sorted order
    const movedItem = sortedLanguages[event.dragIndex!];

    // Find the original index of the moved item
    const originalDragIndex = languages.findIndex(
      (item) => item.id === movedItem.id
    );

    // Create new order
    const newOrder = [...languages];
    newOrder.splice(originalDragIndex, 1);
    newOrder.splice(event.dropIndex!, 0, movedItem);

    // Update order properties to be 1-based
    const updatedLanguages = newOrder.map((item, index) => ({
      ...item,
      order: index + 1 // Make order 1-based
    }));

    // Update the UI and mark as changed
    this.sections.set(updatedLanguages);
    this.hasUnsavedChanges.set(true);
  }

  confirmSaveNewOrder(): void {
    const currentOrder = this.sections();
    const originalOrder = this.initialOrder();

    const changes = currentOrder
      .map((current, newIndex) => {
        const original = originalOrder.find(o => o.id === current.id)!;
        if (original.order !== newIndex) {
          return {
            original,
            current: { ...current, order: newIndex }
          };
        }
        return null;
      })
      .filter((change): change is {
        original: ITeachingLanguageDto;
        current: { order: number; id?: string; name: string; isActive: boolean; }
      } =>
        change !== null
      );
    if (changes.length > 0) {
      this.orderChanges.set(changes);
      this.orderChangesDialogVisible.set(true);
    }
  }

  // Add new save order method
  saveNewOrder(): void {
    const languages = this.sections();
    this.commonService.isLoading$.set(true);

    forkJoin(languages.map(language => this.patchLanguageUpdate(language)))
      .subscribe({
        next: () => {
          this.toastService.show({
            severity: 'success',
            summary: 'Success',
            detail: 'Order updated successfully'
          });
          this.hasUnsavedChanges.set(false);
          this.orderChangesDialogVisible.set(false);
          this.loadLanguages();
        },
        error: () => {
          this.messageService.add({
            severity: 'error',
            summary: 'Error',
            detail: 'Failed to update order'
          });
          this.loadLanguages();
          this.orderChangesDialogVisible.set(false);
        },
        complete: () => this.commonService.isLoading$.set(false)
      });
  }

  // Form Submissions
  onAddSubmit(): void {
    if (this.addForm.valid) {
      this.commonService.isLoading$.set(true);
      this.handleApiService.getApiData<ICreateTeachingLanguageRequest>(
        {
          url: TeachingLanguagesRoutes.postCreateTeachingLanguage,
          method: 'POST'
        },
        // Ensure the order field is included in the payload
        { teachingLanguage: { ...this.addForm.value, order: this.addForm.get('order')?.value } }
      ).subscribe({
        next: () => {
          this.toastService.show({
            severity: 'success',
            summary: 'Success',
            detail: 'Language added successfully'
          });
          this.addDialogVisible.set(false);
          this.loadLanguages();
          this.addForm.reset({ isActive: true, order: 0 }); // Reset order as well
        },
        error: () => {
          this.toastService.show({
            severity: 'error',
            summary: 'Error',
            detail: 'Failed to add language'
          });
        },
        complete: () => this.commonService.isLoading$.set(false)
      });
    }
  }

  onEditSubmit(): void {
    if (this.editForm.valid) {
      this.editLanguage(this.editForm.value, true);
      this.editDialogVisible.set(false);
    }
  }

  // Data Operations
  private loadLanguages(): void {
    this.commonService.isLoading$.set(true);
    this.handleApiService.getApiData<{teachingLanguages: ITeachingLanguageDto[] }>(
      {
        url: TeachingLanguagesRoutes.getAllTeachingLanguages,
        method: 'GET'
      },
      null
    ).subscribe({
      next: (response) => {
        this.sections.set(response.teachingLanguages);
        this.initialOrder.set([...response.teachingLanguages]);
        this.orderChangesDialogVisible.set(false);
      },
      error: () => {
        this.toastService.show({
          severity: 'error',
          summary: 'Error',
          detail: 'Failed to load languages'
        });
        this.commonService.isLoading$.set(false);
      },
      complete: () => this.commonService.isLoading$.set(false)
    });
  }

  private editLanguage(language: ITeachingLanguageDto, reloadAfter: boolean = false): void {
    this.commonService.isLoading$.set(true);

    this.patchLanguageUpdate(language).subscribe({
      next: () => {
        this.toastService.show({
          severity: 'success',
          summary: 'Success',
          detail: 'Language updated successfully'
        });
        if (reloadAfter) {
          this.loadLanguages();
        }
      },
      error: () => {
        this.toastService.show({
          severity: 'error',
          summary: 'Error',
          detail: 'Failed to update language'
        });
        this.commonService.isLoading$.set(false);
      },
      complete: () => this.commonService.isLoading$.set(false)
      });
  }

  private patchLanguageUpdate(language: ITeachingLanguageDto): Observable<IEditTeachingLanguageResponse> {
    return this.handleApiService.getApiData<IEditTeachingLanguageResponse>(
      {
        url: TeachingLanguagesRoutes.patchEditTeachingLanguage,
        method: 'PATCH'
      },
      { teachingLanguage: language }
    );
  }
}