import { CommonModule } from "@angular/common";
import { ChangeDetectionStrategy, Component, OnInit, inject, Injector, signal, computed } from "@angular/core";
import { toObservable } from "@angular/core/rxjs-interop";
import { FormsModule, ReactiveFormsModule, FormBuilder, FormGroup, Validators } from "@angular/forms";
import { Router, ActivatedRoute, NavigationEnd } from "@angular/router";
import { ITeachingLanguageDto, IRegisterStudentToTeachingLanguageRequest, IStudents, IGetStudentDashboardResponse, PrimeProfilePhotoSingleComponent, IStudentTeachingLanguageDto, IGetAllTeachingLanguagesResponse, ISearchStudentDto, DefaultGetStudentsRequest } from "SharedModules.Library";
import { ButtonModule } from "primeng/button";
import { CarouselModule } from "primeng/carousel";
import { DropdownModule } from "primeng/dropdown";
import { TextareaModule } from "primeng/textarea";
import { untilDestroyed, Severity, HandleApiResponseService, DataApiStateService, State, EventBusService, EmitEvent, Events, GeneralService, FormFieldValidationMessageComponent } from "SharedModules.Library";
import { CarouselService } from "@platform.src/app/core/services/carousel.service";
import { BehaviorSubject, combineLatest, filter, map, finalize } from "rxjs";
import { RegisterService } from "@platform.app/core/services/register.service";
import { PrimeStudentsSelectionComponent } from "@platform.app/shared/components/prime/prime-students-selection/prime-students-selection.component";
import { UserService } from "@platform.app/core/services/user.service";

@Component({
    selector: 'app-trial-request-add-new-language-step',
    imports: [
        CommonModule,
        ButtonModule,
        FormsModule,
        ReactiveFormsModule,
        FormFieldValidationMessageComponent,
        TextareaModule,
        DropdownModule,
        CarouselModule,
        PrimeStudentsSelectionComponent,
        PrimeProfilePhotoSingleComponent,
    ],
    templateUrl: './trial-request-add-new-language-step.component.html',
    styleUrls: ['./trial-request-add-new-language-step.component.scss'],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class TrialRequestAddNewLanguageStepComponent implements OnInit {
    private readonly injector = inject(Injector);
    private readonly destroy$ = untilDestroyed(); // Renamed for clarity

    router = inject(Router);
    generalService = inject(GeneralService);
    registerService = inject(RegisterService);
    apiService = inject(HandleApiResponseService);
    dataStateApiService = inject(DataApiStateService);
    eventBusService = inject(EventBusService);
    carouselService = inject(CarouselService);
    userService = inject(UserService);
    route = inject(ActivatedRoute);
    fb = inject(FormBuilder);

    // Simplified student state using only signals
    selectedStudent = signal<ISearchStudentDto>({} as ISearchStudentDto);
    students$ = computed(() => this.dataStateApiService.parentStudents.state() || [] as State<ISearchStudentDto>[]);
    studentFoundFromUrl = signal<ISearchStudentDto>({} as ISearchStudentDto);
    currentStudent = signal<ISearchStudentDto>({} as ISearchStudentDto);
    Severity = Severity;
    levels = this.generalService.levels;
    form: FormGroup = new FormGroup({});

    selectedLanguageOfInterest = signal<ITeachingLanguageDto>({} as ITeachingLanguageDto);
    teachingLanguages$ = computed(() => this.dataStateApiService.teachingLanguages.state() || []);

    // Filtered languages computed based on selected student and available languages
    filteredLanguages = computed(() => {
        const allLanguages = this.teachingLanguages$().data?.teachingLanguages || [];
        const activeStudent = this.studentFoundFromUrl().userId ? this.studentFoundFromUrl() : this.selectedStudent(); // Use student from URL if available, otherwise selected student
        const studentLanguageIds = (activeStudent?.studentTeachingLanguageDto || []).map((lang: IStudentTeachingLanguageDto) => lang.teachingLanguageId);
        return allLanguages.filter((lang: ITeachingLanguageDto) => !studentLanguageIds.includes(lang.id as string));
    });

    studentId = signal<string | null>(null); // Use null for initial state
    resetStudentsSelectionSignal = signal(false);

    ngOnInit(): void {
        this.loadTeachingLanguages();
        this.initializeForm();
        this.setupSubscriptions();
        // Initial load of students (DefaultGetStudentsRequest automatically cleans null values)
        const request = new DefaultGetStudentsRequest();
        this.eventBusService.emit(new EmitEvent(Events.StateLoadParentStudents, request));
    }

    private loadTeachingLanguages(): void {
        this.eventBusService.emit(new EmitEvent(Events.StateLoadTeachingLanguages, undefined));
    }

    private initializeForm(): void {
        const { studentLevel, moreDetails } = this.registerService.getRegisterStudentRequest();
        this.form = this.fb.group({
            languageOfInterest: [null, Validators.required],
            studentLevel: [studentLevel || null, Validators.required],
            moreDetails: [moreDetails || null],
        });
    }

    private setupSubscriptions(): void {
        this.subscribeToRouterEvents();
        this.subscribeToStudentsState();
        // The language filtering is now handled by the `filteredLanguages` computed signal,
        // so no explicit subscription is needed here for filtering logic.
    }

    private subscribeToRouterEvents(): void {
        this.router.events
            .pipe(
                filter(event => event instanceof NavigationEnd),
                this.destroy$()
            )
            .subscribe();
    }

    // Subscribe to the students state to handle query params once data is loaded
    private subscribeToStudentsState(): void {
        toObservable(this.students$, { injector: this.injector })
            .pipe(
                filter(res => !!res.data), // Only proceed when data is available
                this.destroy$()
            )
            .subscribe(() => {
                this.handleQueryParams();
            });
    }

    private handleQueryParams(): void {
        this.route.queryParams.pipe(this.destroy$()).subscribe(params => {
            const studentIdFromParams = params['studentId'] || null;
            this.studentId.set(studentIdFromParams);

            if (studentIdFromParams) {
                const found = this.userService.findStudentById(studentIdFromParams, this.students$());
                this.studentFoundFromUrl.set(found || {} as ISearchStudentDto); // Set found student or empty object
                if (found) {
                    this.updateSelectedStudent(found);
                }
            } else {
                // If studentId is removed from query params, clear selected student state
                this.clearSelectedStudentState();
            }
        });
    }

    private updateSelectedStudent(student: ISearchStudentDto): void {
        this.selectedStudent.set(student);
        // No need for BehaviorSubject anymore
        // this.selectedStudentSubject.next(student);
    }

    // Removed the filterLanguages method as it's replaced by the computed signal

    onStudentSelected(student: ISearchStudentDto | ISearchStudentDto[]): void {
        // Ensure single student is selected
        const studentDto = Array.isArray(student) ? student[0] : student;
        if (studentDto) {
            this.selectedLanguageOfInterest.set({} as ITeachingLanguageDto);
            this.form.patchValue({ languageOfInterest: null });
            this.updateSelectedStudent(studentDto);
            // Update query params when a student is selected
            this.router.navigate([], {
                relativeTo: this.route,
                queryParams: { studentId: studentDto.userId },
                queryParamsHandling: 'merge'
            });
        }
    }

    onLanguageChange(event: { value: ITeachingLanguageDto }): void {
        this.selectedLanguageOfInterest.set(event.value as ITeachingLanguageDto);
    }

    onSubmit(): void {
        if (this.form.valid && !this.generalService.isObjectEmpty(this.selectedStudent())) {
            this.registerStudent();
        } else {
            // Optionally show a message if form is invalid or student not selected
            console.warn('Form is invalid or student not selected');
        }
    }

    private registerStudent(): void {
        const requestObject: IRegisterStudentToTeachingLanguageRequest = {
            studentId: this.selectedStudent().userId,
            registerStudentToTeachingLanguageDto: {
                languageOfInterestId: this.selectedLanguageOfInterest().id as string,
                languageName: this.selectedLanguageOfInterest().name,
                moreDetails: this.form.value.moreDetails,
                studentLevel: this.form.value.studentLevel,
            },
        };

        this.generalService.showDivLoading('.registerTrialSteps__container');
        this.apiService.getApiData<IRegisterStudentToTeachingLanguageRequest>(
            { url: IStudents.postRegisterToNewLanguage, method: 'POST' },
            requestObject
        ).pipe(
            finalize(() => this.generalService.hideDivLoading()),
            this.destroy$() // Use the renamed destroy signal
        ).subscribe({
            next: () => {
                // Reload students state after successful registration
                const request = new DefaultGetStudentsRequest();
                this.eventBusService.emit(new EmitEvent(Events.StateLoadParentStudents, request));
                this.clearFormAndNavigate();
            },
            error: (err) => {
                console.error('Registration failed:', err);
                this.generalService.hideDivLoading();
                // Handle API error, maybe show a message to the user
            },
        });
    }

    clearSelectedStudent(): void {
        this.clearSelectedStudentState();
        // Remove studentId from query params
        this.router.navigate([], { relativeTo: this.route, queryParams: { studentId: null }, queryParamsHandling: 'merge' });
    }

    private clearSelectedStudentState(): void {
        this.studentId.set(null);
        this.selectedStudent.set({} as ISearchStudentDto);
        this.studentFoundFromUrl.set({} as ISearchStudentDto);
        this.resetStudentsSelectionSignal.set(true); // Signal to reset student selection component
        this.form.patchValue({ languageOfInterest: null, studentLevel: null, moreDetails: null });
        this.selectedLanguageOfInterest.set({} as ITeachingLanguageDto); // Clear selected language
    }


    private clearFormAndNavigate(): void {
        this.form.reset();
        // Clear student selection state after successful submission
        // this.clearSelectedStudentState();
        this.registerService.navigateToNewTrialStep('free-trial-thank-you', undefined, { studentId: this.studentId(), teachingLanguageId: this.selectedLanguageOfInterest().id,
            teachingLanguageName: this.selectedLanguageOfInterest().name
         });
    }

}