@use "mixins";

// Modern color palette
$primary: #6366f1;
$primary-50: #eef2ff;
$primary-100: #e0e7ff;
$primary-600: #4f46e5;
$primary-700: #4338ca;

$success: #10b981;
$success-50: #ecfdf5;
$success-100: #d1fae5;

$purple: #8b5cf6;
$purple-50: #f3e8ff;
$purple-100: #e9d5ff;

$orange: #f97316;
$orange-50: #fff7ed;
$orange-100: #ffedd5;

$gray-50: #f9fafb;
$gray-100: #f3f4f6;
$gray-200: #e5e7eb;
$gray-300: #d1d5db;
$gray-400: #9ca3af;
$gray-500: #6b7280;
$gray-600: #4b5563;
$gray-700: #374151;
$gray-800: #1f2937;
$gray-900: #111827;

// Professional shadows
$shadow-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
$shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px -1px rgba(0, 0, 0, 0.1);
$shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -2px rgba(0, 0, 0, 0.1);
$shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -4px rgba(0, 0, 0, 0.1);

// Smooth transitions
$transition-fast: all 0.15s ease-out;
$transition-base: all 0.2s ease-out;
$transition-slow: all 0.3s ease-out;

:host {
  display: block;
  background-image: url('/assets/images/graphic/8852286.webp');
  background-size: cover;
  background-repeat: no-repeat;

  ::ng-deep {
    .p-dialog .p-dialog-content {
      padding: 0rem;
    }

    .p-dialog .p-dialog-footer {
      padding: 0rem;
    }

    .p-button.p-button-outlined.p-purple {
      color: var(--purpley);
    }
  }
}

// Group Success Container
.group-success-container {
  padding: 1.5rem;
  display: flex;
  flex-direction: column;
  gap: 1.25rem;
  max-width: 100%;

  @include mixins.breakpoint(mobile) {
    padding: 1.25rem;
    gap: 1rem;
  }

  // Success Header
  .success-header {
    text-align: center;
    margin-bottom: 0.5rem;

    @include mixins.breakpoint(mobile) {
      margin-bottom: 0.375rem;
    }

    .success-icon {
      position: relative;
      display: inline-block;
      margin-bottom: 1rem;

      @include mixins.breakpoint(mobile) {
        margin-bottom: 0.875rem;
      }

      .icon-background {
        width: 64px;
        height: 64px;
        background: linear-gradient(135deg, $success 0%, #34d399 100%);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1.5rem;
        box-shadow: $shadow-lg;
        position: relative;
        z-index: 2;
        margin: 0 auto;

        @include mixins.breakpoint(mobile) {
          width: 56px;
          height: 56px;
          font-size: 1.375rem;
        }
      }

      .success-glow {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 96px;
        height: 96px;
        background: radial-gradient(circle, rgba(16, 185, 129, 0.2) 0%, transparent 70%);
        border-radius: 50%;
        animation: success-pulse 3s ease-in-out infinite;
        z-index: 1;

        @include mixins.breakpoint(mobile) {
          width: 84px;
          height: 84px;
        }
      }
    }

    .success-content {
      .success-title {
        font-size: 1.5rem;
        font-weight: 700;
        color: $gray-900;
        margin: 0 0 0.5rem;
        letter-spacing: -0.025em;
        line-height: 1.2;

        @include mixins.breakpoint(mobile) {
          font-size: 1.375rem;
          margin-bottom: 0.375rem;
        }
      }

      .success-subtitle {
        font-size: 0.875rem;
        color: $gray-600;
        margin: 0;
        line-height: 1.4;

        @include mixins.breakpoint(mobile) {
          font-size: 0.8125rem;
        }
      }
    }
  }

  // Group Showcase Card
  .group-showcase-card {
    background: linear-gradient(135deg, $purple-50 0%, $primary-50 100%);
    border-radius: 12px;
    padding: 1.25rem;
    border: 1px solid rgba(139, 92, 246, 0.2);
    box-shadow: $shadow-md;
    position: relative;
    overflow: hidden;

    @include mixins.breakpoint(mobile) {
      padding: 1rem;
      border-radius: 10px;
    }

    .group-header {
      display: flex;
      align-items: flex-start;
      gap: 1rem;
      margin-bottom: 1rem;

      @include mixins.breakpoint(mobile) {
        gap: 0.875rem;
        margin-bottom: 0.875rem;
      }

      .group-icon {
        position: relative;
        width: 48px;
        height: 48px;
        background: linear-gradient(135deg, $purple 0%, #a855f7 100%);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1.125rem;
        box-shadow: $shadow-sm;
        flex-shrink: 0;

        @include mixins.breakpoint(mobile) {
          width: 44px;
          height: 44px;
          font-size: 1rem;
        }

        .orbit-ring {
          position: absolute;
          top: -4px;
          left: -4px;
          right: -4px;
          bottom: -4px;
          border: 1px solid rgba(139, 92, 246, 0.4);
          border-radius: 50%;
          animation: orbit-rotate 8s linear infinite;
        }
      }

      .group-details {
        flex: 1;
        min-width: 0;

        .group-name {
          font-size: 1.125rem;
          font-weight: 700;
          color: $gray-900;
          margin: 0 0 0.5rem;
          letter-spacing: -0.025em;
          line-height: 1.3;

          @include mixins.breakpoint(mobile) {
            font-size: 1rem;
            margin-bottom: 0.375rem;
          }
        }

        .group-meta {
          display: flex;
          flex-wrap: wrap;
          gap: 0.75rem;

          @include mixins.breakpoint(mobile) {
            gap: 0.5rem;
          }

          .meta-item {
            display: flex;
            align-items: center;
            gap: 0.375rem;
            font-size: 0.8125rem;
            color: $gray-700;
            font-weight: 500;

            @include mixins.breakpoint(mobile) {
              font-size: 0.75rem;
            }

            i {
              color: $purple;
              font-size: 0.875rem;

              @include mixins.breakpoint(mobile) {
                font-size: 0.8125rem;
              }
            }

            .level-badge {
              background: rgba(139, 92, 246, 0.1);
              color: $purple;
              padding: 0.125rem 0.375rem;
              border-radius: 4px;
              font-weight: 600;
              text-transform: capitalize;
            }
          }
        }
      }
    }

    // Members Preview
    .members-preview {
      padding-top: 0.75rem;
      border-top: 1px solid rgba(139, 92, 246, 0.2);

      @include mixins.breakpoint(mobile) {
        padding-top: 0.625rem;
      }

      .members-header {
        margin-bottom: 0.75rem;

        @include mixins.breakpoint(mobile) {
          margin-bottom: 0.625rem;
        }

        .members-title {
          font-size: 0.875rem;
          font-weight: 600;
          color: $gray-700;

          @include mixins.breakpoint(mobile) {
            font-size: 0.8125rem;
          }
        }
      }

      .members-list {
        display: flex;
        gap: 0.5rem;
        flex-wrap: wrap;

        .member-avatar {
          width: 36px;
          height: 36px;
          background: linear-gradient(135deg, $primary 0%, $primary-600 100%);
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          color: white;
          font-size: 0.75rem;
          font-weight: 600;
          border: 2px solid white;
          box-shadow: $shadow-xs;

          @include mixins.breakpoint(mobile) {
            width: 32px;
            height: 32px;
            font-size: 0.6875rem;
          }

          &.overflow-indicator {
            background: $gray-400;

            .overflow-count {
              font-size: 0.6875rem;
              font-weight: 700;

              @include mixins.breakpoint(mobile) {
                font-size: 0.625rem;
              }
            }
          }
        }
      }
    }
  }

  // Success Actions
  .success-actions {
    display: flex;
    gap: 0.875rem;
    justify-content: center;
    flex-wrap: wrap;

    @include mixins.breakpoint(mobile) {
      flex-direction: column;
      gap: 0.75rem;
    }
  }
}

// Animations
@keyframes success-pulse {
  0%, 100% {
    opacity: 0.6;
    transform: translate(-50%, -50%) scale(1);
  }
  50% {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1.05);
  }
}

@keyframes orbit-rotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

// Modern Button Styles
::ng-deep {
  .primary-action-btn {
    background: linear-gradient(135deg, $purple 0%, #a855f7 100%) !important;
    border: 2px solid $purple !important;
    color: white !important;
    font-weight: 600 !important;
    padding: 0.75rem 1.5rem !important;
    border-radius: 10px !important;
    transition: $transition-base !important;
    font-size: 0.875rem !important;
    box-shadow: $shadow-sm !important;

    @include mixins.breakpoint(mobile) {
      padding: 0.625rem 1.25rem !important;
      font-size: 0.8125rem !important;
      width: 100% !important;
    }

    &:hover {
      background: linear-gradient(135deg, #7c3aed 0%, #9333ea 100%) !important;
      border-color: #7c3aed !important;
      transform: translateY(-2px) !important;
      box-shadow: $shadow-md !important;
    }

    &:active {
      transform: translateY(-1px) !important;
    }

    &:focus {
      box-shadow: 0 0 0 3px rgba(139, 92, 246, 0.2) !important;
    }

    .p-button-icon {
      color: white !important;
      font-size: 0.875rem !important;

      @include mixins.breakpoint(mobile) {
        font-size: 0.8125rem !important;
      }
    }

    .p-button-label {
      color: white !important;
      font-weight: 600 !important;
    }
  }

  .secondary-action-btn {
    background: white !important;
    border: 2px solid $gray-300 !important;
    color: $gray-700 !important;
    font-weight: 600 !important;
    padding: 0.75rem 1.5rem !important;
    border-radius: 10px !important;
    transition: $transition-base !important;
    font-size: 0.875rem !important;
    box-shadow: $shadow-xs !important;

    @include mixins.breakpoint(mobile) {
      padding: 0.625rem 1.25rem !important;
      font-size: 0.8125rem !important;
      width: 100% !important;
    }

    &:hover {
      background: $gray-50 !important;
      border-color: $purple !important;
      color: $purple !important;
      transform: translateY(-2px) !important;
      box-shadow: $shadow-sm !important;
    }

    &:active {
      transform: translateY(-1px) !important;
    }

    &:focus {
      box-shadow: 0 0 0 3px rgba(139, 92, 246, 0.1) !important;
    }

    .p-button-icon {
      color: $gray-700 !important;
      font-size: 0.875rem !important;

      @include mixins.breakpoint(mobile) {
        font-size: 0.8125rem !important;
      }
    }

    &:hover .p-button-icon {
      color: $purple !important;
    }

    .p-button-label {
      color: $gray-700 !important;
      font-weight: 600 !important;
    }

    &:hover .p-button-label {
      color: $purple !important;
    }
  }
}

// ViewGroup Compact State Styles
.view-group-container {
  padding: 1rem;
  display: flex;
  flex-direction: column;
  gap: 1rem;
  max-width: 100%;

  @include mixins.breakpoint(mobile) {
    padding: 0.875rem;
    gap: 0.875rem;
  }

  // Compact Header
  .group-header-compact {
    display: flex;
    align-items: center;
    gap: 0.875rem;
    padding: 1rem;
    background: linear-gradient(135deg, $purple-50 0%, $primary-50 100%);
    border-radius: 12px;
    border: 1px solid rgba(99, 102, 241, 0.1);

    @include mixins.breakpoint(mobile) {
      padding: 0.875rem;
      gap: 0.75rem;
    }

    .group-icon-minimal {
      width: 48px;
      height: 48px;
      background: linear-gradient(135deg, $purple 0%, #a855f7 100%);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-size: 1.125rem;
      flex-shrink: 0;

      @include mixins.breakpoint(mobile) {
        width: 40px;
        height: 40px;
        font-size: 1rem;
      }
    }

    .group-info-minimal {
      flex: 1;
      min-width: 0;

      .group-name-compact {
        font-size: 1.125rem;
        font-weight: 600;
        color: $gray-900;
        margin: 0 0 0.375rem;
        line-height: 1.3;

        @include mixins.breakpoint(mobile) {
          font-size: 1rem;
          margin-bottom: 0.25rem;
        }
      }

      .group-meta-compact {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        flex-wrap: wrap;

        @include mixins.breakpoint(mobile) {
          gap: 0.375rem;
        }

        .language-pill,
        .level-pill {
          background: rgba(99, 102, 241, 0.1);
          color: $primary-700;
          padding: 0.125rem 0.5rem;
          border-radius: 12px;
          font-size: 0.75rem;
          font-weight: 500;

          @include mixins.breakpoint(mobile) {
            font-size: 0.6875rem;
            padding: 0.1rem 0.375rem;
          }
        }

        .level-pill {
          background: rgba(139, 92, 246, 0.1);
          color: $purple;
        }

        .members-count {
          font-size: 0.75rem;
          color: $gray-600;
          font-weight: 500;

          @include mixins.breakpoint(mobile) {
            font-size: 0.6875rem;
          }
        }
      }
    }
  }

  // Compact Info Grid
  .info-grid-compact {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(160px, 1fr));
    gap: 0.75rem;

    @include mixins.breakpoint(mobile) {
      grid-template-columns: 1fr;
      gap: 0.625rem;
    }

    .info-card {
      background: white;
      border-radius: 8px;
      padding: 0.875rem;
      border: 1px solid $gray-200;
      box-shadow: $shadow-xs;
      transition: $transition-fast;

      @include mixins.breakpoint(mobile) {
        padding: 0.75rem;
      }

      &:hover {
        box-shadow: $shadow-sm;
      }

      .card-header-minimal {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        margin-bottom: 0.75rem;
        font-size: 0.8125rem;
        font-weight: 600;
        color: $gray-700;

        @include mixins.breakpoint(mobile) {
          margin-bottom: 0.625rem;
          font-size: 0.75rem;
        }

        i {
          font-size: 0.875rem;
          color: $primary;

          @include mixins.breakpoint(mobile) {
            font-size: 0.8125rem;
          }
        }
      }
    }

    // Members Card
    .members-compact {
      .members-list-compact {
        display: flex;
        flex-wrap: wrap;
        gap: 0.375rem;

        .member-chip {
          display: flex;
          align-items: center;
          gap: 0.375rem;
          background: $gray-50;
          padding: 0.25rem 0.5rem;
          border-radius: 16px;
          font-size: 0.75rem;

          @include mixins.breakpoint(mobile) {
            font-size: 0.6875rem;
            padding: 0.1875rem 0.375rem;
          }

          .member-avatar-mini {
            width: 20px;
            height: 20px;
            background: linear-gradient(135deg, $primary 0%, $primary-600 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 0.625rem;
            font-weight: 600;

            @include mixins.breakpoint(mobile) {
              width: 18px;
              height: 18px;
              font-size: 0.5625rem;
            }
          }

          .member-name-mini {
            color: $gray-700;
            font-weight: 500;
          }

          &.overflow {
            background: $gray-200;
            color: $gray-600;
            font-weight: 600;
            justify-content: center;
            min-width: 2rem;
          }
        }
      }

      .empty-state-mini {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 0.25rem;
        color: $gray-500;
        font-size: 0.75rem;

        @include mixins.breakpoint(mobile) {
          font-size: 0.6875rem;
        }

        i {
          font-size: 1rem;
          color: $gray-400;

          @include mixins.breakpoint(mobile) {
            font-size: 0.875rem;
          }
        }
      }
    }

    // Stats Card
    .stats-compact {
      .stats-mini {
        display: flex;
        justify-content: space-between;
        margin-bottom: 0.5rem;

        .stat-item-mini {
          text-align: center;

          .stat-number {
            display: block;
            font-size: 1.125rem;
            font-weight: 700;
            color: $gray-900;
            margin-bottom: 0.125rem;

            @include mixins.breakpoint(mobile) {
              font-size: 1rem;
            }
          }

          .stat-label {
            font-size: 0.6875rem;
            color: $gray-600;
            font-weight: 500;

            @include mixins.breakpoint(mobile) {
              font-size: 0.625rem;
            }
          }
        }
      }

      .progress-mini {
        width: 100%;
        height: 4px;
        background: $gray-200;
        border-radius: 2px;
        overflow: hidden;

        .progress-bar-mini {
          height: 100%;
          background: linear-gradient(90deg, $success 0%, #34d399 100%);
          border-radius: 2px;
          transition: width 0.4s ease-out;
        }
      }
    }

    // Schedule Card
    .schedule-compact {
      .next-lesson-mini {
        text-align: center;

        .lesson-date-mini {
          font-size: 0.875rem;
          font-weight: 600;
          color: $gray-900;
          margin-bottom: 0.125rem;

          @include mixins.breakpoint(mobile) {
            font-size: 0.8125rem;
          }
        }

        .lesson-time-mini {
          font-size: 0.75rem;
          color: $gray-600;

          @include mixins.breakpoint(mobile) {
            font-size: 0.6875rem;
          }
        }
      }
    }
  }

  // Compact Actions

}

// Compact Action Button Styles
::ng-deep {
  .action-btn-compact {
    font-size: 0.75rem !important;
    padding: 0.5rem 0.875rem !important;
    border-radius: 6px !important;
    font-weight: 500 !important;
    transition: $transition-fast !important;

    @include mixins.breakpoint(mobile) {
      font-size: 0.6875rem !important;
      padding: 0.375rem 0.75rem !important;
    }

    &.primary {
      background: $purple !important;
      border: 1px solid $purple !important;
      color: white !important;

      &:hover {
        background: #7c3aed !important;
        transform: translateY(-1px) !important;
      }
    }

    &.secondary {
      background: $primary !important;
      border: 1px solid $primary !important;
      color: white !important;

      &:hover {
        background: $primary-600 !important;
        transform: translateY(-1px) !important;
      }
    }

    &.outline {
      background: white !important;
      border: 1px solid $gray-300 !important;
      color: $gray-700 !important;

      &:hover {
        background: $gray-50 !important;
        border-color: $primary !important;
        color: $primary !important;
        transform: translateY(-1px) !important;
      }
    }

    &.danger-icon {
      background: white !important;
      border: 1px solid #ef4444 !important;
      color: #ef4444 !important;
      width: 2.5rem !important;
      padding: 0.5rem !important;

      @include mixins.breakpoint(mobile) {
        width: 2.25rem !important;
        padding: 0.375rem !important;
      }

      &:hover {
        background: #fef2f2 !important;
        transform: translateY(-1px) !important;
      }
    }

    .p-button-icon {
      font-size: 0.75rem !important;

      @include mixins.breakpoint(mobile) {
        font-size: 0.6875rem !important;
      }
    }

    .p-button-label {
      font-weight: 500 !important;
    }
  }
}

// Reduced Motion Support
@media (prefers-reduced-motion: reduce) {
  .group-success-container {
    .success-glow {
      animation: none;
      opacity: 0.6;
    }

    .group-icon .orbit-ring {
      animation: none;
    }

    .primary-action-btn:hover,
    .secondary-action-btn:hover {
      transform: none !important;
    }
  }

  .view-group-container {
    .action-btn-compact:hover {
      transform: none !important;
    }
  }
}
