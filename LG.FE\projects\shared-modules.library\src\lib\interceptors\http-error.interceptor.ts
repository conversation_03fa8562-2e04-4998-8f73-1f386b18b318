import { inject } from '@angular/core';
import { HttpRequest, HttpEvent, HttpErrorResponse, HttpInterceptorFn, HttpEventType, HttpResponse } from '@angular/common/http';
import { BehaviorSubject, throwError } from 'rxjs';
import { catchError, filter, finalize, map, switchMap, take, tap } from 'rxjs/operators';

import { Router } from '@angular/router';
import { HandleApiResponseService } from '../services/handle-api-response.service';
import { ToastService } from '../services/toast.service';
import { AuthStateService } from '../services/auth-state.service';
import { IApiResponse, IApiResponseBase, IdentityRoutes, IHttpStatusCode, IRefreshTokenResponse, LocationDataRoutes } from '../GeneratedTsFiles';
import { ApiLoadingStateService } from '../services/api-loading-state.service';

let isRefreshing = false;
let refreshTokenSubject: BehaviorSubject<string | null> = new BehaviorSubject<string | null>(null);
export const httpErrorInterceptor: HttpInterceptorFn = (req, next) => {

  const toastService = inject(ToastService);
  const authService = inject(AuthStateService);
  const apiResponseService = inject(HandleApiResponseService);
  const apiLoadingStateService = inject(ApiLoadingStateService);
  const router = inject(Router);
  const token = inject(AuthStateService).getToken();
  const refreshToken = inject(AuthStateService).getRefreshToken();
  let interceptedReq: HttpRequest<unknown>;

  // Generate unique request ID for tracking
  const requestId = `${req.method}-${req.url}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

  // Call beginRequest() with unique ID before the request is sent
  apiLoadingStateService.beginRequest(requestId);


  return next(attachAuthorizationHeader(req)).pipe(
    tap((event: HttpEvent<unknown>) => {
      if (event instanceof HttpRequest) {
        interceptedReq = event; // Save the intercepted request
      }
    }),
    map((event: HttpEvent<unknown>) => {
      if (event.type === HttpEventType.Response) {
        const response = event as HttpResponse<IApiResponse<unknown>>;
        const body = response.body;
        if (body?.messages) {
          const errorMessages = Object.values(body.messages).flat();
          const concatenatedErrorMessage = errorMessages.join('<br>');
          // ... setTimeout remains the same ...
        }
      }
      return event;
    }),
    catchError((error: HttpErrorResponse) => {
      console.log(error);
      if ((error.error || error.status === IHttpStatusCode.Unauthorized)) {

        if (error.url && error.url.includes(IdentityRoutes.postRefreshToken)) {
          // isRefreshing = false;
          authService.logout();
          // alert('Session expired. Please login again.');
        }

        interceptedReq = req; // Store the intercepted request here
        let concatenatedErrorMessage = '';
        const errorMessage = 'An unknown error occurred.';

        if (error.error && error.error.formValidationErrors) {
          const formErrors = error.error.formValidationErrors;
          if (formErrors) {
            const errorMessages = Object.values(formErrors).flat();

            concatenatedErrorMessage += errorMessages.join('<br>');
          }
        }

        switch (error.status) {
          case IHttpStatusCode.Unauthorized:
            if (!isRefreshing) {
              isRefreshing = true;
              refreshTokenSubject.next(null);
              return apiResponseService.getApiData<IRefreshTokenResponse>({ url: IdentityRoutes.postRefreshToken, method: 'POST' },
                {
                  token: token,
                  refreshToken: refreshToken
                }
              ).pipe(
                switchMap((tokens: IRefreshTokenResponse) => {
                  console.log('tokens response: ', tokens);
                  isRefreshing = false;
                  refreshTokenSubject.next(tokens.token);
                  // Refresh successful, update request with new access token
                  authService.setRefreshToken(tokens.refreshToken);
                  authService.setToken(tokens.token);
                  console.log('Refreshed token: ', tokens.token);
                  console.log('interceptedReq: ', interceptedReq);
                  const updatedReq = interceptedReq.clone({ setHeaders: { Authorization: `Bearer ${tokens.token}` } });
                  // Note: The retry request will be tracked by a new interceptor call,
                  // so we don't need to manually track it here
                  return next((updatedReq));
                }),
                catchError((refreshError: IApiResponseBase) => {
                  isRefreshing = false;
                  refreshTokenSubject.next(null);
                  console.log(refreshError);
                  // Refresh token failed, handle error (e.g., logout)
                  if (refreshError.statusCode === IHttpStatusCode.Unauthorized || refreshError.statusCode === IHttpStatusCode.Forbidden || refreshError.statusCode === IHttpStatusCode.BadRequest || refreshError.statusCode === IHttpStatusCode.NotFound || refreshError.statusCode === IHttpStatusCode.InternalServerError) {
                    //TODO: add Logout logic
                    authService.logout();
                    console.error('Refresh token failed:', refreshError);
                    return throwError(() => error);
                  } else {
                    return throwError(() => error);
                  }
                })
              );
            } else {
              return refreshTokenSubject.pipe(
                filter(token => token !== null),
                switchMap((newToken) => {
                  const updatedReq = interceptedReq.clone({ setHeaders: { Authorization: `Bearer ${newToken}` } });
                  // Note: The retry request will be tracked by a new interceptor call
                  return next(updatedReq);
                })
              );
            }
          case IHttpStatusCode.Forbidden: // Forbidden
            console.error('Forbidden');
            break;
          case IHttpStatusCode.BadRequest:
          case IHttpStatusCode.NotFound:
            console.error('Bad request');
            // Display a toast message using injected ToastService (assuming a provider)
            setTimeout(() => {

              // processErrorMessages(error, toastService);
            }, 100);
            break;
          default:
            //  toastService.show({
            //   summary: 'Message',
            //   detail: error.statusText,
            //   severity: 'info'
            // });
            console.error('Error occurred:', error);
        }

        return throwError(() => error);
      } else {
        console.error(error);
        if (error.status === IHttpStatusCode.Forbidden) {
          toastService.show({
            summary: 'Message',
            detail: 'Forbidden action',
            severity: 'warn'
          });
        }
        return throwError(() => error);
      }
    }),
    finalize(() => {
      // Call endRequest() with the same request ID when the request completes (success or error)
      apiLoadingStateService.endRequest(requestId);
    })
  );
};

function processErrorMessages(error: HttpErrorResponse, toastService: ToastService): void {
  const messages = error.error?.messages;
  if (messages) {
    const errorMessages = Object.values(messages).flat();
    const concatenatedErrorMessage = errorMessages.join('<br>').replace(/<br>/g, '\n');

    toastService.show({
      summary: 'Message',
      detail: concatenatedErrorMessage,
      severity: 'error'
    });
  }
}

function attachAuthorizationHeader(request: HttpRequest<unknown>): HttpRequest<unknown> {
  const requiresAuth = !isRouteUnprotected(request.url);
  const authService = inject(AuthStateService);
  const token = requiresAuth ? authService.getAuthData()?.token ?? null : null;

  if (token) {
    return request.clone({
      headers: request.headers.set('Authorization', `Bearer ${token}`)
    });
  }
  return request;
}

// Implement logic to check if the URL requires authorization based on your routing configuration
function isRouteUnprotected(url: string): boolean {
  const locationDataRouteValues = Object.values(LocationDataRoutes).map(route => route as string); // Cast to string
  const protectedRoutes = [
    ...locationDataRouteValues,
    IdentityRoutes.postRegisterParent,
    IdentityRoutes.postLogin,
  ];

  return protectedRoutes.some(route => url.includes(route));
}
