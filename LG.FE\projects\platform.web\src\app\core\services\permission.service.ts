import { Injectable } from '@angular/core';
import { IBasicProfileInfoDto, IPatchProfileInfoRequest, IUserClaims, nameOf } from 'SharedModules.Library';
import { IUserRole } from 'SharedModules.Library';

type Comment = {
  id: string;
  body: string;
  authorId: string;
  createdAt: Date;
};

type Todo = {
  id: string;
  title: string;
  userId: string;
  completed: boolean;
  invitedUsers: string[];
};

type Role = IUserRole.PARENT | IUserRole.STUDENT | IUserRole.TEACHER | IUserRole.ADMIN;
type User = IUserClaims;

// Type for a single permission check value: boolean or a function
type PermissionCheckValue<T> = T extends void ? boolean | ((user: User) => boolean) : boolean | ((user: User, data: T) => boolean);

// Recursive type to map the structure of Permissions to permission check values
type NestedPermissionMap<T> = {
  [K in keyof T]?: T[K] extends object
    ? (T[K] extends { view?: any; create?: any; update?: any; delete?: any } // Heuristic to identify action nodes
      ? { [Action in keyof T[K]]?: PermissionCheckValue<T[K][Action]> }
      : NestedPermissionMap<T[K]>) // Recurse for nested objects
    : never; // Should not happen with our current Permissions structure
};

// Defines the structure of roles and their associated permissions
type RolesWithPermissions = {
  [R in Role]: NestedPermissionMap<Permissions>;
};

const IBasicProfileInfoDtoParamsMap = nameOf<IBasicProfileInfoDto>();
type ProfileField =
  | typeof IBasicProfileInfoDtoParamsMap.firstName
  | typeof IBasicProfileInfoDtoParamsMap.lastName
  | typeof IBasicProfileInfoDtoParamsMap.gender
  | typeof IBasicProfileInfoDtoParamsMap.dateOfBirth
  | typeof IBasicProfileInfoDtoParamsMap.msTeamsEmailAddress
  | typeof IBasicProfileInfoDtoParamsMap.zoom

type ProfilePermissions = {
  [Field in ProfileField as string]: {
    action: 'view' | 'update';
    dataType: IPatchProfileInfoRequest;
  };
};

// Defines the overall permission structure, including nested resources.
// The leaf nodes of this type define the `dataType` expected for that specific action.
// Use `void` if no specific data is expected for the permission check.
type Permissions = {
  lessons: {
    view: Comment; // Data type for viewing a lesson (e.g., the lesson object itself)
    create: Comment; // Data type for creating a lesson
    update: Comment; // Data type for updating a lesson
  };
  groups: {
    view: Todo; // Data type for viewing a todo
    create: Todo; // Data type for creating a todo
    update: Todo; // Data type for updating a todo
    delete: Todo; // Data type for deleting a todo
  };
  settings: {
    city: {
      view: void; // No specific data for viewing city setting
      update: void; // No specific data for updating city setting
      create: void; // No specific data for creating city setting
    };
    country: {
      view: void; // No specific data for viewing country setting
      update: void; // No specific data for updating country setting
      create: void; // No specific data for creating country setting
    };
    [key: string]: { // Allow for additional fields under settings
      view: void;
      update: void;
      create: void;
    };
  };
  settingsProfile: ProfilePermissions;
};

const ROLES: RolesWithPermissions = {
  [IUserRole.PARENT]: {
    settings: {
      city: { view: true }, // Parent can view city setting
      country: { view: true },
      zoom: { view: false },
      msTeamsEmailAddress: { view: false },
      primaryEmail: { view: true },
    },
    settingsProfile: {
      view: true,
      update: true,
    },
    lessons: { view: true, create: true, update: true },
    groups: { view: true, create: true, update: true, delete: true },
  },
  [IUserRole.STUDENT]: {
    settings: {
      city: { view: true, update: false },
      country: { view: true, update: false },
      zoom: { view: false },
      msTeamsEmailAddress: { view: false },
      primaryEmail: { view: false },
    },
    settingsProfile: {
      view: true,
      update: false,
    },
    lessons: { view: true, create: false, update: false },
    groups: { view: true, create: false, update: false, delete: false },
  },
  [IUserRole.TEACHER]: {
    settings: {
      city: { view: true, update: true },
      country: { view: true, update: true },
      zoom: { view: true },
      msTeamsEmailAddress: { view: true },
      primaryEmail: { view: true },
    },
    settingsProfile: {
      view: true,
      update: true,
    },
    lessons: { view: true, create: true, update: true },
    groups: { view: true, create: true, update: true, delete: (user, todo) => todo.completed },
  },
  [IUserRole.ADMIN]: {
    settings: {
      city: { view: true, update: true, create: true },
      country: { view: true, update: true, create: true }
    },
    lessons: { view: true, create: true, update: true },
    groups: { view: true, create: true, update: true, delete: true },
  },
};

@Injectable({
  providedIn: 'root',
})
export class PermissionService {
  constructor() { }

  /**
   * Checks if a user has permission to perform a specific action on a resource or sub-resource.
   *
   * @param user The user for whom to check permissions.
   * @param path An array of strings representing the path to the specific action.
   * The last element of the array should be the action (e.g., 'view', 'create').
   * Examples: ['lessons', 'view'], ['settings', 'city', 'update'].
   * @param data Optional data relevant to the permission check. The type of this data
   * depends on the specific action being checked (as defined in the `Permissions` type).
   * @returns True if the user has permission, false otherwise.
   */
  hasPermission(
    user: IUserClaims,
    path: string[],
    data?: any // `any` is used here because inferring the specific `dataType` from a dynamic path is complex.
  ): boolean {
    const userRole = user.role as IUserRole;

    // Ensure the user's role is defined in our ROLES configuration.
    if (!ROLES[userRole]) {
      console.warn(`PermissionService: Role '${userRole}' not found in ROLES configuration.`);
      return false;
    }

    // Traverse the ROLES object based on the provided path.
    let currentPermissions: any = ROLES[userRole];
    const action = path[path.length - 1]; // The last element is the action
    const resourcePath = path.slice(0, -1); // The rest of the path is the resource/sub-resource

    for (const segment of resourcePath) {
      if (currentPermissions == null || typeof currentPermissions !== 'object' || !(segment in currentPermissions)) {
        return false; // Path segment not found in permissions
      }
      currentPermissions = currentPermissions[segment];
    }

    // Get the specific permission for the action at the end of the path.
    const permission = currentPermissions?.[action];

    // If no specific permission is defined (null or undefined), default to false.
    if (permission == null) {
      return false;
    }

    // If the permission is a boolean, return it directly.
    // If it's a function, execute the function with the user and provided data.
    // For `void` data types, the function will only receive `user`.
    if (typeof permission === 'boolean') {
      return permission;
    } else {
      // Check if the function expects data (by checking its arity or type).
      // For simplicity, we'll pass `data` if it's provided.
      // The `PermissionCheckValue` type handles the `void` case for the function signature.
      return permission(user, data);
    }
  }
}
