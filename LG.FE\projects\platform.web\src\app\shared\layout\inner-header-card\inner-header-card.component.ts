import { CommonModule, Location } from '@angular/common';
import { ChangeDetectionStrategy, Component, EventEmitter, inject, Input, Output, type OnInit } from '@angular/core';
import { BreadcrumbsComponent } from '@platform.src/app/shared/components/breadcrumbs/breadcrumbs.component';
import { ButtonModule } from 'primeng/button';
import { TooltipModule } from 'primeng/tooltip';
import { Router } from '@angular/router';

@Component({
  selector: 'app-inner-header-card',
  imports: [
    CommonModule,
    ButtonModule,
    BreadcrumbsComponent,
    TooltipModule,
  ],
  templateUrl: './inner-header-card.component.html',
  styleUrl: './inner-header-card.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class InnerHeaderCardComponent implements OnInit {
  @Input() breadcrumbs: any[] = [];
  @Input() isBackButton = false;
  @Input() actionButtonLabel: string = '';
  @Input() actionButtonIcon: string = 'pi pi-user-plus';
  @Input() actionButtonClass: string = 'submit-btn fly';
  @Input() canShowActionButton: boolean = true;
  @Input() fallbackUrl: string = '/'; // Add fallback URL input
  @Output() actionButtonClick = new EventEmitter<void>();
  location = inject(Location);
  router = inject(Router);
  onActionButtonClick() {
    if (this.isBackButton) {
      // Check if there's history state
      const navigationHistory = window.history.state?.navigationId;

      if (navigationHistory && navigationHistory > 1) {
        this.location.back();
      } else {
        // No history available, navigate to fallback URL
        this.router.navigate([this.fallbackUrl]);
      }
    } else {
      this.actionButtonClick.emit();
    }
  }
  ngOnInit(): void { }

}
