import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, signal } from '@angular/core';
import { ButtonModule } from 'primeng/button';
import { GalacticSuccessCardComponent, type GalacticSuccessConfig } from './galactic-success-card.component';

@Component({
  selector: 'app-galactic-success-card-example',
  imports: [
    CommonModule,
    ButtonModule,
    GalacticSuccessCardComponent
  ],
  template: `
    <!-- Success Theme Example -->
    <app-galactic-success-card 
      [config]="successConfig()"
      [size]="'medium'"
      [showOrbitalRing]="true"
      [enableAnimations]="true">
      
      <div slot="primary-actions" class="flex gap-3 justify-content-center flex-wrap">
        <p-button 
          label="Continue Journey" 
          icon="pi pi-arrow-right"
          styleClass="p-button-success p-button-raised"
          (onClick)="navigateToDashboard()">
        </p-button>
        <p-button 
          label="View Progress" 
          icon="pi pi-chart-line"
          styleClass="p-button-outlined p-button-success"
          (onClick)="viewProgress()">
        </p-button>
      </div>
      
      <div slot="secondary-actions" class="flex gap-2 justify-content-center flex-wrap">
        <p-button 
          label="Manage Students" 
          icon="pi pi-users"
          styleClass="p-button-text p-button-sm"
          (onClick)="manageStudents()">
        </p-button>
        <p-button 
          label="Settings" 
          icon="pi pi-cog"
          styleClass="p-button-text p-button-sm"
          (onClick)="openSettings()">
        </p-button>
      </div>
    </app-galactic-success-card>

    <!-- Primary Theme Example -->
    <app-galactic-success-card 
      [config]="primaryConfig()"
      [size]="'large'"
      [showOrbitalRing]="false"
      [enableAnimations]="true"
      customClasses="mt-4">
      
      <div slot="primary-actions" class="flex gap-3 justify-content-center">
        <p-button 
          label="Get Started" 
          icon="pi pi-play"
          styleClass="p-button-primary p-button-raised">
        </p-button>
      </div>
    </app-galactic-success-card>

    <!-- Warning Theme Example -->
    <app-galactic-success-card 
      [config]="warningConfig()"
      [size]="'small'"
      [showOrbitalRing]="true"
      [enableAnimations]="false"
      customClasses="mt-4">
      
      <div slot="primary-actions" class="flex gap-2 justify-content-center">
        <p-button 
          label="Review" 
          icon="pi pi-eye"
          styleClass="p-button-warning p-button-sm">
        </p-button>
        <p-button 
          label="Fix Issues" 
          icon="pi pi-wrench"
          styleClass="p-button-outlined p-button-warning p-button-sm">
        </p-button>
      </div>
    </app-galactic-success-card>
  `,
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class GalacticSuccessCardExampleComponent {
  
  successConfig = signal<GalacticSuccessConfig>({
    title: 'Trial Request Confirmed!',
    subtitle: 'Your galactic learning journey begins now',
    badgeText: 'FREE',
    badgeIcon: 'pi pi-star-fill',
    equipmentItems: ['Interactive Lessons', 'Progress Tracking', 'Expert Support'],
    showDestinationName: true,
    destinationName: 'Spanish',
    theme: 'success'
  });

  primaryConfig = signal<GalacticSuccessConfig>({
    title: 'Welcome Aboard!',
    subtitle: 'Ready to explore new horizons?',
    badgeText: 'PREMIUM',
    badgeIcon: 'pi pi-crown',
    equipmentItems: ['Advanced Features', 'Priority Support'],
    showDestinationName: false,
    theme: 'primary'
  });

  warningConfig = signal<GalacticSuccessConfig>({
    title: 'Action Required',
    subtitle: 'Please review your settings',
    badgeText: 'PENDING',
    badgeIcon: 'pi pi-exclamation-triangle',
    equipmentItems: ['Profile Incomplete', 'Verification Needed'],
    showDestinationName: false,
    theme: 'warning'
  });

  navigateToDashboard(): void {
    console.log('Navigating to dashboard...');
  }

  viewProgress(): void {
    console.log('Viewing progress...');
  }

  manageStudents(): void {
    console.log('Managing students...');
  }

  openSettings(): void {
    console.log('Opening settings...');
  }
}
