import { ComponentFixture, TestBed } from '@angular/core/testing';
import { CommonModule } from '@angular/common';
import { TableModule } from 'primeng/table';
import { DataGridHeaderFooterComponent } from './data-grid-header-footer.component';
import { IDataGridFields } from 'SharedModules.Library';

describe('DataGridHeaderFooterComponent', () => {
  let component: DataGridHeaderFooterComponent;
  let fixture: ComponentFixture<DataGridHeaderFooterComponent>;

  const mockColumns: IDataGridFields[] = [
    { field: 'firstName', header: 'First Name', sortable: true },
    { field: 'lastName', header: 'Last Name', sortable: true },
    { field: 'email', header: 'Email', sortable: false },
    { field: 'country', header: 'Country', sortable: true, maxWidth: '200px' }
  ];

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [
        CommonModule,
        TableModule,
        DataGridHeaderFooterComponent
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(DataGridHeaderFooterComponent);
    component = fixture.componentInstance;
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should have default input values', () => {
    expect(component.columns).toEqual([]);
    expect(component.showHeader).toBe(true);
    expect(component.showFooter).toBe(true);
    expect(component.sortable).toBe(true);
    expect(component.showActionsColumn).toBe(true);
    expect(component.actionsColumnHeader).toBe('Actions');
    expect(component.actionsColumnWidth).toBe('100px');
  });

  it('should generate correct column style', () => {
    const columnWithMaxWidth = { field: 'test', header: 'Test', maxWidth: '200px' };
    const columnWithoutMaxWidth = { field: 'test2', header: 'Test2' };

    expect(component.getColumnStyle(columnWithMaxWidth))
      .toBe('max-width:200px !important; width:max-content !important;');
    expect(component.getColumnStyle(columnWithoutMaxWidth))
      .toBe('width:max-content !important;');
  });

  it('should determine if column is sortable', () => {
    component.sortable = true;
    const sortableColumn = { field: 'test', header: 'Test', sortable: true };
    const nonSortableColumn = { field: 'test2', header: 'Test2', sortable: false };

    expect(component.isColumnSortable(sortableColumn)).toBe(true);
    expect(component.isColumnSortable(nonSortableColumn)).toBe(false);

    component.sortable = false;
    expect(component.isColumnSortable(sortableColumn)).toBe(false);
  });

  it('should determine if columns are reorderable', () => {
    component.reorderable = true;
    expect(component.isColumnReorderable()).toBe(true);

    component.reorderable = false;
    expect(component.isColumnReorderable()).toBe(false);
  });

  it('should accept column input', () => {
    component.columns = mockColumns;
    expect(component.columns).toEqual(mockColumns);
    expect(component.columns.length).toBe(4);
  });
});
