.login-container {
  background: var(--surface-ground);
  min-height: 100vh;
  
  .login-wrapper {
    background: linear-gradient(180deg, var(--primary-400) 0%, var(--primary-700) 100%);
    position: relative;
    overflow: hidden;
      
 
  }
  
@keyframes patternMove {
  0% {
    background-position: 0 0;
  }
  100% {
    background-position: 1000px 1000px;
  }
}
  .login-box {
      position: relative;
      backdrop-filter: blur(10px);
      animation: fadeInUp 0.5s ease-out;
      
      &::before {
          content: '';
          position: absolute;
          inset: 0;
          border-radius: inherit;
          padding: 2px;
          background: linear-gradient(180deg, rgba(255,255,255,0.2), rgba(255,255,255,0.05));
          mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
          -webkit-mask-composite: xor;
          mask-composite: exclude;
          pointer-events: none;
      }
  }
  
  :host ::ng-deep {
      .p-inputtext {
          &:enabled:focus {
              box-shadow: 0 0 0 2px var(--surface-ground), 0 0 0 4px var(--primary-200);
          }
          
          &.ng-dirty.ng-invalid {
              border-color: var(--red-500);
              
              &:enabled:focus {
                  box-shadow: 0 0 0 2px var(--surface-ground), 0 0 0 4px var(--red-200);
              }
          }
      }
      
      .p-checkbox {
          .p-checkbox-box {
              &.p-highlight {
                  background: var(--primary-500);
                  border-color: var(--primary-500);
                  
                  &:hover {
                      background: var(--primary-600);
                      border-color: var(--primary-600);
                  }
              }
          }
      }
      
      .p-button {
          background: var(--primary-500);
          border-color: var(--primary-500);
          
          &:enabled:hover {
              background: var(--primary-600);
              border-color: var(--primary-600);
          }
          
          &:enabled:active {
              background: var(--primary-700);
              border-color: var(--primary-700);
          }
          
          &.p-button-loading {
              background: var(--primary-600);
              border-color: var(--primary-600);
          }
      }
  }
}

@keyframes fadeInUp {
  from {
      opacity: 0;
      transform: translateY(20px);
  }
  to {
      opacity: 1;
      transform: translateY(0);
  }
}

// Responsive adjustments
@media screen and (max-width: 576px) {
  .login-container {
      .login-box {
          margin: 1rem;
          padding: 2rem !important;
      }
  }
}