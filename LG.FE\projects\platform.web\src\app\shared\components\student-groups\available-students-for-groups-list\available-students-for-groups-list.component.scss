@use "mixins";

// Modern color palette
$primary: #6366f1;
$primary-50: #eef2ff;
$primary-100: #e0e7ff;
$primary-600: #4f46e5;
$primary-700: #4338ca;

$success: #10b981;
$success-50: #ecfdf5;
$success-100: #d1fae5;

$warning: #f59e0b;
$warning-50: #fffbeb;
$warning-100: #fef3c7;

$info: #3b82f6;
$info-50: #eff6ff;
$info-100: #dbeafe;

// Cosmic Color Palette for Galactic Theme
$cosmic-deep: #0f0f23;
$cosmic-blue: #1e1b4b;
$cosmic-purple: #4c1d95;
$cosmic-indigo: #312e81;
$cosmic-violet: #5b21b6;
$starlight: #f8fafc;
$nebula-pink: #ec4899;
$nebula-cyan: #06b6d4;
$cosmic-gold: #fbbf24;

$gray-50: #f9fafb;
$gray-100: #f3f4f6;
$gray-200: #e5e7eb;
$gray-300: #d1d5db;
$gray-400: #9ca3af;
$gray-500: #6b7280;
$gray-600: #4b5563;
$gray-700: #374151;
$gray-800: #1f2937;
$gray-900: #111827;

// Professional shadows
$shadow-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
$shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px -1px rgba(0, 0, 0, 0.1);
$shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -2px rgba(0, 0, 0, 0.1);
$shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -4px rgba(0, 0, 0, 0.1);

// Smooth transitions
$transition-fast: all 0.15s ease-out;
$transition-base: all 0.2s ease-out;
$transition-slow: all 0.3s ease-out;

:host {
  display: block;
  width: 100%;
}

// Create Group Action Section
.create-group-section {
  .student-count-info {
    .count-text {
      font-size: 0.875rem;
      font-weight: 500;
      color: $gray-600;

      @include mixins.breakpoint(mobile) {
        font-size: 0.8125rem;
      }
    }
  }
}

// Minimal Divider Section
.divider-section {
  .divider-container {
    .divider-line {
      height: 1px;
      background: linear-gradient(90deg, transparent, $gray-300, transparent);
    }

    .divider-text {
      font-size: 0.875rem;
      font-weight: 500;
      color: $gray-600;
      white-space: nowrap;
      padding: 0 0.75rem;
      text-transform: uppercase;
      letter-spacing: 0.025em;

      @include mixins.breakpoint(mobile) {
        font-size: 0.8125rem;
        padding: 0 0.5rem;
      }
    }
  }
}

// Groups Header Styling (matches suggestions header)
.groups-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: 0.5rem;

  @include mixins.breakpoint(mobile) {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.75rem;
  }

  .header-content {
    flex: 1;

    .header-badge {
      .badge-text {
        font-size: 0.75rem;
        font-weight: 600;
        color: $primary;
        text-transform: uppercase;
        letter-spacing: 0.5px;

        @include mixins.breakpoint(mobile) {
          font-size: 0.6875rem;
        }
      }

      i {
        font-size: 0.875rem;
        color: $primary;

        @include mixins.breakpoint(mobile) {
          font-size: 0.8125rem;
        }
      }
    }

    .header-title {
      font-size: 1.125rem;
      font-weight: 700;
      color: $gray-900;
      letter-spacing: -0.025em;
      line-height: 1.2;

      @include mixins.breakpoint(mobile) {
        font-size: 1rem;
      }
    }

    .header-subtitle {
      font-size: 0.875rem;
      color: $gray-600;
      line-height: 1.4;

      @include mixins.breakpoint(mobile) {
        font-size: 0.8125rem;
      }
    }
  }

  .groups-count {
    .count-badge {
      display: inline-flex;
      align-items: center;
      padding: 0.25rem 0.625rem;
      background: linear-gradient(135deg, $success-100 0%, $primary-100 100%);
      color: $primary-700;
      border-radius: 20px;
      font-size: 0.75rem;
      font-weight: 600;
      border: 1px solid $success-100;

      @include mixins.breakpoint(mobile) {
        font-size: 0.6875rem;
        padding: 0.1875rem 0.5rem;
      }
    }
  }
}

// Modern Suggestions Container
.suggestions-container {
  max-width: 100%;
  margin: 0 auto;

  // Suggestions Header
  .suggestions-header {
    margin-bottom: 1rem;

    @include mixins.breakpoint(mobile) {
      margin-bottom: 0.875rem;
    }

    .header-badge {
      .badge-text {
        font-size: 0.75rem;
        font-weight: 600;
        color: $primary;
        text-transform: uppercase;
        letter-spacing: 0.5px;

        @include mixins.breakpoint(mobile) {
          font-size: 0.6875rem;
        }
      }

      i {
        font-size: 0.875rem;
        color: $primary;

        @include mixins.breakpoint(mobile) {
          font-size: 0.8125rem;
        }
      }
    }

    .header-title {
      font-size: 1.125rem;
      font-weight: 700;
      color: $gray-900;
      letter-spacing: -0.025em;
      line-height: 1.2;

      @include mixins.breakpoint(mobile) {
        font-size: 1rem;
      }
    }

    .header-subtitle {
      font-size: 0.875rem;
      color: $gray-600;
      line-height: 1.4;

      @include mixins.breakpoint(mobile) {
        font-size: 0.8125rem;
      }
    }

    .count-badge {
      display: inline-flex;
      align-items: center;
      padding: 0.25rem 0.625rem;
      background: linear-gradient(135deg, $success-100 0%, $primary-100 100%);
      color: $primary-700;
      border-radius: 20px;
      font-size: 0.75rem;
      font-weight: 600;
      border: 1px solid $success-100;

      @include mixins.breakpoint(mobile) {
        font-size: 0.6875rem;
        padding: 0.1875rem 0.5rem;
      }
    }
  }

  // Loading State
  .loading-state {
    padding: 2rem 1.5rem;
    background: linear-gradient(135deg, $primary-50 0%, $success-50 100%);
    border: 1px solid $primary-100;
    border-radius: 12px;

    @include mixins.breakpoint(mobile) {
      padding: 1.5rem 1rem;
    }

    .loading-text {
      font-size: 0.875rem;
      font-weight: 500;
      color: $gray-700;
    }
  }

  // Error State
  .error-state {
    padding: 2rem 1.5rem;
    background: linear-gradient(135deg, $warning-50 0%, $gray-50 100%);
    border: 1px solid $warning-100;
    border-radius: 12px;

    @include mixins.breakpoint(mobile) {
      padding: 1.5rem 1rem;
    }

    .error-icon {
      width: 48px;
      height: 48px;
      background: $warning-100;
      border-radius: 50%;

      @include mixins.breakpoint(mobile) {
        width: 40px;
        height: 40px;
      }
    }

    .error-title {
      font-size: 1.125rem;
      font-weight: 700;
      color: $gray-900;
      letter-spacing: -0.025em;

      @include mixins.breakpoint(mobile) {
        font-size: 1rem;
      }
    }

    .error-message {
      font-size: 0.875rem;
      line-height: 1.5;
      color: $gray-600;

      @include mixins.breakpoint(mobile) {
        font-size: 0.8125rem;
      }
    }

    .retry-btn {
      padding: 0.625rem 1.25rem;
      background: $warning;
      color: white;
      border: 2px solid $warning;
      border-radius: 8px;
      font-size: 0.875rem;
      font-weight: 600;
      cursor: pointer;
      transition: $transition-base;
      outline: none;

      @include mixins.breakpoint(mobile) {
        padding: 0.5rem 1rem;
        font-size: 0.8125rem;
      }

      &:hover {
        background: darken($warning, 10%);
        border-color: darken($warning, 10%);
      }

      &:focus {
        box-shadow: 0 0 0 3px rgba(245, 158, 11, 0.2);
      }
    }
  }

  // Suggestions List - Enhanced compactness with reduced gaps

  .suggestion-card {
    background: white;
    border: 1px solid $gray-200;
    border-radius: 12px;
    padding: 0.875rem 1rem;
    transition: $transition-base;
    position: relative;
    overflow: hidden;

    @include mixins.breakpoint(mobile) {
      padding: 0.75rem 0.875rem;
      border-radius: 10px;
    }

    // Suggestion highlighting


    &:hover {
      border-color: $primary-100;
      box-shadow: $shadow-md;
      background: linear-gradient(135deg, $primary-50 0%, white 100%);

      .invite-btn {
        background: $primary-600;
        border-color: $primary-600;
        color: white;
      }
    }


    .student-avatar {
      width: 40px;
      height: 40px;
      background: linear-gradient(135deg, $primary-100 0%, $success-100 100%);
      border-radius: 50%;
      border: 2px solid white;
      box-shadow: $shadow-sm;

      @include mixins.breakpoint(mobile) {
        width: 36px;
        height: 36px;
      }

      i {
        font-size: 1.125rem;
        color: $primary-600;

        @include mixins.breakpoint(mobile) {
          font-size: 1rem;
        }
      }
    }

    .student-name {
      font-size: 1rem;
      font-weight: 700;
      color: $gray-900;
      letter-spacing: -0.025em;
      line-height: 1.3;

      @include mixins.breakpoint(mobile) {
        font-size: 0.9375rem;
      }
    }

    .compatibility-badge {
      display: inline-flex;
      align-items: center;
      gap: 0.25rem;
      padding: 0.125rem 0.375rem;
      background: linear-gradient(135deg, $success 0%, $success-100 100%);
      color: white;
      border-radius: 12px;
      font-size: 0.6875rem;
      font-weight: 600;
      text-transform: uppercase;
      letter-spacing: 0.25px;

      @include mixins.breakpoint(mobile) {
        font-size: 0.625rem;
        padding: 0.1rem 0.3rem;
      }

      i {
        font-size: 0.625rem;
        color: $warning;

        @include mixins.breakpoint(mobile) {
          font-size: 0.5625rem;
        }
      }
    }

    .username-info {
      font-size: 0.75rem;
      font-weight: 500;

      @include mixins.breakpoint(mobile) {
        font-size: 0.6875rem;
      }

      i {
        color: $gray-500;
        font-size: 0.75rem;

        @include mixins.breakpoint(mobile) {
          font-size: 0.6875rem;
        }
      }
    }

    .learning-benefit {
      font-size: 0.75rem;
      font-weight: 500;
      font-style: italic;

      @include mixins.breakpoint(mobile) {
        font-size: 0.6875rem;
      }

      i {
        font-size: 0.75rem;
        margin-right: 0.125rem;

        @include mixins.breakpoint(mobile) {
          font-size: 0.6875rem;
        }
      }
    }

    .invite-btn {
      padding: 0.5rem 0.875rem;
      background: linear-gradient(135deg, $primary-100 0%, $primary-100 100%);
      color: $primary-700;
      border: 1px solid $primary-100;
      border-radius: 8px;
      font-size: 0.75rem;
      font-weight: 600;
      cursor: pointer;
      transition: $transition-base;
      outline: none;
      white-space: nowrap;

      @include mixins.breakpoint(mobile) {
        padding: 0.375rem 0.75rem;
        font-size: 0.6875rem;
      }

      &:hover {
        background: linear-gradient(135deg, $primary 0%, $primary-600 100%);
        border-color: $primary;
        color: white;
        box-shadow: $shadow-sm;
      }

      &:focus {
        box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.2);
      }

      i {
        font-size: 0.75rem;

        @include mixins.breakpoint(mobile) {
          font-size: 0.6875rem;
        }
      }
    }
  }

  // Empty State
  .empty-state {
    padding: 2.5rem 1.5rem;
    background: linear-gradient(135deg, $gray-50 0%, $primary-50 100%);
    border: 2px dashed $gray-300;
    border-radius: 12px;

    @include mixins.breakpoint(mobile) {
      padding: 2rem 1rem;
    }

    .empty-icon {
      width: 64px;
      height: 64px;
      background: $gray-100;
      border-radius: 50%;

      @include mixins.breakpoint(mobile) {
        width: 56px;
        height: 56px;
      }
    }

    .empty-title {
      font-size: 1.25rem;
      font-weight: 700;
      color: $gray-900;
      letter-spacing: -0.025em;

      @include mixins.breakpoint(mobile) {
        font-size: 1.125rem;
      }
    }

    .empty-message {
      font-size: 0.875rem;
      max-width: 450px;

      @include mixins.breakpoint(mobile) {
        font-size: 0.8125rem;
      }
    }

    .refresh-btn {
      padding: 0.625rem 1.25rem;
      background: linear-gradient(135deg, $primary 0%, $primary-600 100%);
      color: white;
      border: 2px solid $primary;
      border-radius: 8px;
      font-size: 0.875rem;
      font-weight: 600;
      cursor: pointer;
      transition: $transition-base;
      outline: none;

      @include mixins.breakpoint(mobile) {
        padding: 0.5rem 1rem;
        font-size: 0.8125rem;
      }

      &:hover {
        background: linear-gradient(135deg, $primary 0%, $primary-600 100%);
        border-color: $primary;
        color: white;
        transform: scale(1.02);
      }

      &:focus {
        box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.2);
      }
    }
  }
}



.available-groups-container {
  max-width: 100%;
  margin: 0 auto;

  // Minimal spacing adjustments
  &.mt-3 {
    margin-top: 1rem;

    @include mixins.breakpoint(mobile) {
      margin-top: 0.75rem;
    }
  }

    .header-content {
      flex: 1;

      .header-title {
        font-size: 1rem;
        font-weight: 700;
        color: $gray-900;
        margin: 0 0 0.25rem;
        letter-spacing: -0.025em;
        line-height: 1.2;

        @include mixins.breakpoint(mobile) {
          font-size: 1.375rem;
        }
      }

      .header-subtitle {
        font-size: 0.875rem;
        color: $gray-600;
        margin: 0;
        line-height: 1.4;

        @include mixins.breakpoint(mobile) {
          font-size: 0.8125rem;
        }
      }
    }

    .groups-count {
      .count-badge {
        display: inline-flex;
        align-items: center;
        padding: 0.375rem 0.75rem;
        background: $primary-100;
        color: $primary-700;
        border-radius: 8px;
        font-size: 0.8125rem;
        font-weight: 600;
        border: 1px solid $primary-100;

        @include mixins.breakpoint(mobile) {
          font-size: 0.75rem;
          padding: 0.25rem 0.625rem;
        }
      }
    }
  }

  // Groups List
  .groups-list {
    display: flex;
    flex-direction: column;
    gap: 0.875rem;

    @include mixins.breakpoint(mobile) {
      gap: 0.75rem;
    }

    .group-card {
      background: white;
      border: 1px solid $gray-200;
      border-radius: 12px;
      padding: 1.25rem;
      transition: $transition-base;
      cursor: pointer;
      position: relative;
      overflow: hidden;

      @include mixins.breakpoint(mobile) {
        padding: 1rem;
        border-radius: 10px;
      }

      &:hover {
        border-color: $primary-600;
        box-shadow: $shadow-md;

        .join-btn {
          background: $primary-600;
          border-color: $primary-600;
        }
      }


      // Group Header
      .group-header {
        display: flex;
        align-items: flex-start;
        justify-content: space-between;
        margin-bottom: 1rem;

        @include mixins.breakpoint(mobile) {
          margin-bottom: 0.875rem;
        }

        .group-info {
          flex: 1;
          min-width: 0;

          .group-name {
            font-size: 1.125rem;
            font-weight: 700;
            color: $gray-900;
            margin: 0 0 0.5rem;
            letter-spacing: -0.025em;
            line-height: 1.3;

            @include mixins.breakpoint(mobile) {
              font-size: 1rem;
              margin-bottom: 0.375rem;
            }
          }

          .group-meta {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            flex-wrap: wrap;

            @include mixins.breakpoint(mobile) {
              gap: 0.5rem;
            }

            .language-info {
              display: flex;
              align-items: center;
              gap: 0.375rem;
              font-size: 0.8125rem;
              color: $gray-600;
              font-weight: 500;

              @include mixins.breakpoint(mobile) {
                font-size: 0.75rem;
              }

              i {
                color: $gray-500;
                font-size: 0.875rem;

                @include mixins.breakpoint(mobile) {
                  font-size: 0.8125rem;
                }
              }
            }

            .level-badge {
              display: inline-flex;
              align-items: center;
              padding: 0.25rem 0.5rem;
              border-radius: 6px;
              font-size: 0.75rem;
              font-weight: 600;
              text-transform: uppercase;
              letter-spacing: 0.025em;

              @include mixins.breakpoint(mobile) {
                font-size: 0.6875rem;
                padding: 0.1875rem 0.375rem;
              }

              &.level-beginner {
                background: $success-100;
                color: $success;
                border: 1px solid $success-100;
              }

              &.level-intermediate {
                background: $warning-100;
                color: $warning-100;
                border: 1px solid $warning-100;
              }

              &.level-advanced {
                background: $info-100;
                color: $info-100;
                border: 1px solid $info-100;
              }
            }
          }
        }

        .group-stats {
          display: flex;
          align-items: center;
          gap: 0.75rem;

          .member-count {
            display: flex;
            align-items: center;
            gap: 0.375rem;
            padding: 0.375rem 0.625rem;
            background: $gray-100;
            border-radius: 8px;
            font-size: 0.8125rem;
            font-weight: 600;
            color: $gray-700;

            @include mixins.breakpoint(mobile) {
              font-size: 0.75rem;
              padding: 0.25rem 0.5rem;
            }

            i {
              color: $gray-500;
              font-size: 0.875rem;

              @include mixins.breakpoint(mobile) {
                font-size: 0.8125rem;
              }
            }
          }
        }
      }

      // Group Actions
      .group-actions {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        justify-content: flex-end;

        @include mixins.breakpoint(mobile) {
          gap: 0.5rem;
        }

        .join-btn {
          display: flex;
          align-items: center;
          gap: 0.375rem;
          padding: 0.625rem 1rem;
          background: $primary;
          color: white;
          border: 2px solid $primary;
          border-radius: 8px;
          font-size: 0.8125rem;
          font-weight: 600;
          cursor: pointer;
          transition: $transition-base;
          outline: none;

          @include mixins.breakpoint(mobile) {
            padding: 0.5rem 0.875rem;
            font-size: 0.75rem;
          }

          &:hover {
            background: $primary-600;
            border-color: $primary-600;
            box-shadow: $shadow-sm;
          }
          &:focus {
            box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.2);
          }

          i {
            font-size: 0.875rem;

            @include mixins.breakpoint(mobile) {
              font-size: 0.8125rem;
            }
          }
        }

        .info-btn {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 36px;
          height: 36px;
          background: $gray-100;
          color: $gray-600;
          border: 1px solid $gray-200;
          border-radius: 8px;
          cursor: pointer;
          transition: $transition-base;
          outline: none;

          @include mixins.breakpoint(mobile) {
            width: 32px;
            height: 32px;
          }

          &:hover {
            background: $gray-200;
            color: $gray-700;
            border-color: $gray-300;
          }

          &:focus {
            box-shadow: 0 0 0 3px rgba(107, 114, 128, 0.1);
          }

          i {
            font-size: 0.875rem;

            @include mixins.breakpoint(mobile) {
              font-size: 0.8125rem;
            }
          }
        }
      }
    }
  }

  // Empty State
  .empty-state {
    text-align: center;
    padding: 3rem 1.5rem;
    background: white;
    border: 2px dashed $gray-200;
    border-radius: 12px;

    @include mixins.breakpoint(mobile) {
      padding: 2rem 1rem;
    }

    .empty-icon {
      width: 64px;
      height: 64px;
      background: $gray-100;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 0 auto 1.5rem;

      @include mixins.breakpoint(mobile) {
        width: 56px;
        height: 56px;
        margin-bottom: 1.25rem;
      }

      i {
        font-size: 1.5rem;
        color: $gray-400;

        @include mixins.breakpoint(mobile) {
          font-size: 1.375rem;
        }
      }
    }

    .empty-title {
      font-size: 1.25rem;
      font-weight: 700;
      color: $gray-900;
      margin: 0 0 0.75rem;
      letter-spacing: -0.025em;

      @include mixins.breakpoint(mobile) {
        font-size: 1.125rem;
        margin-bottom: 0.5rem;
      }
    }

    .empty-message {
      font-size: 0.875rem;
      color: $gray-600;
      margin: 0 0 2rem;
      line-height: 1.5;
      max-width: 400px;
      margin-left: auto;
      margin-right: auto;

      @include mixins.breakpoint(mobile) {
        font-size: 0.8125rem;
        margin-bottom: 1.5rem;
      }
    }

    .refresh-btn {
      display: inline-flex;
      align-items: center;
      gap: 0.5rem;
      padding: 0.75rem 1.5rem;
      background: white;
      color: $gray-700;
      border: 2px solid $gray-300;
      border-radius: 8px;
      font-size: 0.875rem;
      font-weight: 600;
      cursor: pointer;
      transition: $transition-base;
      outline: none;

      @include mixins.breakpoint(mobile) {
        padding: 0.625rem 1.25rem;
        font-size: 0.8125rem;
      }

      &:hover {
        background: $gray-50;
        border-color: $gray-400;
        color: $gray-800;
      }

      &:focus {
        box-shadow: 0 0 0 3px rgba(107, 114, 128, 0.1);
      }

      i {
        font-size: 0.875rem;

        @include mixins.breakpoint(mobile) {
          font-size: 0.8125rem;
        }
      }
    }
  }

