<!-- Teachers List Filters Content Only -->
<div class="filters-content">
  <div class="grid">

        <!-- Basic Filters -->
        <div class="col-12">
          <div class="flex flex-column gap-1">
            <!-- Gender Dropdown -->
            <div class="field">
              <label for="gender" class="block text-sm mb-2">Gender</label>
              <p-dropdown
                id="gender"
                [options]="enumDropdownOptionsService.genderOptions"
                [ngModel]="currentFilters().gender"
                (ngModelChange)="onFilterChange('gender', $event)"
                placeholder="Select Gender"
                optionLabel="label"
                optionValue="value"
                class="w-full">
              </p-dropdown>
            </div>
          </div>
        </div>

        <!-- Language Filters -->
        <div class="col-12">
          <div class="flex flex-column gap-1">
            <!-- Teaching Language Dropdown -->
            <div class="field">
              <label for="teachingLanguage" class="block text-sm mb-2">Teaching Language</label>
              <p-dropdown
                id="teachingLanguage"
                [options]="filterState.teachingLanguages"
                [ngModel]="currentFilters().teachingLanguage"
                (ngModelChange)="onFilterChange('teachingLanguage', $event)"
                placeholder="Select Teaching Language"
                optionLabel="name"
                optionValue="id"
                class="w-full">
              </p-dropdown>
            </div>

            <!-- Native Language Dropdown -->
            <div class="field">
              <label for="speakingLanguage" class="block text-sm mb-2">Speaking Language</label>
              <p-dropdown
                id="speakingLanguage"
                [options]="filterState.nativeLanguages"
                [filter]="true"
                [ngModel]="currentFilters().speakingLanguage"
                (ngModelChange)="onFilterChange('speakingLanguage', $event)"
                placeholder="Select Speaking Language"
                class="w-full">
              </p-dropdown>
            </div>
          </div>
        </div>

        <!-- Status and Experience -->
        <div class="col-12">
          <div class="flex flex-column gap-1">
            <!-- Availability Status MultiSelect -->
            <div class="field">
              <label for="availabilityStatus" class="block text-sm mb-2">Availability Status</label>
              <p-multiSelect
                id="availabilityStatus"
                class="w-full"
                [options]="enumDropdownOptionsService.availabilityStatusEnumFlagsOptions"
                [ngModel]="selectedAvailabilityStatuses()"
                (ngModelChange)="onAvailabilityStatusChange($event)"
                defaultLabel="Select Availability Statuses"
                optionLabel="label"
                optionValue="value"
                display="chip">
              </p-multiSelect>
            </div>

            <!-- Show Only Active Toggle -->
            <div class="field-checkbox">
              <p-checkbox
                id="includeBlocked"
                [ngModel]="currentFilters().includeBlocked"
                (ngModelChange)="onFilterChange('includeBlocked', $event)"
                [binary]="true">
              </p-checkbox>
              <label for="includeBlocked" class="ml-2">Include Blocked</label>
            </div>
          </div>
        </div>

        <!-- Teaching Experience -->
        <div class="col-12">
          <div class="flex flex-column gap-1">
            <!-- Teaching Ages Experience MultiSelect -->
            <div class="field">
              <label for="teachingAgesExperience" class="block text-sm mb-2">Teaching Ages Experience</label>
              <p-multiSelect
                id="teachingAgesExperience"
                class="w-full"
                [options]="enumDropdownOptionsService.teachingAgesExperienceEnumFlagsOptions"
                [ngModel]="selectedTeachingAgesExperience()"
                (ngModelChange)="onTeachingAgesExperienceChange($event)"
                defaultLabel="Select Experience"
                optionLabel="label"
                optionValue="value"
                display="chip">
              </p-multiSelect>
            </div>

            <!-- Student Ages Preference MultiSelect -->
            <div class="field">
              <label for="teacherStudentAgesPreference" class="block text-sm mb-2">Student Ages Preference</label>
              <p-multiSelect
                id="teacherStudentAgesPreference"
                class="w-full"
                [options]="enumDropdownOptionsService.teacherStudentAgesPreferenceEnumFlagsOptions"
                [ngModel]="selectedTeacherStudentAgesPreference()"
                (ngModelChange)="onStudentAgesPreferenceChange($event)"
                defaultLabel="Select Preferences"
                optionLabel="label"
                optionValue="value"
                display="chip">
              </p-multiSelect>
            </div>
          </div>
        </div>

        <!-- Date Range -->
        <div class="col-12">
          <div class="flex flex-column gap-1">
            <label class="block text-sm mb-2">Approved Date Range</label>
            <div class="flex gap-2">
              <p-datepicker
                id="approvedDateFrom"
                [ngModel]="currentFilters().approvedDateFrom"
                (ngModelChange)="onApprovedDateFromChange($event)"
                dateFormat="yy-mm-dd"
                placeholder="From"
                [appendTo]="'body'"
                class="flex-1">
              </p-datepicker>
              <p-datepicker
                id="approvedDateTo"
                [ngModel]="currentFilters().approvedDateTo"
                (ngModelChange)="onApprovedDateToChange($event)"
                dateFormat="yy-mm-dd"
                placeholder="To"
                [appendTo]="'body'"
                class="flex-1">
              </p-datepicker>
            </div>
          </div>
        </div>

        <!-- Age Range Slider -->
        <div class="col-12">
          <div class="flex flex-column gap-1">
            <div class="field p-2">
              <label class="block text-sm mb-3">
                Student Ages Range ({{ studentAgesRange()[0] }} - {{ studentAgesRange()[1] }})
              </label>
              <div style="padding: 0 7px">
                <p-slider
                  [ngModel]="studentAgesRange()"
                  (ngModelChange)="onStudentAgesRangeChange($event)"
                  [range]="true"
                  [min]="2"
                  [max]="17"
                  class="w-full">
                </p-slider>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
