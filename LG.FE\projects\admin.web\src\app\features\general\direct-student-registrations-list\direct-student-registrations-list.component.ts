import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, computed, inject, model, signal, ViewChild } from '@angular/core';
import { FormBuilder, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { MessageService } from 'primeng/api';
import { Table, TableModule } from 'primeng/table';
import { DialogModule } from 'primeng/dialog';
import { InputTextModule } from 'primeng/inputtext';
import { CheckboxModule } from 'primeng/checkbox';
import { TooltipModule } from 'primeng/tooltip';
import { ProgressSpinnerModule } from 'primeng/progressspinner';
import { ButtonModule } from 'primeng/button';
import { TextareaModule } from 'primeng/textarea';

// Assuming these imports are correctly resolved from your SharedModules.Library
import { CommonService, HandleApiResponseService, ToastService, IDirectStudentRegistrationDto, IStudents, IGetConflictedStudentDirectRegistrationsRequest, IResolveConflictedStudentDirectRegistrationRequest, IGetConflictedStudentDirectRegistrationResponse, IDataGridFields, nameOf, CustomDialogPopupComponent } from 'SharedModules.Library';

import moment from 'moment-timezone'; // Import moment.js
import { IconFieldModule } from 'primeng/iconfield';
import { InputIconModule } from 'primeng/inputicon';


@Component({
  selector: 'app-direct-student-registrations-list',
  standalone: true,
  imports: [
    CommonModule,
    TableModule,
    FormsModule,
    ButtonModule,
    DialogModule,
    ReactiveFormsModule,
    InputTextModule,
    CheckboxModule,
    TooltipModule,
    TextareaModule,
    ProgressSpinnerModule,
    IconFieldModule,
    InputIconModule,
    CustomDialogPopupComponent,
  ],
  templateUrl: './direct-student-registrations-list.component.html',
  styleUrl: './direct-student-registrations-list.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class DirectStudentRegistrationsListComponent {

  @ViewChild('dt') dt!: Table; // Non-null assertion operator

  // Services
  commonService = inject(CommonService);
  private handleApiService = inject(HandleApiResponseService);
  private formBuilder = inject(FormBuilder);
  private messageService = inject(MessageService); // Keep if needed, otherwise remove
  private toastService = inject(ToastService);

  // Signals and State
  registrations = signal<IDirectStudentRegistrationDto[]>([]);
  registrations$ = computed(() => this.registrations());
  loading = computed(() => this.commonService.isLoading$());
  showReviewedRegistrations = model<boolean>(true);
  showReviewedRegistrations$ = computed(() => this.showReviewedRegistrations());

  // Dialog state
  dialogVisible = model<boolean>(false);
  dialogVisible$ = computed(() => this.dialogVisible());
  selectedRegistration = signal<IDirectStudentRegistrationDto | null>(null);
  selectedRegistration$ = computed(() => this.selectedRegistration());
  selectedColumns = signal<IDataGridFields[]>([]);

  // Form
  registrationForm: FormGroup;
  submitting = signal<boolean>(false);
  submitting$ = computed(() => this.submitting());
  cols: IDataGridFields[] = [];

  constructor() {
    this.registrationForm = this.formBuilder.group({
      id: ['', Validators.required],
      isReviewed: [true, Validators.required], // Initialize with default or actual value
      notes: ['', Validators.maxLength(15000)]
    });
  }

  ngOnInit(): void {
    this.selectedColumns.set(this.initializeTableColumns());
    this.loadRegistrations();
  }

  // Data Operations
  loadRegistrations(): void {
    this.commonService.isLoading$.set(true);

    const request: IGetConflictedStudentDirectRegistrationsRequest = {
      includeReviewed: this.showReviewedRegistrations()
    };

    this.handleApiService.getApiData<IGetConflictedStudentDirectRegistrationResponse>(
      {
        url: IStudents.getConflictedDirectStudentRegistrations,
        method: 'GET',
      }, request
    ).subscribe({
      next: (response: IGetConflictedStudentDirectRegistrationResponse) => {
        if (response && response.directStudentRegistrations) {
          this.registrations.set(response.directStudentRegistrations);
          // this.dt.clearFilter(); // Keep commented unless you explicitly want to clear client-side filter on every load
        }
      },
      complete: () => this.commonService.isLoading$.set(false)
    });
  }

  toggleShowReviewed(): void {
    this.loadRegistrations();
  }

  openReviewDialog(registration: IDirectStudentRegistrationDto): void {
    this.selectedRegistration.set(registration);
    this.registrationForm.patchValue({
      id: registration.id,
      isReviewed: registration.isReviewed,
      notes: registration.notes || ''
    });
    this.dialogVisible.set(true);
  }

  closeDialog(): void {
    this.dialogVisible.set(false);
    this.selectedRegistration.set(null);
    this.registrationForm.reset();
  }

  resolveRegistration(): void {
    if (this.registrationForm.invalid) {
      this.toastService.show({
        severity: 'error',
        summary: 'Validation Error',
        detail: 'Please complete all required fields correctly'
      });
      return;
    }

    this.submitting.set(true);

    const request: IResolveConflictedStudentDirectRegistrationRequest = this.registrationForm.value;

    this.handleApiService.getApiData(
      {
        url: IStudents.patchResolveConflictedStudentDirectRegistration,
        method: 'PATCH',
      }, request
    ).subscribe({
      next: () => {
        this.toastService.show({
          severity: 'success',
          summary: 'Success',
          detail: 'Registration has been resolved successfully',
          position: 'top-center'
        });
        this.closeDialog();
        this.loadRegistrations();
      },
      error: (error) => {

        this.submitting.set(false);
      },
      complete: () => this.submitting.set(false)
    });
  }

  /**
   * Refactored CSV export method to match the teacher export logic.
   * Exports all data, not just what's currently on the table or filtered client-side.
   * @param dt The PrimeNG Table instance (not directly used for data source, but for context)
   */
  /**
   * Refactored CSV export method to match the teacher export logic.
   * Exports all data, not just what's currently on the table or filtered client-side.
   * @param dt The PrimeNG Table instance (not directly used for data source, but for context)
   */
  /**
   * Exports registration data to XLS format
   * @param dt The PrimeNG Table instance (not directly used for data source, but for context)
   */
  exportCsv(dt: Table): void {
    const registrationsToExport = this.registrations$(); // Get all registrations from the signal

    if (!registrationsToExport || registrationsToExport.length === 0) {
      this.toastService.show({
        severity: 'info',
        summary: 'No Data',
        detail: 'No registrations to export.'
      });
      return;
    }

    const studentRegFieldNames = nameOf<IDirectStudentRegistrationDto>();
    const cols = this.initializeTableColumns(); // Use the same columns for export

    // Create a new array of objects with string values for all properties, applying formatting
    const formattedData = registrationsToExport.map(registration => {
      const exportObject: Record<string, string> = {};

      for (const col of cols) {
        const fieldName = col.field;
        let value: any = registration[fieldName as keyof IDirectStudentRegistrationDto];

        if (fieldName === studentRegFieldNames.fullName) {
          exportObject[fieldName] = value || '';
        } else if (fieldName === studentRegFieldNames.studentEmailAddress) {
          exportObject[fieldName] = value || '';
        } else if (fieldName === studentRegFieldNames.lastParentEmailAddress) {
          exportObject[fieldName] = value || 'N/A';
        } else if (fieldName === studentRegFieldNames.attemptCount) {
          exportObject[fieldName] = value !== undefined && value !== null ? String(value) : '0';
        } else if (fieldName === studentRegFieldNames.createdAt) {
          exportObject[fieldName] = value ? moment(value).format('YYYY-MM-DD HH:mm:ss') : '';
        } else if (fieldName === studentRegFieldNames.isReviewed) {
          exportObject[fieldName] = value ? 'Reviewed' : 'Pending';
        } else if (fieldName === studentRegFieldNames.notes) { // Include notes if you want it in export
          exportObject[fieldName] = value || '';
        }
        // Add more else if blocks here for any other specific formatting needed
        // For general fields, fall back to string conversion if no specific formatting is needed
        else {
          exportObject[fieldName] = value !== undefined && value !== null ? String(value) : '';
        }
      }
      return exportObject;
    });

    // Generate XLS and trigger download
    const xlsContent = this.convertToXLS(formattedData, cols);
    const formattedDate = moment().format('YYYY-MM-DD_HH-mm-ss');
    this.downloadXLS(xlsContent, `direct_student_registrations_export_${formattedDate}.xls`);
  }

  private convertToXLS(data: Record<string, string>[], cols: IDataGridFields[]): string {
    if (data.length === 0) {
      return '';
    }

    // Get headers from column definitions
    const headers = cols.map(col => col.header);
    const fields = cols.map(col => col.field);

    // Create HTML table for XLS
    let xlsString = '<html xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:x="urn:schemas-microsoft-com:office:excel" xmlns="http://www.w3.org/TR/REC-html40">';
    xlsString += '<head><!--[if gte mso 9]><xml><x:ExcelWorkbook><x:ExcelWorksheets><x:ExcelWorksheet><x:Name>Sheet1</x:Name><x:WorksheetOptions><x:DisplayGridlines/></x:WorksheetOptions></x:ExcelWorksheet></x:ExcelWorksheets></x:ExcelWorkbook></xml><![endif]--></head>';
    xlsString += '<body><table>';

    // Add header row
    xlsString += '<tr>';
    headers.forEach(header => {
      xlsString += `<th>${header}</th>`;
    });
    xlsString += '</tr>';

    // Add data rows
    data.forEach(item => {
      xlsString += '<tr>';
      fields.forEach(field => {
        const value = item[field] || '';
        xlsString += `<td>${value.replace(/</g, '&lt;').replace(/>/g, '&gt;')}</td>`;
      });
      xlsString += '</tr>';
    });

    xlsString += '</table></body></html>';
    return xlsString;
  }

  private downloadXLS(xlsContent: string, filename: string): void {
    const blob = new Blob([xlsContent], {
      type: 'application/vnd.ms-excel;charset=utf-8;'
    });

    // Create download link and trigger it
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = filename.replace('.xls', '.html'); // Change extension to .html
    link.style.display = 'none';
    document.body.appendChild(link);
    link.click();

    // Clean up
    setTimeout(() => {
      document.body.removeChild(link);
      URL.revokeObjectURL(link.href);
    }, 100);
  }

  initializeTableColumns(): IDataGridFields[] {
    const searchStudentRegDtoFieldNames = nameOf<IDirectStudentRegistrationDto>();

    return [
      { field: searchStudentRegDtoFieldNames.fullName, header: 'Student Name', sortable: true },
      { field: searchStudentRegDtoFieldNames.studentEmailAddress, header: 'Student Email', sortable: true },
      { field: searchStudentRegDtoFieldNames.lastParentEmailAddress!, header: 'Parent Email', sortable: true },
      { field: searchStudentRegDtoFieldNames.createdAt, header: 'Created Date', sortable: true },
      { field: searchStudentRegDtoFieldNames.lastUpdatedAt!, header: 'Updated Date', sortable: false },
      { field: searchStudentRegDtoFieldNames.attemptCount, header: 'Attempts', sortable: false },
      { field: searchStudentRegDtoFieldNames.isReviewed, header: 'Status', sortable: true },
      // If you want to include 'notes' in the export, add it here.
      // { field: searchStudentRegDtoFieldNames.notes, header: 'Notes', sortable: false }
    ];
  }
}