import { Component, OnInit, inject, input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { DropdownModule } from 'primeng/dropdown';
import { ButtonModule } from 'primeng/button';
import {
  HandleApiResponseService,
  ITeacherAvailabilityStatusEnum,
  ToastService,
  FormFieldValidationMessageComponent,
  getToastMessage,
  ToastMessages,
  ITeachers,
  AuthStateService,
  IUpdateTeacherAvailabilityStatusRequest,
  IUpdateTeacherAvailabilityStatusResponse,
  IGetTeacherAvailabilityStatusResponse,
  ApiLoadingStateService,
  IAvailability,
  EnumDropdownOptionsService
} from 'SharedModules.Library';
import { Subject, takeUntil } from 'rxjs';

@Component({
  selector: 'app-teacher-profile-availability-status-selection',
  imports: [
    CommonModule,
    ReactiveFormsModule,
    DropdownModule,
    ButtonModule,
    FormFieldValidationMessageComponent
  ],
  templateUrl: './teacher-profile-availability-status-selection.component.html',
  styleUrls: ['./teacher-profile-availability-status-selection.component.scss']
})
export class TeacherProfileAvailabilityStatusSelectionComponent implements OnInit {
  teacherId = input<string>();
  form!: FormGroup;

  private destroy$ = new Subject<void>();
  private fb = inject(FormBuilder);
  private authStateService = inject(AuthStateService);
  private handleApiService = inject(HandleApiResponseService);
  private toastService = inject(ToastService);
  apiLoadingStateService = inject(ApiLoadingStateService);
  enumDropdownOptionsService = inject(EnumDropdownOptionsService);

  availabilityStatusOptions = [
    { label: 'Open', value: ITeacherAvailabilityStatusEnum.Open },
    { label: 'Full', value: ITeacherAvailabilityStatusEnum.Full },
    { label: 'Limited', value: ITeacherAvailabilityStatusEnum.Limited },
  ];
  lastUpdated: Date | undefined = undefined; // Add this property to hold the last updated da
  currentStatus: ITeacherAvailabilityStatusEnum | null = null;

  ngOnInit(): void {
    this.createForm();
    this.loadCurrentStatus();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  createForm(): void {
    this.form = this.fb.group({
      status: [null, Validators.required]
    });
  }

  loadCurrentStatus(): void {
    this.handleApiService.getApiData<IGetTeacherAvailabilityStatusResponse>(
      { url: IAvailability.getTeacherAvailabilityStatus, method: 'GET' },
      { teacherId: this.teacherId() as string }
    ).pipe(takeUntil(this.destroy$)).subscribe({
      next: (response: IGetTeacherAvailabilityStatusResponse) => {
        if (response) {
          this.lastUpdated = response.lastUpdated;
          this.currentStatus = response.availabilityStatus;
          this.form.patchValue({
            status: response.availabilityStatus
          });
        }
      },
      error: (error) => {
      }
    });
  }

  onSubmit(): void {
    if (this.form.invalid) {
      return;
    }

    const request: IUpdateTeacherAvailabilityStatusRequest = {
      teacherId: this.teacherId() as string,
      status: this.form.get('status')?.value
    };

    this.handleApiService.getApiData<IUpdateTeacherAvailabilityStatusResponse>(
      { url: IAvailability.patchTeacherAvailabilityStatus, method: 'PATCH' },
      request, true
    ).pipe(takeUntil(this.destroy$)).subscribe({
      next: (response: IUpdateTeacherAvailabilityStatusResponse) => {
        console.log(response);
        this.toastService.show(
          getToastMessage(ToastMessages.UserSettingsSaved.success, {
            data: ''
          })
        );
        this.loadCurrentStatus();
      },
    });
  }

  getStatusClass(status: ITeacherAvailabilityStatusEnum | null): string {
    switch (status) {
      case ITeacherAvailabilityStatusEnum.Open:
        return 'bg-green-100 text-green-700';
      case ITeacherAvailabilityStatusEnum.Full:
        return 'bg-red-100 text-red-700';
      case ITeacherAvailabilityStatusEnum.Limited:
        return 'bg-orange-100 text-orange-700';
      default:
        return 'bg-gray-100 text-gray-700';
    }
  }

  getStatusLabel(status: ITeacherAvailabilityStatusEnum | null): string {
    return this.enumDropdownOptionsService.availabilityStatusEnumFlagsOptions.find(option => option.value === status)?.label || 'Unknown';
  }

}