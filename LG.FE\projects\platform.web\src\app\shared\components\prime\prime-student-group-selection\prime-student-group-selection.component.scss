:host {
  display: block;
  width: 100%;
}

:host ::ng-deep {
  .p-select-label {
    text-align: left;
    // color: white;
  }

  // Load More Container Styles
  .load-more-container {
    background: var(--surface-ground);
    margin: 0;

    .load-more-btn {
      color: var(--primary-color) !important;
      border: none !important;
      background: transparent !important;
      transition: all 0.2s ease-in-out;

      &:hover {
        background: var(--primary-color-text) !important;
        color: var(--primary-color) !important;
        transform: translateY(-1px);
      }

      &:focus {
        box-shadow: 0 0 0 2px var(--primary-color-text) !important;
      }

      .p-button-icon {
        color: var(--primary-color) !important;
        transition: transform 0.2s ease-in-out;
      }

      &:hover .p-button-icon {
        transform: translateY(2px);
      }
    }
  }

  // Loading State Styles
  .pi-spinner {
    animation: spin 1s linear infinite;
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

  // Modern Group Selector Styles (for specific use cases)
  &.modern-group-selector {
    .load-more-container {
      background: linear-gradient(135deg, var(--surface-50) 0%, var(--surface-100) 100%);
      border-radius: 0 0 8px 8px;

      .load-more-btn {
        font-weight: 500;
        letter-spacing: 0.025em;

        &:hover {
          background: var(--surface-200) !important;
        }
      }
    }
  }

  // Search Indicator Styles
  .search-indicator {
    background: var(--primary-50) !important;
    border: 1px solid var(--primary-100);
    transition: all 0.2s ease-in-out;

    .clear-search-btn {
      color: var(--primary-color) !important;
      background: transparent !important;
      border: none !important;
      width: 1.75rem !important;
      height: 1.75rem !important;

      &:hover {
        background: var(--primary-100) !important;
        color: var(--primary-600) !important;
        transform: scale(1.1);
      }

      &:focus {
        box-shadow: 0 0 0 2px var(--primary-200) !important;
      }
    }
  }

  // Empty Search Results Styles
  .text-center {
    .pi-search {
      opacity: 0.6;
      margin-bottom: 0.5rem;
    }
  }

  // Search Loading States
  .search-loading {
    .pi-spinner {
      color: var(--primary-color) !important;
    }
  }

  // Enhanced Load More Button for Search
  .load-more-btn {
    &.search-mode {
      background: linear-gradient(135deg, var(--primary-50) 0%, var(--primary-100) 100%) !important;
      border: 1px solid var(--primary-200) !important;
      color: var(--primary-700) !important;

      &:hover {
        background: linear-gradient(135deg, var(--primary-100) 0%, var(--primary-200) 100%) !important;
        border-color: var(--primary-300) !important;
      }
    }
  }

  // Responsive Design
  @media (max-width: 768px) {
    .load-more-container {
      padding: 0.75rem;

      .load-more-btn {
        font-size: 0.875rem;
      }

      .search-indicator {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;

        .clear-search-btn {
          align-self: flex-end;
        }
      }
    }
  }

  // Filter Input Enhancements (PrimeNG Select Filter)
  .p-select-filter-container {
    .p-select-filter {
      &::placeholder {
        color: var(--text-color-secondary) !important;
        font-style: italic;
      }

      &:focus {
        border-color: var(--primary-color) !important;
        box-shadow: 0 0 0 2px var(--primary-color-text) !important;
      }
    }
  }

  // Dropdown State Management Enhancements
  .p-select-overlay {
    transition: opacity 0.2s ease-in-out !important;

    &.search-mode {
      .p-select-items {
        transition: all 0.2s ease-in-out;
      }
    }
  }

  // Prevent dropdown flicker during search
  .p-select-items-wrapper {
    min-height: 200px;

    .p-select-items {
      transition: opacity 0.15s ease-in-out;
    }
  }

  // Custom search template enhancements
  .p-select-header {
    position: sticky;
    top: 0;
    z-index: 1000;
    background: var(--surface-0);
    border-bottom: 1px solid var(--surface-border);

    // Search input loading and typing states
    .p-inputgroup {
      &.search-loading {
        .p-inputgroup-addon {
          background: var(--primary-50);
          border-color: var(--primary-color);
        }

        input {
          border-color: var(--primary-color);
          background: var(--primary-50);
          color: var(--primary-color);

          &:disabled {
            opacity: 0.8;
            cursor: wait;
          }
        }
      }

      &.typing {
        .p-inputgroup-addon {
          background: var(--orange-50);
          border-color: var(--orange-300);

          .pi-clock {
            animation: pulse 1.5s ease-in-out infinite;
          }
        }

        input {
          border-color: var(--orange-300);
          background: var(--orange-50);
          color: var(--orange-600);
        }
      }
    }

      input {
        transition: all 0.2s ease-in-out;

        &:focus {
          outline: none;
          border-color: var(--primary-color);
          box-shadow: 0 0 0 2px rgba(var(--primary-color-rgb), 0.2);
        }
      }

      .p-button {
        &:focus {
          outline: none;
          box-shadow: 0 0 0 2px rgba(var(--primary-color-rgb), 0.2);
        }
      }
    }

    // Search state indicators
    .search-indicator {
      .clear-search-btn {
        &:hover {
          background: rgba(var(--primary-color-rgb), 0.1) !important;
        }
      }
    }
  }

  // Dropdown loading states
  .dropdown-item-wrapper {
    position: relative;
    transition: opacity 0.2s ease-in-out;

    &.loading-overlay {
      opacity: 0.6;
      pointer-events: none;
    }
  }

  .dropdown-loading-overlay {
    position: sticky;
    top: 0;
    background: rgba(var(--surface-0-rgb), 0.95);
    backdrop-filter: blur(2px);
    border-bottom: 1px solid var(--surface-border);
    z-index: 999;
  }

  // Enhanced loading animations
  .pi-spinner {
    animation: spin 1s linear infinite;
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

  @keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
  }

  // Loading state transitions
  .p-select-overlay {
    &.loading {
      .p-select-items {
        opacity: 0.8;
        transition: opacity 0.2s ease-in-out;
      }
    }
  }

  // Selected item compact design styles
  .compact-avatar {
    width: 24px !important;
    height: 24px !important;
    font-size: 10px !important;
    font-weight: 600 !important;
    margin-left: -4px !important;
    border: 2px solid var(--surface-0) !important;

    &:first-child {
      margin-left: 0 !important;
    }
  }

  // Selected item container
  .p-select-label {
    .flex {
      min-height: 32px;

      .text-sm {
        line-height: 1.2;
      }

      .text-xs {
        line-height: 1.1;
      }
    }
  }

  // Ensure proper spacing in selected item
  .p-select-trigger {
    padding-left: 8px !important;
  }

  // Avatar group spacing
  p-avatar-group {
    .p-avatar-group {
      gap: 0 !important;
    }
  }

  // Loading state overlay for dropdown
  .dropdown-loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;

    .loading-content {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      padding: 1rem;
      background: white;
      border-radius: 4px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }
  }