import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, computed, inject, Inject, Input, type OnInit, signal } from '@angular/core';
import { AuthStateService, CustomDialogPopupComponent, GeneralService, IBasicProfileInfoDto, ICalendarEventDto, ILanguageLevelsEnum, IPackageTypeEnum, LESSON_STATUS_TITLES, TimezoneService } from 'SharedModules.Library';
import { ButtonModule } from 'primeng/button';
import { ChipModule } from 'primeng/chip';
import { ICalendarLessonDto, ILessonStatusEnum } from 'SharedModules.Library';

@Component({
  selector: 'app-lesson-info-actions-dialog',
  imports: [
    CommonModule,
    ButtonModule,
    CustomDialogPopupComponent,
    ChipModule,
  ],
  templateUrl: './lesson-info-actions-dialog.component.html',
  styleUrl: './lesson-info-actions-dialog.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class LessonInfoActionsDialogComponent implements OnInit {

  generalService = inject(GeneralService);
  timezoneService = inject(TimezoneService);
  authService = inject(AuthStateService);
  dialogVisible = signal(true);
  dialogStyleClass = signal('p-dialog p-dialog-md purple-dialog-header');
  dialogStyle = signal({ width: '60vw' });
  dialogHeader = computed(() => `Lesson Details`);
  dialogStyle$ = computed(() => this.dialogStyle());
  mockLesson: ICalendarEventDto = {
    title: "French Language Lesson - B2 Level",
    start: "2024-01-25T14:00:00.000Z",
    end: "2024-01-25T15:30:00.000Z",
    startDateTime: new Date("2024-01-25T14:00:00.000Z"),
    endDateTime: new Date("2024-01-25T15:30:00.000Z"),
    lesson: {
      lessonId: "lesson-123",
      groupId: null, // null for 1-1 lesson
      lessonStatus: ILessonStatusEnum.Scheduled,
      teachingLanguageName: "French",
      languageLevel: ILanguageLevelsEnum.B2,
      lessonType: IPackageTypeEnum.Paid,
      parent: {
        userId: "teacher-456",
        firstName: "Marie",
        lastName: "Dubois",
        timeZoneIana: 'America/New_York',
        timeZoneDisplayName: 'Eastern Time',
        profilePhotoUrl: ''
    },
      teacher: {
        userId: "teacher-456",
        firstName: "Marie",
        lastName: "Dubois",
        profilePhotoUrl: "https://example.com/profile/marie.jpg",
        timeZoneDisplayName: "Pacific Standard Time",
        timeZoneIana: "America/Los_Angeles"
      },
      students: [
        {
          userId: "student-789",
          firstName: "John",
          lastName: "Smith",
          profilePhotoUrl: "https://example.com/profile/john.jpg",
          timeZoneDisplayName: "Pacific Standard Time",
          timeZoneIana: "America/Los_Angeles"
        }
      ]
    }
  };
  statusClass = computed(() => {
    const statusMap: Record<ILessonStatusEnum, string> = {
      [ILessonStatusEnum.Scheduled]: 'bg-blue-100 text-blue-700',
      [ILessonStatusEnum.PendingConfirmationByTeacher]: 'bg-orange-100 text-orange-700',
      [ILessonStatusEnum.Completed]: 'bg-teal-100 text-teal-700',
      [ILessonStatusEnum.StudentNoShow]: 'bg-red-100 text-red-700',
      [ILessonStatusEnum.TeacherNoShow]: 'bg-red-100 text-red-700',
      [ILessonStatusEnum.CancelledByParent]: 'bg-gray-100 text-gray-700',
      [ILessonStatusEnum.CancelledByTeacher]: 'bg-gray-100 text-gray-700',
      [ILessonStatusEnum.AwaitingEvaluation]: 'bg-purple-100 text-purple-700',
      [ILessonStatusEnum.CancelledByAdmin]: 'bg-purple-100 text-purple-700'
    };
    return statusMap[this.mockLesson.lesson!.lessonStatus] || 'bg-gray-100 text-gray-700';
  });

  statusText = computed(() => {
    const statusMap: Record<ILessonStatusEnum, string> = LESSON_STATUS_TITLES;
    return statusMap[this.mockLesson.lesson!.lessonStatus] || 'Unknown';
  });

  ILanguageLevelsEnum = ILanguageLevelsEnum;
  IPackageTypeEnum = IPackageTypeEnum;
  ILessonStatusEnum = ILessonStatusEnum;
  userBasicInfo = (this.authService.getUserBasicInfo() as IBasicProfileInfoDto);

  constructor(@Inject('dialogParameters') parameters: ICalendarEventDto) {
    this.timezoneService.setTimezone(this.userBasicInfo.timeZoneIana!);
    this.mockLesson! = parameters as ICalendarEventDto;
    console.log(this.mockLesson);
  }

  ngOnInit(): void {
    this.dialogStyleClass.set('p-dialog p-dialog-sm purple-dialog-header');
    this.dialogStyle.set({ width: '510px' });
  }

  closeDialog() {
    this.dialogVisible.set(false);
  }

  canJoinLesson(): boolean {
    return this.mockLesson.lesson!.lessonStatus === ILessonStatusEnum.Scheduled ||
      this.mockLesson.lesson!.lessonStatus === ILessonStatusEnum.StudentNoShow;
  }

  canCancelLesson(): boolean {
    return this.mockLesson.lesson!.lessonStatus === ILessonStatusEnum.Scheduled ||
      this.mockLesson.lesson!.lessonStatus === ILessonStatusEnum.PendingConfirmationByTeacher;
  }
}