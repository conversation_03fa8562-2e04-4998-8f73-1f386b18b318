<form (ngSubmit)="submitForm()" [formGroup]="form">


  <label [class]="labelClass" htmlfor="name3"
    [innerHTML]="generalService.getDisplayLabel('Student\'s Country', true )"></label>

  <div class="w-full">

    <app-prime-countries-dropdown [parentForm]="form" (countrySelected)="onCountryChange($event)"
      formControlName="country" data-name="country" styleClass=" w-full text-left text-left mb-0">
    </app-prime-countries-dropdown>


  </div>
  <div class="w-full ">
    <app-form-field-validation-message [control]="form.controls['countryOfResidence']" messageClass="text-red-500"
      propertyName="Country" styleClass="w-full mb-3"></app-form-field-validation-message>
  </div>

  <div class="">
    <label [class]="labelClass" htmlfor="name3"
      [innerHTML]="generalService.getDisplayLabel('Student\'s Timezone', true )"></label>

    <div class="w-full">
      <app-prime-timezone-dropdown [parentForm]="form" [useGeoLocation]="false"
        (timezoneSelected)="onTimezoneChange($event)" formControlName="timeZoneIana" styleClass=" full-width w-full">
      </app-prime-timezone-dropdown>

    </div>
    <div class="w-full ">
      <app-form-field-validation-message [control]="form.controls['timeZoneIana']" messageClass="text-red-500"
        propertyName="Timezone" styleClass="w-full"></app-form-field-validation-message>
    </div>
  </div>


  <div formGroupName="addressDto">
    <div class="">
      <label [class]="labelClass" htmlfor="name3" [innerHTML]="generalService.getDisplayLabel('City', true )"></label>
      <div class="w-full">
        <app-prime-reactive-form-input [inputClass]="'w-full '" [parentForm]="form" [required]="true"
          formControlName="city" [controlPath]="'addressDto.city'" placeholder="Enter your city" type="text">
        </app-prime-reactive-form-input>
      </div>
      <div class="w-full ">
        <app-form-field-validation-message [control]="form.get('addressDto.city')!" messageClass="text-red-500"
          propertyName="City" styleClass="w-full"></app-form-field-validation-message>
      </div>
    </div>

    @if (shouldSetAState()) {
    <div class="">
      <!-- <label [class]="labelClass" htmlfor="name3" [innerHTML]="generalService.getDisplayLabel('State/County', true )"></label> -->
      <div class="w-full" [ngClass]="{
      'hidden': !shouldSetAState()
    }">
        <app-prime-reactive-form-input [inputClass]="'w-full '" [parentForm]="form" [label]="'State/County'"
          formControlName="state" [controlPath]="'addressDto.state'" placeholder="Enter your State/County" type="text"
          [required]="shouldSetAState()">
        </app-prime-reactive-form-input>
      </div>
      <div class="w-full">
        <app-form-field-validation-message [control]="form.get('addressDto.state')!" messageClass="text-red-500"
          propertyName="state" styleClass="w-full"></app-form-field-validation-message>
      </div>
    </div>
    }
  </div>
  <!-- ends addressDto -->

  <div class=" mb-3">

    <label [class]="labelClass" htmlfor="name3">More Details</label>
    <div class="w-full">
      <textarea [autoResize]="true" class="w-full  h-7rem" cols="35" formControlName="moreDetails" pTextarea
        placeholder="Tell us more about the student, his/her current level, schedule and goals he/she wants to achieve by taking this course."
        rows="7"></textarea>
    </div>
  </div>

  <app-form-field-validation-message [severity]="Severity.Danger" [text]="generalService.errorDataSignal"
    messageClass="mb-3" styleClass="w-full"></app-form-field-validation-message>

  <div class="text-center mt-4">

    <p-button role="button" type="submit" class="gradient-purple-btn" styleClass="w-full" label="Create Student"
      [disabled]="form.invalid || form.pristine || apiLoadingStateService.getIsLoading()"
      [loading]="apiLoadingStateService.getIsLoading()"
      icon="pi pi-check" iconPos="right">
    </p-button>

  </div>

</form>