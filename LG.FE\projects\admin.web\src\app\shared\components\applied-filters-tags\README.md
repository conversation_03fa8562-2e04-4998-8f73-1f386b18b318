# Applied Filters Tags Component

A fully reusable Angular component for displaying and managing applied filters across different data grid pages in the admin.web project.

## Features

- **Reusable**: Can be used across different data grid implementations
- **Reactive**: Built with Angular signals for optimal reactivity and performance
- **Accessible**: WCAG 2.1 AA compliant with proper ARIA labels and keyboard navigation
- **Responsive**: Mobile-first design with PrimeFlex integration
- **Customizable**: Support for custom templates and styling
- **Type-safe**: Full TypeScript interfaces and strong typing
- **Enterprise-grade**: Professional aesthetics with established color palette
- **Signal-based**: Internal state management with Angular signals for better reactivity

## Basic Usage

```html
<app-applied-filters-tags
  [filters]="appliedFilters()"
  (filterRemoved)="onFilterRemove($event)"
  (clearAllClicked)="onClearAll($event)">
</app-applied-filters-tags>
```

## Inputs

| Property | Type | Default | Description |
|----------|------|---------|-------------|
| `filters` | `IAppliedFilterTag[]` | `[]` | Array of filter tags to display |
| `config` | `IAppliedFiltersConfig` | See below | Configuration options |
| `showWhenEmpty` | `boolean` | `false` | Whether to show component when no filters |

### Default Configuration

```typescript
{
  showClearAll: true,
  clearAllLabel: 'Clear All',
  clearAllIcon: 'pi pi-filter-slash',
  headerText: 'Applied Filters :',
  headerIcon: 'pi pi-filter',
  maxVisibleTags: undefined,
  responsive: true
}
```

## Outputs

| Event | Type | Description |
|-------|------|-------------|
| `filterRemoved` | `IFilterTagRemoveEvent` | Emitted when a filter tag is removed |
| `clearAllClicked` | `MouseEvent` | Emitted when clear all button is clicked |

## Filter Tag Interface

```typescript
interface IAppliedFilterTag {
  id: string;                    // Unique identifier
  label: string;                 // Display text
  icon?: string;                 // PrimeIcons class
  type: FilterTagType;           // For styling categorization
  removeData?: any;              // Data passed back on removal
  removable?: boolean;           // Whether tag can be removed
  customClasses?: string;        // Additional CSS classes
}
```

## Filter Types

- `search` - Search/text filters
- `sort` - Sorting filters
- `date` - Date range filters
- `select` - Single selection filters
- `multiselect` - Multiple selection filters
- `range` - Numeric range filters
- `boolean` - Boolean/toggle filters
- `custom` - Custom filter types

## Reactive API Methods

The component provides reactive methods for programmatic interaction:

```typescript
// Add a filter programmatically
component.addFilter({
  id: 'new-filter',
  label: 'New Filter',
  type: 'search'
});

// Remove a filter by ID
component.removeFilterById('filter-id');

// Clear all filters
component.clearAllFilters();

// Update configuration
component.updateConfig({
  headerText: 'Custom Filters:',
  showClearAll: false
});

// Get reactive signals (read-only)
const filtersSignal = component.getFiltersSignal();
const configSignal = component.getConfigSignal();
```

## Custom Templates

You can provide custom templates for filter tags:

```html
<app-applied-filters-tags [filters]="filters()">
  <ng-template #filterTagTemplate let-filter let-remove="remove">
    <div class="custom-filter-tag">
      <span>{{ filter.label }}</span>
      <button (click)="remove()">×</button>
    </div>
  </ng-template>
</app-applied-filters-tags>
```

## Integration with Existing Components

### Using the Adapter Service

The `AppliedFiltersAdapterService` helps convert existing filter objects to the required format:

```typescript
// In your component
appliedFilters = computed(() => {
  return this.appliedFiltersAdapter.convertTeachersRequestToFilterTags(
    this.queryParams(),
    this.currentUrlParams(),
    {
      getSortColumnDisplayName: () => this.getSortColumnDisplayName(),
      getFilterLabelFromUrl: (filterType, paramName) => 
        this.getFilterLabelFromUrl(filterType, paramName),
      getDefaultSortColumn: () => 'defaultColumn',
      getDefaultSortDirection: () => 'asc'
    }
  );
});
```

### Event Handling

```typescript
onAppliedFilterRemove(event: IFilterTagRemoveEvent): void {
  const filterName = event.filter.removeData?.filterName;
  if (filterName) {
    this.removeFilter(filterName, event.event);
  }
}

onAppliedFiltersClearAll(event: MouseEvent): void {
  this.resetFilters();
}
```

## Reactive Features

The component leverages Angular signals for optimal reactivity:

### Internal Signal Management
- **Filters Signal**: Reactive state for filter array with automatic change detection
- **Config Signal**: Reactive configuration with partial updates support
- **Computed Properties**: Automatically recalculated based on signal changes
  - `isVisible()` - Component visibility based on filters and showWhenEmpty
  - `visibleFilters()` - Filtered list considering maxVisibleTags
  - `hiddenFiltersCount()` - Count of hidden filters
  - `showClearAll()` - Clear button visibility logic

### Effects and Side Effects
- **Filter Change Effect**: Logs filter updates and visibility changes
- **Configuration Effect**: Reacts to configuration updates
- **Automatic Recomputation**: All computed values update automatically when signals change

### Performance Benefits
- **Minimal Re-renders**: Only affected parts of the template update
- **Efficient Change Detection**: OnPush strategy with signal-based reactivity
- **Memory Efficient**: Automatic cleanup with DestroyRef

## Styling

The component uses enterprise-grade styling with:

- Established color palette (#6366f1 primary, #10b981 success, etc.)
- 16px border radius
- Subtle animations (max 1.02x scale)
- Responsive design with mixins
- WCAG 2.1 AA compliance

## Accessibility Features

- Proper ARIA labels and roles
- Keyboard navigation support
- High contrast mode support
- Reduced motion support
- Minimum touch target sizes (44px)
- Screen reader friendly

## Browser Support

- Modern browsers with ES2020+ support
- Angular 17+ required
- PrimeNG 17+ required

## Examples

See the teachers-list component for a complete implementation example.
