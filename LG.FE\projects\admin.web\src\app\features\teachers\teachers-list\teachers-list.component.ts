// ============================================================================
// TEACHERS LIST COMPONENT - Displays and manages teacher data in a data grid
// ============================================================================

import { CommonModule } from '@angular/common';
import {
  Component,
  OnInit,
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  inject,
  OnDestroy,
  signal,
  computed,
  ViewChild
} from '@angular/core';
import { ActivatedRoute, Params, Router } from '@angular/router';
import { takeUntil, combineLatest } from 'rxjs';
import { toObservable } from '@angular/core/rxjs-interop';
import moment from "moment-timezone";

// === PRIMENG IMPORTS ===
import { TableModule } from 'primeng/table';
import { MultiSelectModule } from 'primeng/multiselect';
import { ButtonModule } from "primeng/button";
import { InputTextModule } from 'primeng/inputtext';
import { TooltipModule } from 'primeng/tooltip';
import { FormsModule } from '@angular/forms';

// === SHARED LIBRARY IMPORTS ===
import {
  IGetTeachersRequest,
  IGetTeachersResponse,
  ITeachingLanguageDto,
  nameOf,
  IGetAllTeachingLanguagesResponse,
  TeachingLanguagesRoutes,
  ITeachers,
  ISearchTeacherDto,
  EnumDropdownOptionsService,
  IEnumDropdownOptions,
  GeneralService,
  IDataGridFields,
  IGetLanguagesResponse,
  LocationDataRoutes,
  IBasedDataGridRequest
} from 'SharedModules.Library';

// === COMPONENT IMPORTS ===
import { AppliedFiltersTagsComponent } from '../../../shared/components/applied-filters-tags/applied-filters-tags.component';
import { DataGridHeaderFooterComponent } from '../../../shared/components/data-grid-header-footer/data-grid-header-footer.component';
import { FiltersDrawerSidebarComponent, IFiltersDrawerConfig } from '../../../shared/components/filters-drawer-sidebar/filters-drawer-sidebar.component';
import { ITeachersFilterActionEvent, ITeachersFilterChangeEvent, TeachersListFiltersComponent } from './teachers-list-filters/teachers-list-filters.component';

// === SERVICE IMPORTS ===
import { AppliedFiltersAdapterService } from '../../../shared/services/teachers-list/applied-filters-adapter.service';
import { TeacherListComponentHelperService } from '../../../shared/services/teacher-list-component-helper.service';

// === BASE COMPONENT ===
import { BaseDataGridComponent, IBaseDataGridConfig } from "../../../shared/components/BaseDataGrid/BaseDataGridComponent";

// ============================================================================
// COMPONENT DEFINITION
// ============================================================================

/**
 * Teachers List Component
 *
 * Displays teachers in a data grid with:
 * - Pagination and sorting
 * - Advanced filtering (ages, availability, languages, etc.)
 * - Applied filters display
 * - Search functionality
 * - Export capabilities
 *
 * Extends BaseDataGridComponent for common data grid functionality
 */
@Component({
  selector: 'app-teachers-list',
  standalone: true,
  imports: [
    CommonModule,
    TableModule,
    MultiSelectModule,
    ButtonModule,
    FormsModule,
    InputTextModule,
    TooltipModule,
    DataGridHeaderFooterComponent,
    AppliedFiltersTagsComponent,
    TeachersListFiltersComponent,
    FiltersDrawerSidebarComponent
  ],
  templateUrl: './teachers-list.component.html',
  styleUrls: ['./teachers-list.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class TeachersListComponent extends BaseDataGridComponent<
  IGetTeachersRequest,
  IGetTeachersResponse
> implements OnInit, OnDestroy {

  private teacherHelperService = inject(TeacherListComponentHelperService);
  enumDropdownOptionsService = inject(EnumDropdownOptionsService);
  private appliedFiltersAdapter = inject(AppliedFiltersAdapterService);
  protected override generalService = inject(GeneralService);
  protected override router = inject(Router);

  teachingLanguages = signal<ITeachingLanguageDto[]>([]);
  nativeLanguages = signal<string[]>([]);
  studentAgesRange = computed(() => {
    const params = this.queryParams();
    return [params.studentAgesMin || 2, params.studentAgesMax || 17];
  });

  selectedAvailabilityStatuses = computed(() =>
    this.generalService.convertFlagsToArray(
      this.queryParams().availabilityStatus || 0,
      this.enumDropdownOptionsService.availabilityStatusEnumFlagsOptions
    )
  );

  selectedTeachingAgesExperience = computed(() =>
    this.generalService.convertFlagsToArray(
      this.queryParams().teachingAgesExperience || 0,
      this.enumDropdownOptionsService.teachingAgesExperienceEnumFlagsOptions
    )
  );

  selectedTeacherStudentAgesPreference = computed(() =>
    this.generalService.convertFlagsToArray(
      this.queryParams().teacherStudentAgesPreference || 0,
      this.enumDropdownOptionsService.teacherStudentAgesPreferenceEnumFlagsOptions
    )
  );

  // Computed property for visible columns (excludes hidden columns)
  visibleColumns = computed(() => this.cols.filter(col => !col.hide));

  getConfig(): IBaseDataGridConfig<IGetTeachersRequest> {
    return {
      defaultRequest: this.teacherHelperService.createDefaultTeachersRequest(),
      apiEndpoint: ITeachers.getTeachers,
      errorPrefix: 'Failed to load teachers',
      mapUrlParamsToRequest: (params: Params) => this.teacherHelperService.mapQueryParamsToTeachersRequest(params),
      createDefaultRequest: () => this.teacherHelperService.createDefaultTeachersRequest(),
      appliedFiltersConfig: {
        convertToFilterTags: (request: IGetTeachersRequest, urlParams: Record<string, string>) =>
          this.appliedFiltersAdapter.convertTeachersRequestToFilterTags(request, {
            getSortColumnDisplayName: () => this.getSortColumnDisplayNameFromUrl(),
            getFilterLabelFromUrl: (filterType: string, paramName: string) => this.getFilterLabelFromUrl(filterType as keyof IGetTeachersRequest, paramName),
            getDefaultSortColumn: () => TeacherListComponentHelperService.DEFAULT_SORT_COLUMN,
            getDefaultSortDirection: () => TeacherListComponentHelperService.DEFAULT_SORT_DIRECTION
          })
      },
      filterDrawerConfig: { enabled: true, position: 'right', width: '450px', headerText: 'Teachers List Filters', headerIcon: 'pi pi-filter' },
      fieldNames: nameOf<IGetTeachersRequest>()
    };
  }

  filterState = computed(() => ({
    queryParams: this.queryParams(),
    studentAgesRange: this.studentAgesRange(),
    selectedAvailabilityStatuses: this.selectedAvailabilityStatuses(),
    selectedTeachingAgesExperience: this.selectedTeachingAgesExperience(),
    selectedTeacherStudentAgesPreference: this.selectedTeacherStudentAgesPreference(),
    teachingLanguages: this.teachingLanguages(),
    nativeLanguages: this.nativeLanguages(),
    isFilterOpen: true
  }));

  filtersDrawerConfig: IFiltersDrawerConfig = {
    headerText: 'Teachers List Filters', headerIcon: 'pi pi-filter', position: 'right', width: '450px',
    showApplyButton: true, showResetButton: true, showCloseButton: true,
    applyButtonLabel: 'Apply Filters', resetButtonLabel: 'Reset All', closeButtonLabel: 'Close',
    applyButtonIcon: 'pi pi-check', resetButtonIcon: 'pi pi-refresh', closeButtonIcon: 'pi pi-times'
  };

  @ViewChild('teachersFilters') teachersFiltersComponent!: TeachersListFiltersComponent;
  searchTeacherDtoFieldNames = nameOf<ISearchTeacherDto>();
  private getTeachersRequestFieldNames = nameOf<IGetTeachersRequest>();

  constructor(route: ActivatedRoute, cdr: ChangeDetectorRef) {
    super(route, cdr);
    combineLatest([toObservable(this.teachingLanguages), toObservable(this.nativeLanguages)])
      .pipe(takeUntil(this.destroy$))
      .subscribe(([teachingLanguages, nativeLanguages]) => {
        if ((teachingLanguages.length > 0 || nativeLanguages.length > 0) && Object.keys(this.currentUrlParams()).length > 0) {
          this.updateAppliedFilters();
        }
      });
  }

  override ngOnInit(): void {
    this.cols = this.teacherHelperService.initializeTableColumns();

    // Filter out hidden columns for initial selection
    const visibleColumns = this.cols.filter(col => !col.hide);
    this.selectedColumns.set(visibleColumns);

    this.getTeachingLanguages();
    this.getNativeLanguages();
    super.ngOnInit();
  }

  override ngOnDestroy(): void { super.ngOnDestroy(); }

  hasFilterInUrl(paramName: string): boolean {
    const value = this.currentUrlParams()[paramName];
    return !!value && value !== '0' && value !== 'false';
  }

  hasRangeFilterInUrl(minParam: string, maxParam: string, defaultMin = 2, defaultMax = 17): boolean {
    const params = this.currentUrlParams();
    return (!!params[minParam] && +params[minParam] !== defaultMin) || (!!params[maxParam] && +params[maxParam] !== defaultMax);
  }

  hasAnyFiltersInUrl(): boolean {
    const params = this.currentUrlParams();
    const paginationParams: string[] = [this.getTeachersRequestFieldNames.pageNumber!, this.getTeachersRequestFieldNames.pageSize!, this.getTeachersRequestFieldNames.sortColumn!, this.getTeachersRequestFieldNames.sortDirection!];
    return Object.keys(params).some(key => !paginationParams.includes(key) && params[key] !== '0' && params[key] !== 'false');
  }

  hasSortFilterInUrl(): boolean {
    const sortColumn = this.currentUrlParams()[this.getTeachersRequestFieldNames.sortColumn!];
    return !!(sortColumn && sortColumn !== TeacherListComponentHelperService.DEFAULT_SORT_COLUMN);
  }

  getSortColumnDisplayName(): string {
    const sortColumn = this.queryParams().sortColumn;
    const column = this.cols.find(col => col.field === sortColumn);
    return column ? column.header : sortColumn || 'Unknown';
  }

  getSortColumnDisplayNameFromUrl(): string {
    const sortColumn = this.currentUrlParams()[this.getTeachersRequestFieldNames.sortColumn!];
    const column = this.cols.find(col => col.field === sortColumn);
    return column ? column.header : sortColumn || 'Unknown';
  }

  /**
   * Gets human-readable labels for different types of filter values from URL parameters
   * Uses typed field names to avoid magic strings
   */
  getFilterLabelFromUrl(filterType: keyof IGetTeachersRequest, paramName: string): string {
    const value = this.currentUrlParams()[paramName];
    if (value === undefined || value === null || value === '0' || value === 'false') return '';

    switch (filterType) {
      case this.getTeachersRequestFieldNames.gender!:
        const genderOption = this.enumDropdownOptionsService.genderOptions.find(
          g => g.value === +value
        );
        return genderOption ? genderOption.label : '';

      case this.getTeachersRequestFieldNames.teachingLanguage!:
        if (!value) return '';
        const language = this.teachingLanguages().find(l => l.id === value);
        // Return language name if found, otherwise return the ID with a loading indicator
        return language ? language.name : `Language ID: ${value}`;

      case this.getTeachersRequestFieldNames.speakingLanguage!:
        if (!value) return '';
        // For speaking languages, check if we have the language in our native languages list
        const nativeLanguages = this.nativeLanguages();
        if (nativeLanguages.length > 0) {
          // If we have native languages loaded, return the value as is (it should be the name)
          return value;
        } else {
          // If languages aren't loaded yet, show a loading indicator
          return `${value}`;
        }

      case this.getTeachersRequestFieldNames.availabilityStatus!:
        return this.getBitFlagLabels(
          this.enumDropdownOptionsService.availabilityStatusEnumFlagsOptions,
          +value
        );

      case this.getTeachersRequestFieldNames.teachingAgesExperience!:
        return this.getBitFlagLabels(
          this.enumDropdownOptionsService.teachingAgesExperienceEnumFlagsOptions,
          +value
        );

      case this.getTeachersRequestFieldNames.teacherStudentAgesPreference!:
        return this.getBitFlagLabels(
          this.enumDropdownOptionsService.teacherStudentAgesPreferenceEnumFlagsOptions,
          +value
        );

      default:
        return '';
    }
  }

  /**
   * Helper method to get labels from bit flag enum values
   */
  private getBitFlagLabels(optionsArray: IEnumDropdownOptions[], flagValue: number): string {
    if (!flagValue) return '';

    const matchingOptions = optionsArray
      .filter(option => (option.value & flagValue) === option.value && option.value !== 0)
      .map(option => option.label);

    return matchingOptions.join(', ');
  }

  override resetFilters(): void {
    this.saveScrollPosition();

    // Reset to default values
    this.queryParams.set(this.teacherHelperService.createDefaultTeachersRequest());

    // No need to manually reset UI state - computed signals will automatically update when queryParams change

    this.generalService.updateQueryParams(this.queryParams(), {
      preserveFragment: true,
      queryParamsHandling: '',
      replaceUrl: true
    });

    // BaseDataGrid will automatically fetch data when URL changes
    this.restoreScrollPosition();
  }

  // Override the base class method to handle date formatting for API calls
  protected override cleanRequestForApi<T extends IBasedDataGridRequest>(request: T): Partial<T> {
    const cleanedRequest: Partial<T> = {};

    // Only include non-null and non-undefined values, with special handling for dates
    for (const [key, value] of Object.entries(request)) {
      if (value !== null && value !== undefined) {
        // Format Date objects to ISO strings for approvedDateFrom and approvedDateTo
        if ((key === 'approvedDateFrom' || key === 'approvedDateTo') && value instanceof Date) {
          const isoString = value.toISOString();
          console.log(`Formatting ${key} from Date object to ISO string:`, isoString);
          cleanedRequest[key as keyof T] = isoString as any;
        } else {
          cleanedRequest[key as keyof T] = value;
        }
      }
    }

    return cleanedRequest;
  }

  // ============================================================================
  // DATA LOADING - API calls for reference data
  // ============================================================================

  private getTeachingLanguages(): void {
    this.handleApiService.getApiData<IGetAllTeachingLanguagesResponse>({ url: TeachingLanguagesRoutes.getAllTeachingLanguages, method: 'GET' })
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response) => response?.teachingLanguages ? this.teachingLanguages.set(response.teachingLanguages) : console.error('Failed to load teaching languages: Invalid response'),
        error: (error) => console.error('Failed to load teaching languages:', error)
      });
  }

  private getNativeLanguages(): void {
    this.handleApiService.getApiData<IGetLanguagesResponse>({ url: LocationDataRoutes.getLanguages, method: 'GET' })
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response) => response?.languages ? this.nativeLanguages.set(response.languages) : console.error('Failed to load native languages: Invalid response'),
        error: (error) => console.error('Failed to load native languages:', error)
      });
  }

  /**
   * Handles filter removal from the new applied filters component
   */
  // onAppliedFilterRemove and onAppliedFiltersClearAll are now handled by BaseDataGrid

  override onFilterChange(event: ITeachersFilterChangeEvent): void {
    this.queryParams.update(current => ({ ...current, [event.filterName]: event.value, ...(event.resetPage ? { pageNumber: 1 } : {}) }));
  }

  override onFilterAction(event: ITeachersFilterActionEvent): void {
    if (event.action === 'search') {
      this.saveScrollPosition();
      this.queryParams.set(event.filters);
      this.generalService.updateQueryParams(this.queryParams(), { replaceUrl: true });
      this.restoreScrollPosition();
    } else if (event.action === 'reset') {
      this.resetFilters();
    }
  }

  /**
   * Removes a specific filter and updates the data
   * Uses typed field names to avoid magic strings
   */
  override removeFilter(filterName: keyof IGetTeachersRequest, event?: MouseEvent): void {
    if (event) {
      event.stopPropagation();
    }

    this.saveScrollPosition();

    // Create a new params object with the updated values
    this.queryParams.update(params => {
      const updatedParams = { ...params };

      // Use the typed field names for comparison to avoid magic strings
      const fieldNames = this.getTeachersRequestFieldNames;

      switch (filterName) {
        case fieldNames.searchTerm!:
          updatedParams.searchTerm = null;
          break;
        case fieldNames.gender:
          updatedParams.gender = 0;
          break;
        case fieldNames.teachingLanguage!:
          updatedParams.teachingLanguage = null;
          break;
        case fieldNames.speakingLanguage!:
          updatedParams.speakingLanguage = null;
          break;
        case fieldNames.includeBlocked!:
          updatedParams.includeBlocked = false;
          break;
        case fieldNames.availabilityStatus!:
          updatedParams.availabilityStatus = null;
          break;
        case fieldNames.teachingAgesExperience!:
          updatedParams.teachingAgesExperience = null;
          break;
        case fieldNames.teacherStudentAgesPreference!:
          updatedParams.teacherStudentAgesPreference = null;
          break;
        // Handle composite filters using actual field names from interfaces
        case fieldNames.approvedDateFrom:
          updatedParams.approvedDateFrom = null;
          updatedParams.approvedDateTo = null;
          break;
        case fieldNames.studentAgesMin:
          updatedParams.studentAgesMin = 2;
          updatedParams.studentAgesMax = 17;
          break;
        case fieldNames.sortColumn:
          updatedParams.sortColumn = TeacherListComponentHelperService.DEFAULT_SORT_COLUMN;
          updatedParams.sortDirection = TeacherListComponentHelperService.DEFAULT_SORT_DIRECTION;
          break;
      }

      // Reset to the first page
      updatedParams.pageNumber = 1;

      return updatedParams;
    });

    // Update URL - BaseDataGrid will automatically fetch data when URL changes
    this.generalService.updateQueryParams(this.queryParams(), { replaceUrl: true });
    this.restoreScrollPosition();
  }

  onColumnReorder(event: any): void {
    if (event.columns?.length) {
      const reorderedColumns = event.columns.filter((col: any) => col?.field && col?.header) as IDataGridFields[];
      this.selectedColumns.set([...reorderedColumns]);
    }
  }

  exportTable(): void {
    const response = this.dataResponse();
    if (response) this.teacherHelperService.exportTeacherTable(this.table, this.selectedColumns(), response);
  }

  formatUtcDateToAdminLocalized(date: string | Date): string {
    return date ? moment.utc(date).tz('Europe/Athens').format('DD/MM/YYYY HH:mm') : '';
  }

  goToTeacherOverview(id: string) {
    this.generalService.setPreviousUrl(this.router.url);
    this.router.navigate(['/dashboard/teachers/' + id + '/overview']);
  }

  /**
   * Handles filters drawer action events - with temporary filter support
   */
  override onFiltersDrawerAction(event: any): void {
    switch (event.action) {
      case 'apply':
        // Apply temporary filters from the filters component
        if (this.teachersFiltersComponent) {
          const filters = this.teachersFiltersComponent.getCurrentFilters();
          this.onFilterAction({ action: 'search', filters });
        }
        this.closeFiltersDrawer();
        break;

      case 'reset':
        // Reset filters and apply immediately
        if (this.teachersFiltersComponent) {
          this.teachersFiltersComponent.emitResetAction();
        }
        this.closeFiltersDrawer();
        break;

      case 'close':
        // Check if there are pending changes and handle accordingly
        if (this.teachersFiltersComponent?.hasPendingChanges()) {
          // Discard temporary changes when closing without applying
          this.teachersFiltersComponent.discardTempChanges();
        }
        this.closeFiltersDrawer();
        break;
    }
  }
}
