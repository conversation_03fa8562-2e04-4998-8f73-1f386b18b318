<!-- Intuitive Progress Steps Container -->
<div class="progress-steps-container w-full">
  <!-- Clear Progress Header -->
  <div class="progress-header">
    <div class="header-content">
      <h2 class="flow-title">{{ flowTitle }}</h2>
      <div class="step-counter">
        <span class="current-step">{{ currentStepIndex() + 1 }}</span>
        <span class="separator">of</span>
        <span class="total-steps">{{ stepDefinitions.length }}</span>
      </div>
    </div>
  
  </div>

    <!-- Steps Navigation -->
  <div class="steps-navigation">
  <!-- Intuitive Step Navigation -->
  <div class="steps-navigation">
    <!-- Desktop & Tablet View -->
    <div class="hidden md:block">
      <!-- Connection Line -->
      <div class="step-connector"
           [class.connector-completed]="progressPercentage() > 0">
      </div>

      <!-- Steps Container -->
      <div class="steps-container">
        <div
          *ngFor="let step of steps(); let i = index"
          class="step-item"
          [class.step-disabled]="!step.isAccessible"
          [class.cursor-pointer]="step.isAccessible && !step.isActive"
          (click)="navigateToStep(step)"
          [pTooltip]="getStepTooltip(step)"
          tooltipPosition="top"
          tabindex="0"
          role="button"
          [attr.aria-label]="getStepTooltip(step)">

          <!-- Step Indicator -->
          <div
            [class]="getStepIndicatorClasses(step)"
            class="step-indicator">
            <i [class]="step.icon" *ngIf="!step.isCompleted"></i>
            <i class="pi pi-check" *ngIf="step.isCompleted"></i>
          </div>

          <!-- Step Label -->
          <div
            [class]="getStepTitleClasses(step)"
            class="step-label">
            {{ step.shortTitle }}
          </div>
        </div>
      </div>
    </div>

    <!-- Mobile View -->
    <div class="block md:hidden mobile-steps">
      <!-- Current Step Card -->
      <div class="current-step-card" *ngIf="activeStep()">
        <div class="step-info">
          <div
            [class]="getStepIndicatorClasses(activeStep()!)"
            class="mobile-step-indicator">
            <i [class]="activeStep()?.icon" *ngIf="!activeStep()?.isCompleted"></i>
            <i class="pi pi-check" *ngIf="activeStep()?.isCompleted"></i>
          </div>

          <div class="step-details">
            <h3 class="step-title">{{ activeStep()?.title }}</h3>
            <p class="step-description">{{ activeStep()?.description }}</p>
          </div>

          <div class="step-counter">
            {{ currentStepIndex() + 1 }}/{{ steps().length }}
          </div>
        </div>
      </div>

      <!-- Progress Dots -->
      <div class="progress-dots">
        <div
          *ngFor="let step of steps()"
          class="progress-dot"
          [class.dot-active]="step.isActive"
          [class.dot-completed]="step.isCompleted">
        </div>
      </div>
    </div>
  </div>

  <!-- Current Step Summary (Optional) -->
  <!-- <div class="current-step-summary animate-fade-in" *ngIf="activeStep()">
    <div class="summary-content">
      <div class="summary-icon">
        <i [class]="activeStep()?.icon"></i>
      </div>
      <div class="summary-text">
        <h4 class="summary-title">{{ activeStep()?.title }}</h4>
        <p class="summary-description">{{ activeStep()?.description }}</p>
      </div>
    </div>
  </div> -->
</div>

  <p class="progress-subtitle" *ngIf="activeStep()">
      {{ activeStep()?.description }}
    </p>
  <!-- Subtle Progress Track -->


  <!-- <div class="progress-track-container">
    <div class="progress-track">
      <div
        class="progress-fill"
        [style.width.%]="progressPercentage()">
      </div>
    </div>
    <div class="progress-percentage">
      {{ progressPercentage() }}%
    </div>
  </div> -->

