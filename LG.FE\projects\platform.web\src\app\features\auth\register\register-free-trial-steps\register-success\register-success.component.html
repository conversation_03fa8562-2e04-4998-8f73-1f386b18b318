<div class="flex flex-column align-items-center justify-content-center w-full h-full md:px-2">
  <div class="flex flex-column align-items-center justify-content-center w-full h-full primary-purple-color">
    <!-- <img alt="successfully registered" class="w-5rem my-2" src="assets/images/graphic/check.svg" /> -->
    <div class="mt-0 text-center">
      <!-- <h3 class="font-semibold mb-3 text-center m-0">Student registration successful!
      </h3> -->

      <app-galactic-success-card [config]="galacticConfig()" [size]="'medium'" [showOrbitalRing]="true">

        <div slot="primary-actions" class="flex gap-3 justify-content-center flex-wrap">

          

  <div class="w-full">
    <app-available-students-for-groups-list [studentId]="studentId()!"
      [teachingLanguageId]="teachingLanguageId()!"></app-available-students-for-groups-list>
  </div>
  
          <div class="w-full">

      <p class="text-800 text-sm mb-3"> Your student has been successfully registered. You can now access the student
        details to manage their progress.</p>
            <h3 class="font-semibold mb-3 text-center m-0">Would you like to register another student?</h3>
            <div class="flex mt-2 gap-2 align-items-center justify-content-center">

              <p-button (click)="addAnotherStudent()" [rounded]="true" class=" " icon="pi pi-plus" iconPos="left"
                label="Add another Student" styleClass="cyan-btn"></p-button>
              <p-button [rounded]="true" [routerLink]="['/dashboard/parent/students']" class=""
                icon="pi pi-arrow-up-right" iconPos="left" label="Go to Students" styleClass="submit-btn"></p-button>
            </div>
          </div>
        </div>
      </app-galactic-success-card>

    </div>
  </div>


</div>