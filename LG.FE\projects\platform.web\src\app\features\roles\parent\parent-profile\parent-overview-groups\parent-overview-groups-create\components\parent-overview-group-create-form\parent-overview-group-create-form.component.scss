:host {
  display: block;
  width: 100%;
}

// Group Requirements Section Styling
.group-requirements-section {
  .requirements-card {
    transition: all 0.3s ease;

    .requirement-item {
      transition: all 0.2s ease;

      i {
        font-size: 0.875rem;
        transition: all 0.2s ease;
      }

      span {
        transition: all 0.2s ease;
      }
    }

    .requirement-status {
      .flex {
        transition: all 0.2s ease;
      }
    }
  }
}

// Validation and UI improvements
.validation-error-message {
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// Smart Student Selector Integration
.smart-student-selector-container {
  margin: 1rem 0;
}

// Language Level Badges Styling
.language-level-badges {
  display: flex;
  gap: 0.25rem;
  flex-wrap: wrap;

  .level-badge {
    display: inline-block;
    padding: 0.125rem 0.375rem;
    border-radius: 4px;
    font-size: 0.625rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    border: 1px solid transparent;

    // Level-specific colors
    &[data-level="0"] { // A1
      background: #fef3c7;
      color: #92400e;
      border-color: #fbbf24;
    }

    &[data-level="1"] { // A2
      background: #fed7aa;
      color: #9a3412;
      border-color: #fb923c;
    }

    &[data-level="2"] { // B1
      background: #dbeafe;
      color: #1e40af;
      border-color: #60a5fa;
    }

    &[data-level="3"] { // B2
      background: #bfdbfe;
      color: #1e3a8a;
      border-color: #3b82f6;
    }

    &[data-level="4"] { // C1
      background: #f3e8ff;
      color: #6b21a8;
      border-color: #a855f7;
    }

    &[data-level="5"] { // C2
      background: #ede9fe;
      color: #581c87;
      border-color: #8b5cf6;
    }

    &.na {
      background: #f3f4f6;
      color: #6b7280;
      border-color: #d1d5db;
    }
  }
}

  .field-description {
    color: #6b7280;
    font-size: 0.875rem;
    line-height: 1.5;
    margin-bottom: 1rem;
  }