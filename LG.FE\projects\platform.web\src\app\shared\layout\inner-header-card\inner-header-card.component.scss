@use "mixins";

:host {
  display: block;
}

// Mobile app-style back button
.back-btn {
  min-width: 2.5rem !important;
  width: 2.5rem !important;
  height: 2.5rem !important;
  color: var(--primary-color) !important;
  transition: all 0.2s ease-in-out;

  &:hover {
    background-color: var(--primary-color-text) !important;
    transform: scale(1.05);
  }

  &:active {
    transform: scale(0.98);
  }

  // Mobile-specific styling
  @include mixins.breakpoint(mobile) {
    min-width: 2.25rem !important;
    width: 2.25rem !important;
    height: 2.25rem !important;
    margin-left: -0.25rem; // Align closer to edge on mobile
  }

  // Ensure proper icon sizing
  .pi {
    font-size: 1.1rem;
    font-weight: 600;
  }
}
