<div [class]="getContainerClasses()">
  <!-- Cosmic Success Icon -->
  <div class="success-icon-wrapper">
    <div class="success-icon-background">
      <i [class]="getIconClass()" class="success-icon"></i>
    </div>
    
    <!-- Orbital Ring (optional) -->
    <div *ngIf="showOrbitalRing" class="orbital-ring"></div>
  </div>

  <!-- Galactic Journey Content -->
  <div class="success-content">
    <h2 class="galactic-title">{{ config.title }}</h2>
    <p class="journey-subtitle">{{ config.subtitle }}</p>
  </div>

  <!-- Galactic Trial Info -->
  <div class="trial-info-galactic">
    <div class="stellar-badge">
      <i [class]="getBadgeIcon()" class="stellar-icon"></i>
      <span>{{ config.badgeText }}</span>
    </div>
    
    <div *ngIf="config.showDestinationName && config.destinationName" class="destination-name">
      {{ config.destinationName }}
    </div>
  </div>

  <!-- Mission Equipment -->
  <div *ngIf="config.equipmentItems?.length" class="mission-equipment">
    <div *ngFor="let item of config.equipmentItems" class="equipment-item">
      {{ item }}
    </div>
  </div>

  <!-- Content Projection for Custom Actions -->
  <div class="mission-control-center">
    <ng-content select="[slot=primary-actions]"></ng-content>
    
    <!-- Cosmic Divider -->
    <div class="cosmic-divider">
      <div class="divider-line"></div>
      <div class="cosmic-orb">
        <i class="pi pi-circle-fill"></i>
      </div>
      <div class="divider-line"></div>
    </div>
    
    <ng-content select="[slot=secondary-actions]"></ng-content>
  </div>

  <!-- Additional Content Projection -->
  <ng-content select="[slot=additional-content]"></ng-content>
</div>
