import { ComponentFixture, TestBed } from '@angular/core/testing';
import { CommonModule } from '@angular/common';
import { GalacticSuccessCardComponent, type GalacticSuccessConfig } from './galactic-success-card.component';

describe('GalacticSuccessCardComponent', () => {
  let component: GalacticSuccessCardComponent;
  let fixture: ComponentFixture<GalacticSuccessCardComponent>;

  const mockConfig: GalacticSuccessConfig = {
    title: 'Test Title',
    subtitle: 'Test Subtitle',
    badgeText: 'TEST',
    equipmentItems: ['Item 1', 'Item 2'],
    theme: 'success'
  };

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [CommonModule, GalacticSuccessCardComponent]
    }).compileComponents();

    fixture = TestBed.createComponent(GalacticSuccessCardComponent);
    component = fixture.componentInstance;
    component.config = mockConfig;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should display the title and subtitle', () => {
    const compiled = fixture.nativeElement as HTMLElement;
    expect(compiled.querySelector('.galactic-title')?.textContent).toContain('Test Title');
    expect(compiled.querySelector('.journey-subtitle')?.textContent).toContain('Test Subtitle');
  });

  it('should display badge text', () => {
    const compiled = fixture.nativeElement as HTMLElement;
    expect(compiled.querySelector('.stellar-badge span')?.textContent).toContain('TEST');
  });

  it('should display equipment items', () => {
    const compiled = fixture.nativeElement as HTMLElement;
    const equipmentItems = compiled.querySelectorAll('.equipment-item');
    expect(equipmentItems.length).toBe(2);
    expect(equipmentItems[0].textContent).toContain('Item 1');
    expect(equipmentItems[1].textContent).toContain('Item 2');
  });

  it('should apply correct theme classes', () => {
    const compiled = fixture.nativeElement as HTMLElement;
    const container = compiled.querySelector('.galactic-success-container');
    expect(container?.classList).toContain('theme-success');
  });

  it('should apply correct size classes', () => {
    component.size = 'large';
    fixture.detectChanges();
    
    const compiled = fixture.nativeElement as HTMLElement;
    const container = compiled.querySelector('.galactic-success-container');
    expect(container?.classList).toContain('size-large');
  });

  it('should show orbital ring when enabled', () => {
    component.showOrbitalRing = true;
    fixture.detectChanges();
    
    const compiled = fixture.nativeElement as HTMLElement;
    const orbitalRing = compiled.querySelector('.orbital-ring');
    expect(orbitalRing).toBeTruthy();
  });

  it('should hide orbital ring when disabled', () => {
    component.showOrbitalRing = false;
    fixture.detectChanges();
    
    const compiled = fixture.nativeElement as HTMLElement;
    const orbitalRing = compiled.querySelector('.orbital-ring');
    expect(orbitalRing).toBeFalsy();
  });

  it('should show destination name when configured', () => {
    component.config = {
      ...mockConfig,
      showDestinationName: true,
      destinationName: 'Spanish'
    };
    fixture.detectChanges();
    
    const compiled = fixture.nativeElement as HTMLElement;
    const destinationName = compiled.querySelector('.destination-name');
    expect(destinationName?.textContent).toContain('Spanish');
  });

  it('should get correct icon class for different themes', () => {
    component.config = { ...mockConfig, theme: 'success' };
    expect(component.getIconClass()).toBe('pi pi-check');

    component.config = { ...mockConfig, theme: 'primary' };
    expect(component.getIconClass()).toBe('pi pi-star-fill');

    component.config = { ...mockConfig, theme: 'warning' };
    expect(component.getIconClass()).toBe('pi pi-exclamation-triangle');

    component.config = { ...mockConfig, theme: 'info' };
    expect(component.getIconClass()).toBe('pi pi-info-circle');
  });

  it('should use custom icon class when provided', () => {
    component.config = { ...mockConfig, customIconClass: 'pi pi-custom' };
    expect(component.getIconClass()).toBe('pi pi-custom');
  });

  it('should apply custom classes', () => {
    component.customClasses = 'custom-class-1 custom-class-2';
    fixture.detectChanges();
    
    const compiled = fixture.nativeElement as HTMLElement;
    const container = compiled.querySelector('.galactic-success-container');
    expect(container?.classList).toContain('custom-class-1');
    expect(container?.classList).toContain('custom-class-2');
  });

  it('should apply animation classes when enabled', () => {
    component.enableAnimations = true;
    fixture.detectChanges();
    
    const compiled = fixture.nativeElement as HTMLElement;
    const container = compiled.querySelector('.galactic-success-container');
    expect(container?.classList).toContain('animations-enabled');
  });

  it('should apply no-animation classes when disabled', () => {
    component.enableAnimations = false;
    fixture.detectChanges();
    
    const compiled = fixture.nativeElement as HTMLElement;
    const container = compiled.querySelector('.galactic-success-container');
    expect(container?.classList).toContain('animations-disabled');
  });
});
