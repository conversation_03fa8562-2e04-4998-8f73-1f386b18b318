<div class="surface-card shadow-0">
    <div class="py-3 px-3">
        <div class="flex flex-row align-items-center justify-content-between">
            <!-- Mobile app-style back button on the left -->
            <div class="flex flex-row align-items-center gap-3">
                @if (isBackButton) {
                <p-button
                    (click)="onActionButtonClick()"
                    icon="pi pi-arrow-left"
                    styleClass="back-btn p-button-text p-button-rounded p-button-sm"
                    [text]="true"
                    [rounded]="true">
                </p-button>
                }
                <div class="flex flex-column font-semibold text-lg md:text-xl primary-purple-color">
                    <ng-content select="[breadcrumb-label]"></ng-content>
                    <app-breadcrumbs [breadcrumbs]="breadcrumbs"></app-breadcrumbs>
                </div>
            </div>

            <!-- Action buttons on the right -->
            <div class="md:mt-0">
                @if (actionButtonLabel && canShowActionButton && !isBackButton) {
                <p-button (click)="onActionButtonClick()" [label]="actionButtonLabel"
                    styleClass="{{actionButtonClass}} w-full" [rounded]="true" [icon]="actionButtonIcon" iconPos="left">
                </p-button>
                <!-- <ng-content select="[action-button]"></ng-content> -->
                }
                <ng-content select="[action-content]"></ng-content>
            </div>
        </div>
    </div>
</div>